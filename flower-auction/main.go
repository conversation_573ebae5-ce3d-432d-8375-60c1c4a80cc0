package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/api"
	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/service"
	"github.com/putonghao/flower-auction/internal/websocket"
	"github.com/spf13/viper"
)

// 版本信息，在构建时通过ldflags注入
var (
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	// 打印版本信息
	log.Printf("昆明花卉拍卖系统启动中...")
	log.Printf("版本: %s, 构建时间: %s, Git提交: %s", Version, BuildTime, GitCommit)

	// 初始化配置
	if err := initConfig(); err != nil {
		log.Fatalf("初始化配置失败: %v", err)
	}

	// 初始化数据库
	if err := initDatabase(); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	log.Println("数据库初始化成功")

	// 初始化路由
	r := initRouter()

	// 配置服务器
	port := viper.GetString("server.port")
	if port == "" {
		port = "8080"
	}

	srv := &http.Server{
		Addr:           ":" + port,
		Handler:        r,
		ReadTimeout:    30 * time.Second,
		WriteTimeout:   30 * time.Second,
		IdleTimeout:    60 * time.Second,
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	// 在goroutine中启动服务器
	go func() {
		log.Printf("服务器启动在端口: %s", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 优雅退出
	gracefulShutdown(srv)
}

// gracefulShutdown 优雅退出处理
func gracefulShutdown(srv *http.Server) {
	// 创建一个接收系统信号的通道
	quit := make(chan os.Signal, 1)

	// 监听指定的系统信号
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

	// 阻塞等待信号
	sig := <-quit
	log.Printf("接收到信号: %v, 开始优雅退出...", sig)

	// 设置超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("服务器强制关闭: %v", err)
		return
	}

	// 这里可以添加其他资源的清理工作
	// 例如：关闭数据库连接、清理缓存等
	cleanup()

	log.Println("服务器已优雅退出")
}

// cleanup 清理资源
func cleanup() {
	log.Println("正在清理资源...")

	// 清理数据库连接
	if err := dao.CloseDB(); err != nil {
		log.Printf("关闭数据库连接失败: %v", err)
	}

	// 这里可以添加其他清理工作
	// 例如：关闭Redis连接、关闭消息队列连接等

	log.Println("资源清理完成")
}

// initConfig 初始化配置
func initConfig() error {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	viper.SetDefault("server.port", "8081")
	viper.SetDefault("server.mode", "debug")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件未找到，使用默认值
			log.Println("配置文件未找到，使用默认配置")
		} else {
			return err
		}
	}

	return nil
}

// initDatabase 初始化数据库
func initDatabase() error {
	config := &dao.Config{
		UserDB: dao.DBConfig{
			Host:         viper.GetString("userDB.host"),
			Port:         viper.GetInt("userDB.port"),
			User:         viper.GetString("userDB.user"),
			Password:     viper.GetString("userDB.password"),
			Database:     viper.GetString("userDB.database"),
			MaxIdleConns: viper.GetInt("userDB.maxIdleConns"),
			MaxOpenConns: viper.GetInt("userDB.maxOpenConns"),
		},
		ProductDB: dao.DBConfig{
			Host:         viper.GetString("productDB.host"),
			Port:         viper.GetInt("productDB.port"),
			User:         viper.GetString("productDB.user"),
			Password:     viper.GetString("productDB.password"),
			Database:     viper.GetString("productDB.database"),
			MaxIdleConns: viper.GetInt("productDB.maxIdleConns"),
			MaxOpenConns: viper.GetInt("productDB.maxOpenConns"),
		},
		AuctionDB: dao.DBConfig{
			Host:         viper.GetString("auctionDB.host"),
			Port:         viper.GetInt("auctionDB.port"),
			User:         viper.GetString("auctionDB.user"),
			Password:     viper.GetString("auctionDB.password"),
			Database:     viper.GetString("auctionDB.database"),
			MaxIdleConns: viper.GetInt("auctionDB.maxIdleConns"),
			MaxOpenConns: viper.GetInt("auctionDB.maxOpenConns"),
		},
		OrderDB: dao.DBConfig{
			Host:         viper.GetString("orderDB.host"),
			Port:         viper.GetInt("orderDB.port"),
			User:         viper.GetString("orderDB.user"),
			Password:     viper.GetString("orderDB.password"),
			Database:     viper.GetString("orderDB.database"),
			MaxIdleConns: viper.GetInt("orderDB.maxIdleConns"),
			MaxOpenConns: viper.GetInt("orderDB.maxOpenConns"),
		},
	}

	return dao.InitDB(config)
}

// initRouter 初始化路由
func initRouter() *gin.Engine {
	// 设置运行模式
	mode := viper.GetString("server.mode")
	if mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "昆明花卉拍卖系统运行正常",
		})
	})

	// 注册API路由
	userHandler := api.NewUserHandler()
	userHandler.RegisterRoutes(r)

	// 添加认证路由别名，使前端可以使用 /api/v1/auth/* 路径
	auth := r.Group("/api/v1/auth")
	{
		auth.POST("/login", userHandler.Login)
		auth.POST("/logout", func(c *gin.Context) {
			c.JSON(200, gin.H{"success": true, "message": "登出成功"})
		})
		auth.GET("/me", func(c *gin.Context) {
			// 简单的用户信息返回，实际应该从token中解析
			c.JSON(200, gin.H{
				"success": true,
				"data": gin.H{
					"id":       1,
					"username": "admin",
					"realName": "系统管理员",
					"userType": 3,
					"status":   1,
				},
			})
		})
		auth.POST("/refresh", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"success": true,
				"data": gin.H{
					"token":        "new-token-123",
					"refreshToken": "new-refresh-token-123",
				},
			})
		})
	}

	productHandler := api.NewProductHandler()
	productHandler.RegisterRoutes(r)

	qualityHandler := api.NewQualityHandler()
	qualityHandler.RegisterRoutes(r)

	auctionHandler := api.NewAuctionHandler()
	auctionHandler.RegisterRoutes(r)

	// 初始化角色相关的DAO和Service
	userDB := dao.GetUserDB()
	roleDAO := dao.NewRoleDAO(userDB)
	userDAO := dao.NewUserDAO()
	roleService := service.NewRoleService(roleDAO, userDAO)

	permissionHandler := api.NewPermissionHandler(roleService)
	permissionHandler.RegisterRoutes(r)

	userEnhancedHandler := api.NewUserEnhancedHandler()
	userEnhancedHandler.RegisterRoutes(r)

	uploadHandler := api.NewUploadHandler()
	uploadHandler.RegisterRoutes(r)

	productUploadHandler := api.NewProductUploadHandler()
	productUploadHandler.RegisterProductUploadRoutes(r)

	orderHandler := api.NewOrderHandler()
	orderHandler.RegisterRoutes(r)

	paymentHandler := api.NewPaymentHandler()
	paymentHandler.RegisterRoutes(r)

	financeHandler := api.NewFinanceHandler()
	financeHandler.RegisterRoutes(r)

	shippingHandler := api.NewShippingHandler()
	shippingHandler.RegisterRoutes(r)

	return r
}
