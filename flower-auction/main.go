package main

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	websocketHandler "github.com/putonghao/flower-auction/internal/websocket"
)

func main() {
	log.Println("昆明花卉拍卖系统 WebSocket 服务启动中...")

	// 创建Gin引擎
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>SO<PERSON>(200, gin.H{
			"status":  "ok",
			"message": "昆明花卉拍卖系统 WebSocket 服务运行正常",
			"time":    time.Now(),
		})
	})

	// WebSocket处理器
	wsHandler := websocketHandler.NewHandler()
	wsHandler.RegisterRoutes(r)

	// 模拟拍卖API
	api := r.Group("/api/v1")
	{
		// 拍卖相关API
		auctions := api.Group("/auctions")
		{
			auctions.GET("/", func(c *gin.Context) {
				c.JSON(200, gin.H{
					
					"success": true,
					"data": []gin.H{
						{
							"id":           "auction-1",
							"title":        "精品红玫瑰专场",
							"status":       "active",
							"currentPrice": 100.0,
							"startPrice":   50.0,
							"timeRemaining": 3600,
						},
						{
							"id":           "auction-2",
							"title":        "白玫瑰花束拍卖",
							"status":       "waiting",
							"currentPrice": 80.0,
							"startPrice":   40.0,
							"timeRemaining": 7200,
						},
					},
				})
			})

			auctions.GET("/:id", func(c *gin.Context) {
				auctionID := c.Param("id")
				c.JSON(200, gin.H{
					"success": true,
					"data": gin.H{
						"id":               auctionID,
						"title":            "精品红玫瑰专场",
						"description":      "来自云南的精品红玫瑰，品质优良",
						"status":           "active",
						"currentPrice":     100.0,
						"startPrice":       50.0,
						"reservePrice":     200.0,
						"timeRemaining":    3600,
						"bidCount":         15,
						"participantCount": 8,
						"images": []string{
							"https://example.com/image1.jpg",
							"https://example.com/image2.jpg",
						},
						"seller": gin.H{
							"id":   "seller-1",
							"name": "云南花卉种植基地",
						},
						"product": gin.H{
							"id":       "product-1",
							"name":     "精品红玫瑰",
							"category": "玫瑰",
							"grade":    "A级",
							"quantity": 100,
							"unit":     "支",
						},
					},
				})
			})

			auctions.POST("/:id/start", func(c *gin.Context) {
				auctionID := c.Param("id")
				// 通过WebSocket广播拍卖开始
				wsHandler.GetHub().BroadcastToAuction(auctionID, "auction_started", gin.H{
					"auctionId": auctionID,
					"startTime": time.Now().Format(time.RFC3339),
					"status":    "active",
				})
				
				c.JSON(200, gin.H{
					"success": true,
					"message": "拍卖已开始",
				})
			})

			auctions.POST("/:id/end", func(c *gin.Context) {
				auctionID := c.Param("id")
				// 通过WebSocket广播拍卖结束
				wsHandler.GetHub().BroadcastToAuction(auctionID, "auction_ended", gin.H{
					"auctionId": auctionID,
					"endTime":   time.Now().Format(time.RFC3339),
					"status":    "ended",
				})
				
				c.JSON(200, gin.H{
					"success": true,
					"message": "拍卖已结束",
				})
			})

			auctions.POST("/:id/bid", func(c *gin.Context) {
				auctionID := c.Param("id")
				var bidData struct {
					Amount float64 `json:"amount" binding:"required"`
					UserID string  `json:"userId" binding:"required"`
				}

				if err := c.ShouldBindJSON(&bidData); err != nil {
					c.JSON(400, gin.H{
						"success": false,
						"message": "请求参数无效",
						"error":   err.Error(),
					})
					return
				}

				// 通过WebSocket广播新出价
				wsHandler.GetHub().BroadcastToAuction(auctionID, "bid_placed", gin.H{
					"auctionId":  auctionID,
					"bidderId":   bidData.UserID,
					"bidderName": "用户" + bidData.UserID,
					"bidAmount":  bidData.Amount,
					"bidTime":    time.Now().Format(time.RFC3339),
					"isWinning":  true,
				})

				// 更新价格
				wsHandler.GetHub().BroadcastToAuction(auctionID, "price_updated", gin.H{
					"auctionId":    auctionID,
					"currentPrice": bidData.Amount,
					"lastBidder": gin.H{
						"id":        bidData.UserID,
						"name":      "用户" + bidData.UserID,
						"bidAmount": bidData.Amount,
						"bidTime":   time.Now().Format(time.RFC3339),
					},
				})

				c.JSON(200, gin.H{
					"success": true,
					"message": "出价成功",
					"data": gin.H{
						"auctionId": auctionID,
						"bidAmount": bidData.Amount,
						"bidTime":   time.Now().Format(time.RFC3339),
					},
				})
			})
		}
	}

	// 配置服务器
	port := "8081"
	srv := &http.Server{
		Addr:           ":" + port,
		Handler:        r,
		ReadTimeout:    30 * time.Second,
		WriteTimeout:   30 * time.Second,
		IdleTimeout:    60 * time.Second,
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	log.Printf("服务器启动在端口: %s", port)
	log.Printf("WebSocket 端点: ws://localhost:%s/ws/auction", port)
	log.Printf("健康检查: http://localhost:%s/health", port)
	
	if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
