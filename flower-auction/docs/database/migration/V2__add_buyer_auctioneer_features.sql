-- 花卉拍卖系统扩展功能数据库脚本
-- Version: V2
-- Description: 添加购买商端和拍卖师端专用功能

USE flower_auction;

-- 初始化钟号状态数据（1-12号钟）
INSERT IGNORE INTO `clock_status` (`clock_number`, `status`) VALUES
(1, 0), (2, 0), (3, 0), (4, 0), (5, 0), (6, 0),
(7, 0), (8, 0), (9, 0), (10, 0), (11, 0), (12, 0);

-- 创建商品分类初始数据
INSERT IGNORE INTO `category` (`id`, `name`, `parent_id`, `level`, `sort_order`) VALUES
(1, '鲜切花', 0, 1, 1),
(2, '玫瑰', 1, 2, 1),
(3, '康乃馨', 1, 2, 2),
(4, '百合', 1, 2, 3),
(5, '菊花', 1, 2, 4),
(6, '郁金香', 1, 2, 5),
(7, '满天星', 1, 2, 6),
(8, '绿植', 0, 1, 2),
(9, '多肉植物', 8, 2, 1),
(10, '观叶植物', 8, 2, 2);

-- 创建示例商品数据
INSERT IGNORE INTO `product` (`id`, `name`, `category_id`, `description`, `unit`, `origin`, `variety`, `color`) VALUES
(1, '红玫瑰', 2, '优质红玫瑰，花朵饱满，颜色鲜艳', '支', '昆明', '卡罗拉', '红色'),
(2, '白玫瑰', 2, '纯白玫瑰，花型优美，适合婚庆', '支', '昆明', '雪山', '白色'),
(3, '粉玫瑰', 2, '粉色玫瑰，浪漫温馨', '支', '昆明', '戴安娜', '粉色'),
(4, '红康乃馨', 3, '经典红色康乃馨，母亲节首选', '支', '昆明', '标准型', '红色'),
(5, '白百合', 4, '纯洁白百合，香气怡人', '支', '昆明', '东方百合', '白色'),
(6, '黄菊花', 5, '金黄色菊花，寓意吉祥', '支', '昆明', '大菊', '黄色'),
(7, '红郁金香', 6, '荷兰进口郁金香品种', '支', '荷兰', '达尔文', '红色'),
(8, '满天星', 7, '白色满天星，配花首选', '扎', '昆明', '标准型', '白色');

-- 创建示例拍卖会
INSERT IGNORE INTO `auction` (`id`, `name`, `description`, `start_time`, `status`, `total_items`) VALUES
(1, '2024年12月花卉拍卖会', '年末大型花卉拍卖会，品种丰富，质量上乘', '2024-12-01 09:00:00', 1, 50);

-- 创建示例拍卖商品
INSERT IGNORE INTO `auction_item` (`id`, `auction_id`, `product_id`, `batch_number`, `quantity`, `grade`, `start_price`, `step_price`, `current_price`, `clock_number`, `status`) VALUES
(1, 1, 1, 'B20241201001', 1000, 'A', 8.00, 0.50, 8.00, 1, 0),
(2, 1, 2, 'B20241201002', 800, 'A', 10.00, 0.50, 10.00, 2, 0),
(3, 1, 3, 'B20241201003', 1200, 'A', 9.00, 0.50, 9.00, 3, 0),
(4, 1, 4, 'B20241201004', 1500, 'A', 6.00, 0.30, 6.00, 4, 0),
(5, 1, 5, 'B20241201005', 600, 'A', 15.00, 1.00, 15.00, 5, 0),
(6, 1, 6, 'B20241201006', 2000, 'A', 4.00, 0.20, 4.00, 6, 0),
(7, 1, 7, 'B20241201007', 500, 'A', 12.00, 0.50, 12.00, 7, 0),
(8, 1, 8, 'B20241201008', 300, 'A', 8.00, 0.50, 8.00, 8, 0),
(9, 1, 1, 'B20241201009', 800, 'B', 7.00, 0.50, 7.00, 9, 0),
(10, 1, 2, 'B20241201010', 600, 'B', 9.00, 0.50, 9.00, 10, 0);

-- 创建测试用户
INSERT IGNORE INTO `user` (`id`, `username`, `password`, `phone`, `real_name`, `user_type`, `company_name`) VALUES
(1, 'auctioneer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001001', '张拍卖师', 1, '昆明花卉拍卖中心'),
(2, 'buyer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '***********', '李购买商', 2, '云南花卉贸易公司'),
(3, 'buyer02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '***********', '王购买商', 2, '昆明花卉批发市场'),
(4, 'admin01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '***********', '管理员', 3, '系统管理');

-- 为测试用户创建账户
INSERT IGNORE INTO `account` (`user_id`, `account_type`, `balance`, `frozen_amount`) VALUES
(1, 1, 0.00, 0.00),
(2, 1, 50000.00, 5000.00),
(3, 1, 30000.00, 2000.00),
(4, 1, 0.00, 0.00);

-- 创建一些示例竞价记录
INSERT IGNORE INTO `bid` (`auction_item_id`, `user_id`, `price`, `bid_time`) VALUES
(1, 2, 8.50, '2024-12-01 10:15:00'),
(1, 3, 9.00, '2024-12-01 10:16:00'),
(1, 2, 9.50, '2024-12-01 10:17:00'),
(2, 2, 10.50, '2024-12-01 10:20:00'),
(2, 3, 11.00, '2024-12-01 10:21:00'),
(3, 3, 9.50, '2024-12-01 10:25:00');

-- 创建一些关注记录
INSERT IGNORE INTO `watch_list` (`user_id`, `auction_item_id`) VALUES
(2, 1), (2, 2), (2, 5),
(3, 1), (3, 3), (3, 4), (3, 6);

-- 创建一些埋单记录
INSERT IGNORE INTO `pre_order` (`user_id`, `auction_item_id`, `price`, `quantity`) VALUES
(2, 1, 10.00, 500),
(3, 2, 12.00, 300),
(2, 5, 18.00, 200);

-- 创建一些交易记录
INSERT IGNORE INTO `transaction` (`user_id`, `account_id`, `type`, `amount`, `balance_before`, `balance_after`, `description`) VALUES
(2, 2, 1, 50000.00, 0.00, 50000.00, '账户充值'),
(3, 3, 1, 30000.00, 0.00, 30000.00, '账户充值'),
(2, 2, 3, 5000.00, 50000.00, 45000.00, '竞价保证金冻结'),
(3, 3, 3, 2000.00, 30000.00, 28000.00, '竞价保证金冻结');

-- 添加索引优化
ALTER TABLE `bid` ADD INDEX `idx_price` (`price`);
ALTER TABLE `auction_item` ADD INDEX `idx_start_time` (`start_time`);
ALTER TABLE `auction_item` ADD INDEX `idx_end_time` (`end_time`);
ALTER TABLE `transaction` ADD INDEX `idx_type_created` (`type`, `created_at`);
ALTER TABLE `watch_list` ADD INDEX `idx_created_at` (`created_at`);

-- 创建视图：活跃拍卖商品
CREATE OR REPLACE VIEW `v_active_auction_items` AS
SELECT 
    ai.id,
    ai.batch_number,
    ai.quantity,
    ai.unit,
    ai.grade,
    ai.start_price,
    ai.current_price,
    ai.clock_number,
    ai.status,
    ai.start_time,
    ai.bid_count,
    p.name as product_name,
    p.origin,
    p.variety,
    p.color,
    c.name as category_name,
    a.name as auction_name
FROM auction_item ai
LEFT JOIN product p ON ai.product_id = p.id
LEFT JOIN category c ON p.category_id = c.id
LEFT JOIN auction a ON ai.auction_id = a.id
WHERE ai.status IN (0, 1, 4); -- 待拍、拍卖中、暂停

-- 创建视图：用户竞价统计
CREATE OR REPLACE VIEW `v_user_bid_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    u.real_name,
    COUNT(b.id) as total_bids,
    COUNT(DISTINCT b.auction_item_id) as items_bid,
    MAX(b.price) as max_bid_price,
    AVG(b.price) as avg_bid_price,
    SUM(CASE WHEN b.is_winning = 1 THEN 1 ELSE 0 END) as winning_bids
FROM user u
LEFT JOIN bid b ON u.id = b.user_id
WHERE u.user_type = 2 -- 购买商
GROUP BY u.id, u.username, u.real_name;

-- 创建视图：钟号状态详情
CREATE OR REPLACE VIEW `v_clock_status_detail` AS
SELECT 
    cs.clock_number,
    cs.status,
    cs.updated_at,
    ai.id as current_item_id,
    ai.batch_number,
    ai.current_price,
    ai.bid_count,
    p.name as product_name,
    u.real_name as auctioneer_name
FROM clock_status cs
LEFT JOIN auction_item ai ON cs.current_item_id = ai.id
LEFT JOIN product p ON ai.product_id = p.id
LEFT JOIN user u ON cs.auctioneer_id = u.id;

-- 创建存储过程：更新拍卖商品当前价格
DELIMITER //
CREATE PROCEDURE UpdateAuctionItemPrice(
    IN p_item_id BIGINT,
    IN p_new_price DECIMAL(10,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新拍卖商品价格
    UPDATE auction_item 
    SET current_price = p_new_price,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_item_id;
    
    -- 增加竞价次数
    UPDATE auction_item 
    SET bid_count = bid_count + 1
    WHERE id = p_item_id;
    
    COMMIT;
END //
DELIMITER ;

-- 创建存储过程：完成拍卖
DELIMITER //
CREATE PROCEDURE CompleteAuction(
    IN p_item_id BIGINT,
    IN p_winner_id BIGINT,
    IN p_final_price DECIMAL(10,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新拍卖商品状态
    UPDATE auction_item 
    SET status = 2,
        winner_id = p_winner_id,
        final_price = p_final_price,
        end_time = CURRENT_TIMESTAMP
    WHERE id = p_item_id;
    
    -- 更新中标竞价记录
    UPDATE bid 
    SET is_winning = 1
    WHERE auction_item_id = p_item_id 
    AND user_id = p_winner_id 
    AND price = p_final_price;
    
    -- 释放钟号
    UPDATE clock_status 
    SET status = 0, current_item_id = NULL
    WHERE current_item_id = p_item_id;
    
    COMMIT;
END //
DELIMITER ;
