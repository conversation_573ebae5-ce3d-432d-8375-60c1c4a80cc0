#!/bin/bash

# 后端启动脚本 - 固定使用8081端口
# 如果端口被占用则自动kill进程并重启

PORT=8081

echo "🚀 启动后端服务..."
echo "📍 目标端口: $PORT"

# 检查端口是否被占用
PID=$(lsof -ti:$PORT)

if [ ! -z "$PID" ]; then
    echo "⚠️  端口 $PORT 被进程 $PID 占用"
    echo "🔪 正在终止占用进程..."
    kill -9 $PID
    sleep 2
    echo "✅ 进程已终止"
fi

# 清理旧的编译文件
echo "🧹 清理旧的编译文件..."
rm -f flower-auction

# 编译项目
echo "🔨 编译Go项目..."
go build -o flower-auction .

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    exit 1
fi

echo "✅ 编译成功"

# 启动服务
echo "🎯 在端口 $PORT 启动后端服务..."
./flower-auction