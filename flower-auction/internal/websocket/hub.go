package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Hub 管理WebSocket连接
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 广播消息通道
	broadcast chan []byte

	// 注册客户端通道
	register chan *Client

	// 注销客户端通道
	unregister chan *Client

	// 按拍卖会ID分组的客户端
	auctionClients map[int64]map[*Client]bool

	// 互斥锁
	mutex sync.RWMutex
}

// Client WebSocket客户端
type Client struct {
	// WebSocket连接
	conn *websocket.Conn

	// 发送消息通道
	send chan []byte

	// 客户端关注的拍卖会ID
	auctionID int64

	// 用户ID
	userID int64

	// Hub引用
	hub *Hub
}

// Message WebSocket消息结构
type Message struct {
	Type      string      `json:"type"`
	AuctionID int64       `json:"auctionId,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// BidMessage 竞价消息
type BidMessage struct {
	ItemID      int64   `json:"itemId"`
	UserID      int64   `json:"userId"`
	Username    string  `json:"username"`
	Price       float64 `json:"price"`
	CurrentTime string  `json:"currentTime"`
}

// AuctionStatusMessage 拍卖状态消息
type AuctionStatusMessage struct {
	AuctionID int64  `json:"auctionId"`
	Status    int8   `json:"status"`
	Message   string `json:"message"`
}

// PriceUpdateMessage 价格更新消息
type PriceUpdateMessage struct {
	ItemID       int64   `json:"itemId"`
	CurrentPrice float64 `json:"currentPrice"`
	BidCount     int64   `json:"bidCount"`
	HighestBidder string `json:"highestBidder"`
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许跨域连接
		return true
	},
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:        make(map[*Client]bool),
		broadcast:      make(chan []byte),
		register:       make(chan *Client),
		unregister:     make(chan *Client),
		auctionClients: make(map[int64]map[*Client]bool),
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			
			// 按拍卖会ID分组
			if client.auctionID > 0 {
				if h.auctionClients[client.auctionID] == nil {
					h.auctionClients[client.auctionID] = make(map[*Client]bool)
				}
				h.auctionClients[client.auctionID][client] = true
			}
			h.mutex.Unlock()
			
			log.Printf("客户端连接: 用户ID=%d, 拍卖会ID=%d", client.userID, client.auctionID)

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				
				// 从拍卖会分组中移除
				if client.auctionID > 0 {
					if clients, exists := h.auctionClients[client.auctionID]; exists {
						delete(clients, client)
						if len(clients) == 0 {
							delete(h.auctionClients, client.auctionID)
						}
					}
				}
			}
			h.mutex.Unlock()
			
			log.Printf("客户端断开: 用户ID=%d, 拍卖会ID=%d", client.userID, client.auctionID)

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// BroadcastToAuction 向特定拍卖会广播消息
func (h *Hub) BroadcastToAuction(auctionID int64, message interface{}) {
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	h.mutex.RLock()
	clients, exists := h.auctionClients[auctionID]
	h.mutex.RUnlock()

	if !exists {
		return
	}

	for client := range clients {
		select {
		case client.send <- data:
		default:
			close(client.send)
			h.mutex.Lock()
			delete(h.clients, client)
			delete(clients, client)
			h.mutex.Unlock()
		}
	}
}

// GetOnlineCount 获取在线用户数
func (h *Hub) GetOnlineCount(auctionID int64) int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	if clients, exists := h.auctionClients[auctionID]; exists {
		return len(clients)
	}
	return 0
}

// HandleWebSocket 处理WebSocket连接
func (h *Hub) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 从查询参数获取用户ID和拍卖会ID
	userID := c.GetInt64("userID") // 从JWT中间件获取
	auctionIDStr := c.Query("auctionId")
	
	var auctionID int64
	if auctionIDStr != "" {
		// 这里应该解析auctionID，简化处理
		auctionID = 1 // 默认值，实际应该解析
	}

	client := &Client{
		conn:      conn,
		send:      make(chan []byte, 256),
		auctionID: auctionID,
		userID:    userID,
		hub:       h,
	}

	client.hub.register <- client

	// 启动goroutine处理读写
	go client.writePump()
	go client.readPump()
}

// readPump 读取客户端消息
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	for {
		_, _, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}
	}
}

// writePump 向客户端发送消息
func (c *Client) writePump() {
	defer c.conn.Close()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("发送消息失败: %v", err)
				return
			}
		}
	}
}
