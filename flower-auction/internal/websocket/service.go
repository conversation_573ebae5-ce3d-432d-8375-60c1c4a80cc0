package websocket

import (
	"time"
)

// WebSocketService WebSocket服务
type WebSocketService struct {
	hub *Hub
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService() *WebSocketService {
	hub := NewHub()
	go hub.Run()
	
	return &WebSocketService{
		hub: hub,
	}
}

// GetHub 获取Hub实例
func (s *WebSocketService) GetHub() *Hub {
	return s.hub
}

// BroadcastBid 广播竞价消息
func (s *WebSocketService) BroadcastBid(auctionID, itemID, userID int64, username string, price float64) {
	message := Message{
		Type:      "bid",
		AuctionID: auctionID,
		Data: BidMessage{
			ItemID:      itemID,
			UserID:      userID,
			Username:    username,
			Price:       price,
			CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
		},
		Timestamp: time.Now().Unix(),
	}
	
	s.hub.BroadcastToAuction(auctionID, message)
}

// BroadcastAuctionStatus 广播拍卖状态变化
func (s *WebSocketService) BroadcastAuctionStatus(auctionID int64, status int8, statusMessage string) {
	message := Message{
		Type:      "auction_status",
		AuctionID: auctionID,
		Data: AuctionStatusMessage{
			AuctionID: auctionID,
			Status:    status,
			Message:   statusMessage,
		},
		Timestamp: time.Now().Unix(),
	}
	
	s.hub.BroadcastToAuction(auctionID, message)
}

// BroadcastPriceUpdate 广播价格更新
func (s *WebSocketService) BroadcastPriceUpdate(auctionID, itemID int64, currentPrice float64, bidCount int64, highestBidder string) {
	message := Message{
		Type:      "price_update",
		AuctionID: auctionID,
		Data: PriceUpdateMessage{
			ItemID:        itemID,
			CurrentPrice:  currentPrice,
			BidCount:      bidCount,
			HighestBidder: highestBidder,
		},
		Timestamp: time.Now().Unix(),
	}
	
	s.hub.BroadcastToAuction(auctionID, message)
}

// GetOnlineCount 获取在线用户数
func (s *WebSocketService) GetOnlineCount(auctionID int64) int {
	return s.hub.GetOnlineCount(auctionID)
}
