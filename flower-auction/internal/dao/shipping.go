package dao

import (
	"context"
	"fmt"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// ShippingDAO 物流数据访问接口
type ShippingDAO interface {
	// 物流信息管理
	CreateShipping(ctx context.Context, shipping *model.Shipping) error
	UpdateShipping(ctx context.Context, shipping *model.Shipping) error
	GetShippingByID(ctx context.Context, id int64) (*model.Shipping, error)
	GetShippingByOrderID(ctx context.Context, orderID int64) (*model.Shipping, error)
	GetShippingByTrackingNumber(ctx context.Context, trackingNumber string) (*model.Shipping, error)
	ListShippings(ctx context.Context, params ShippingQueryParams) ([]*model.Shipping, int64, error)
	DeleteShipping(ctx context.Context, id int64) error

	// 物流跟踪管理
	CreateShippingTrack(ctx context.Context, track *model.ShippingTrack) error
	ListShippingTracks(ctx context.Context, shippingID int64) ([]*model.ShippingTrack, error)
	GetLatestTrack(ctx context.Context, shippingID int64) (*model.ShippingTrack, error)

	// 物流公司管理
	CreateShippingCompany(ctx context.Context, company *model.ShippingCompany) error
	UpdateShippingCompany(ctx context.Context, company *model.ShippingCompany) error
	GetShippingCompanyByID(ctx context.Context, id int64) (*model.ShippingCompany, error)
	ListShippingCompanies(ctx context.Context, status *int8) ([]*model.ShippingCompany, error)
	DeleteShippingCompany(ctx context.Context, id int64) error

	// 物流模板管理
	CreateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error
	UpdateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error
	GetShippingTemplateByID(ctx context.Context, id int64) (*model.ShippingTemplate, error)
	ListShippingTemplates(ctx context.Context, status *int8) ([]*model.ShippingTemplate, error)
	GetDefaultTemplate(ctx context.Context) (*model.ShippingTemplate, error)
	DeleteShippingTemplate(ctx context.Context, id int64) error
}

// ShippingQueryParams 物流查询参数
type ShippingQueryParams struct {
	OrderID           *int64                  `json:"orderId"`
	TrackingNumber    string                  `json:"trackingNumber"`
	ShippingCompanyID *int64                  `json:"shippingCompanyId"`
	Status            *model.ShippingStatus   `json:"status"`
	ReceiverName      string                  `json:"receiverName"`
	ReceiverPhone     string                  `json:"receiverPhone"`
	DateRange         []string                `json:"dateRange"` // [startDate, endDate]
	Page              int                     `json:"page"`
	PageSize          int                     `json:"pageSize"`
}

// shippingDAO 物流数据访问实现
type shippingDAO struct {
	db *gorm.DB
}

// NewShippingDAO 创建物流数据访问实例
func NewShippingDAO() ShippingDAO {
	return &shippingDAO{
		db: GetDB(),
	}
}

// CreateShipping 创建物流信息
func (d *shippingDAO) CreateShipping(ctx context.Context, shipping *model.Shipping) error {
	return d.db.WithContext(ctx).Create(shipping).Error
}

// UpdateShipping 更新物流信息
func (d *shippingDAO) UpdateShipping(ctx context.Context, shipping *model.Shipping) error {
	return d.db.WithContext(ctx).Save(shipping).Error
}

// GetShippingByID 根据ID获取物流信息
func (d *shippingDAO) GetShippingByID(ctx context.Context, id int64) (*model.Shipping, error) {
	var shipping model.Shipping
	err := d.db.WithContext(ctx).Preload("ShippingCompany").First(&shipping, id).Error
	if err != nil {
		return nil, err
	}
	return &shipping, nil
}

// GetShippingByOrderID 根据订单ID获取物流信息
func (d *shippingDAO) GetShippingByOrderID(ctx context.Context, orderID int64) (*model.Shipping, error) {
	var shipping model.Shipping
	err := d.db.WithContext(ctx).Preload("ShippingCompany").Where("order_id = ?", orderID).First(&shipping).Error
	if err != nil {
		return nil, err
	}
	return &shipping, nil
}

// GetShippingByTrackingNumber 根据快递单号获取物流信息
func (d *shippingDAO) GetShippingByTrackingNumber(ctx context.Context, trackingNumber string) (*model.Shipping, error) {
	var shipping model.Shipping
	err := d.db.WithContext(ctx).Preload("ShippingCompany").Where("tracking_number = ?", trackingNumber).First(&shipping).Error
	if err != nil {
		return nil, err
	}
	return &shipping, nil
}

// ListShippings 获取物流列表
func (d *shippingDAO) ListShippings(ctx context.Context, params ShippingQueryParams) ([]*model.Shipping, int64, error) {
	var shippings []*model.Shipping
	var total int64

	query := d.db.WithContext(ctx).Model(&model.Shipping{})

	// 添加查询条件
	if params.OrderID != nil {
		query = query.Where("order_id = ?", *params.OrderID)
	}
	if params.TrackingNumber != "" {
		query = query.Where("tracking_number LIKE ?", "%"+params.TrackingNumber+"%")
	}
	if params.ShippingCompanyID != nil {
		query = query.Where("shipping_company_id = ?", *params.ShippingCompanyID)
	}
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}
	if params.ReceiverName != "" {
		query = query.Where("receiver_name LIKE ?", "%"+params.ReceiverName+"%")
	}
	if params.ReceiverPhone != "" {
		query = query.Where("receiver_phone LIKE ?", "%"+params.ReceiverPhone+"%")
	}
	if len(params.DateRange) == 2 {
		query = query.Where("created_at BETWEEN ? AND ?", params.DateRange[0], params.DateRange[1])
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	err := query.Preload("ShippingCompany").
		Order("created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&shippings).Error

	return shippings, total, err
}

// DeleteShipping 删除物流信息
func (d *shippingDAO) DeleteShipping(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Shipping{}, id).Error
}

// CreateShippingTrack 创建物流跟踪记录
func (d *shippingDAO) CreateShippingTrack(ctx context.Context, track *model.ShippingTrack) error {
	return d.db.WithContext(ctx).Create(track).Error
}

// ListShippingTracks 获取物流跟踪记录列表
func (d *shippingDAO) ListShippingTracks(ctx context.Context, shippingID int64) ([]*model.ShippingTrack, error) {
	var tracks []*model.ShippingTrack
	err := d.db.WithContext(ctx).Where("shipping_id = ?", shippingID).Order("track_time DESC").Find(&tracks).Error
	return tracks, err
}

// GetLatestTrack 获取最新的跟踪记录
func (d *shippingDAO) GetLatestTrack(ctx context.Context, shippingID int64) (*model.ShippingTrack, error) {
	var track model.ShippingTrack
	err := d.db.WithContext(ctx).Where("shipping_id = ?", shippingID).Order("track_time DESC").First(&track).Error
	if err != nil {
		return nil, err
	}
	return &track, nil
}

// CreateShippingCompany 创建物流公司
func (d *shippingDAO) CreateShippingCompany(ctx context.Context, company *model.ShippingCompany) error {
	return d.db.WithContext(ctx).Create(company).Error
}

// UpdateShippingCompany 更新物流公司
func (d *shippingDAO) UpdateShippingCompany(ctx context.Context, company *model.ShippingCompany) error {
	return d.db.WithContext(ctx).Save(company).Error
}

// GetShippingCompanyByID 根据ID获取物流公司
func (d *shippingDAO) GetShippingCompanyByID(ctx context.Context, id int64) (*model.ShippingCompany, error) {
	var company model.ShippingCompany
	err := d.db.WithContext(ctx).First(&company, id).Error
	if err != nil {
		return nil, err
	}
	return &company, nil
}

// ListShippingCompanies 获取物流公司列表
func (d *shippingDAO) ListShippingCompanies(ctx context.Context, status *int8) ([]*model.ShippingCompany, error) {
	var companies []*model.ShippingCompany
	query := d.db.WithContext(ctx)
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	err := query.Order("created_at DESC").Find(&companies).Error
	return companies, err
}

// DeleteShippingCompany 删除物流公司
func (d *shippingDAO) DeleteShippingCompany(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ShippingCompany{}, id).Error
}

// CreateShippingTemplate 创建物流模板
func (d *shippingDAO) CreateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error {
	return d.db.WithContext(ctx).Create(template).Error
}

// UpdateShippingTemplate 更新物流模板
func (d *shippingDAO) UpdateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error {
	return d.db.WithContext(ctx).Save(template).Error
}

// GetShippingTemplateByID 根据ID获取物流模板
func (d *shippingDAO) GetShippingTemplateByID(ctx context.Context, id int64) (*model.ShippingTemplate, error) {
	var template model.ShippingTemplate
	err := d.db.WithContext(ctx).First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// ListShippingTemplates 获取物流模板列表
func (d *shippingDAO) ListShippingTemplates(ctx context.Context, status *int8) ([]*model.ShippingTemplate, error) {
	var templates []*model.ShippingTemplate
	query := d.db.WithContext(ctx)
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	err := query.Order("is_default DESC, created_at DESC").Find(&templates).Error
	return templates, err
}

// GetDefaultTemplate 获取默认物流模板
func (d *shippingDAO) GetDefaultTemplate(ctx context.Context) (*model.ShippingTemplate, error) {
	var template model.ShippingTemplate
	err := d.db.WithContext(ctx).Where("is_default = ? AND status = ?", true, 1).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// DeleteShippingTemplate 删除物流模板
func (d *shippingDAO) DeleteShippingTemplate(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ShippingTemplate{}, id).Error
}
