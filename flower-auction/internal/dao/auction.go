package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// AuctionQueryParams 拍卖会查询参数
type AuctionQueryParams struct {
	Title    string `form:"title" json:"title"`       // 拍卖会标题
	Status   *int8  `form:"status" json:"status"`     // 状态
	Page     int    `form:"page" json:"page"`         // 页码
	PageSize int    `form:"pageSize" json:"pageSize"` // 每页数量
}

// BidQueryParams 竞价记录查询参数
type BidQueryParams struct {
	Offset      int    `json:"offset"`                         // 偏移量
	Limit       int    `json:"limit"`                          // 限制数量
	ProductName string `form:"productName" json:"productName"` // 商品名称
	Username    string `form:"username" json:"username"`       // 用户名
	Status      *int8  `form:"status" json:"status"`           // 状态
}

// AuctionDAO 拍卖数据访问接口
type AuctionDAO interface {
	CreateAuction(ctx context.Context, auction *model.Auction) error
	UpdateAuction(ctx context.Context, auction *model.Auction) error
	FindAuctionByID(ctx context.Context, id int64) (*model.Auction, error)
	ListAuctions(ctx context.Context, offset, limit int) ([]*model.Auction, error)
	ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error)
	CountAuctions(ctx context.Context) (int64, error)
	DeleteAuction(ctx context.Context, id int64) error

	CreateAuctionItem(ctx context.Context, item *model.AuctionItem) error
	UpdateAuctionItem(ctx context.Context, item *model.AuctionItem) error
	FindAuctionItemByID(ctx context.Context, id int64) (*model.AuctionItem, error)
	ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItem, error)
	DeleteAuctionItem(ctx context.Context, id int64) error

	CreateBid(ctx context.Context, bid *model.Bid) error
	ListBidsByItem(ctx context.Context, itemID int64) ([]*model.Bid, error)
	ListBidsByAuction(ctx context.Context, auctionID int64, offset, limit int) ([]*model.BidWithDetails, int64, error)
	ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error)
	GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error)
	CountBidsByItem(ctx context.Context, itemID int64) (int64, error)
}

// auctionDAO 拍卖数据访问实现
type auctionDAO struct {
	db *gorm.DB
}

// NewAuctionDAO 创建拍卖数据访问实例
func NewAuctionDAO() AuctionDAO {
	return &auctionDAO{
		db: GetAuctionDB(),
	}
}

// CreateAuction 创建拍卖会
func (d *auctionDAO) CreateAuction(ctx context.Context, auction *model.Auction) error {
	return d.db.WithContext(ctx).Create(auction).Error
}

// UpdateAuction 更新拍卖会
func (d *auctionDAO) UpdateAuction(ctx context.Context, auction *model.Auction) error {
	return d.db.WithContext(ctx).Save(auction).Error
}

// FindAuctionByID 根据ID查找拍卖会
func (d *auctionDAO) FindAuctionByID(ctx context.Context, id int64) (*model.Auction, error) {
	var auction model.Auction
	if err := d.db.WithContext(ctx).First(&auction, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &auction, nil
}

// ListAuctions 查询拍卖会列表
func (d *auctionDAO) ListAuctions(ctx context.Context, offset, limit int) ([]*model.Auction, error) {
	var auctions []*model.Auction
	if err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&auctions).Error; err != nil {
		return nil, err
	}
	return auctions, nil
}

// ListAuctionsWithFilter 带过滤条件查询拍卖会列表
func (d *auctionDAO) ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error) {
	var auctions []*model.Auction
	var total int64

	// 构建查询条件
	query := d.db.WithContext(ctx).Model(&model.Auction{})

	// 按标题搜索（模糊匹配）
	if params.Title != "" {
		query = query.Where("name LIKE ?", "%"+params.Title+"%")
	}

	// 按状态筛选
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	if err := query.Offset(offset).Limit(params.PageSize).
		Order("created_at DESC").Find(&auctions).Error; err != nil {
		return nil, 0, err
	}

	return auctions, total, nil
}

// CountAuctions 统计拍卖会总数
func (d *auctionDAO) CountAuctions(ctx context.Context) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.Auction{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CreateAuctionItem 创建拍卖商品
func (d *auctionDAO) CreateAuctionItem(ctx context.Context, item *model.AuctionItem) error {
	return d.db.WithContext(ctx).Create(item).Error
}

// UpdateAuctionItem 更新拍卖商品
func (d *auctionDAO) UpdateAuctionItem(ctx context.Context, item *model.AuctionItem) error {
	return d.db.WithContext(ctx).Save(item).Error
}

// FindAuctionItemByID 根据ID查找拍卖商品
func (d *auctionDAO) FindAuctionItemByID(ctx context.Context, id int64) (*model.AuctionItem, error) {
	var item model.AuctionItem
	if err := d.db.WithContext(ctx).First(&item, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

// ListAuctionItems 查询拍卖商品列表
func (d *auctionDAO) ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItem, error) {
	var items []*model.AuctionItem
	query := d.db.WithContext(ctx)

	// 如果auctionID为0，查询所有拍卖商品；否则查询特定拍卖会的商品
	if auctionID > 0 {
		query = query.Where("auction_id = ?", auctionID)
	}

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// CreateBid 创建竞价记录
func (d *auctionDAO) CreateBid(ctx context.Context, bid *model.Bid) error {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	return d.db.WithContext(ctx).Table(tableName).Create(bid).Error
}

// ListBidsByItem 查询商品的竞价记录
func (d *auctionDAO) ListBidsByItem(ctx context.Context, itemID int64) ([]*model.Bid, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var bids []*model.Bid
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ?", itemID).
		Order("price DESC").
		Find(&bids).Error; err != nil {
		return nil, err
	}
	return bids, nil
}

// GetHighestBid 获取商品的最高出价
func (d *auctionDAO) GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var bid model.Bid
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ? AND status = 1", itemID).
		Order("price DESC").
		First(&bid).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &bid, nil
}

// ListBidsByAuction 查询拍卖会的竞价记录
func (d *auctionDAO) ListBidsByAuction(ctx context.Context, auctionID int64, offset, limit int) ([]*model.BidWithDetails, int64, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))

	// 先查询基础竞价记录
	var bids []*model.Bid
	var total int64

	// 构建基础查询
	query := d.db.WithContext(ctx).
		Table(tableName+" b").
		Joins("JOIN auction_item ai ON b.auction_item_id = ai.id").
		Where("ai.auction_id = ?", auctionID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询基础竞价记录
	if err := query.Select("b.*").
		Offset(offset).Limit(limit).
		Order("b.created_at DESC").
		Find(&bids).Error; err != nil {
		return nil, 0, err
	}

	// 转换为详细记录并补充信息
	results := make([]*model.BidWithDetails, len(bids))
	for i, bid := range bids {
		results[i] = &model.BidWithDetails{
			Bid:         *bid,
			BidderName:  "用户" + fmt.Sprintf("%d", bid.UserID),        // 临时显示，后续可以优化
			ProductName: "商品" + fmt.Sprintf("%d", bid.AuctionItemID), // 临时显示，后续可以优化
			ProductID:   0,                                           // 需要从auction_item表查询
			IsWinning:   false,                                       // 需要计算
		}
	}

	return results, total, nil
}

// CountBidsByItem 统计商品的竞价次数
func (d *auctionDAO) CountBidsByItem(ctx context.Context, itemID int64) (int64, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var count int64
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ?", itemID).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// DeleteAuction 删除拍卖会
func (d *auctionDAO) DeleteAuction(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Auction{}, id).Error
}

// DeleteAuctionItem 删除拍卖商品
func (d *auctionDAO) DeleteAuctionItem(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.AuctionItem{}, id).Error
}

// ListBidsByAuctionWithFilter 带过滤条件查询拍卖会的竞价记录
func (d *auctionDAO) ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))

	// 构建基础查询
	query := d.db.WithContext(ctx).
		Table(tableName+" b").
		Joins("JOIN auction_item ai ON b.auction_item_id = ai.id").
		Joins("JOIN product_db.product p ON ai.product_id = p.id").
		Joins("JOIN user_db.user u ON b.user_id = u.id").
		Where("ai.auction_id = ?", auctionID)

	// 添加搜索条件
	if params.ProductName != "" {
		query = query.Where("p.name LIKE ?", "%"+params.ProductName+"%")
	}

	if params.Username != "" {
		query = query.Where("u.username LIKE ? OR u.real_name LIKE ?", "%"+params.Username+"%", "%"+params.Username+"%")
	}

	if params.Status != nil {
		query = query.Where("b.status = ?", *params.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询详细记录
	var results []struct {
		model.Bid
		BidderName  string `gorm:"column:bidder_name"`
		ProductName string `gorm:"column:product_name"`
		ProductID   int64  `gorm:"column:product_id"`
	}

	if err := query.Select("b.*, u.real_name as bidder_name, p.name as product_name, p.id as product_id").
		Offset(params.Offset).Limit(params.Limit).
		Order("b.created_at DESC").
		Find(&results).Error; err != nil {
		return nil, 0, err
	}

	// 转换为BidWithDetails
	bidDetails := make([]*model.BidWithDetails, len(results))
	for i, result := range results {
		bidDetails[i] = &model.BidWithDetails{
			Bid:         result.Bid,
			BidderName:  result.BidderName,
			ProductName: result.ProductName,
			ProductID:   result.ProductID,
			IsWinning:   false, // 需要计算是否为中标价格
		}
	}

	return bidDetails, total, nil
}
