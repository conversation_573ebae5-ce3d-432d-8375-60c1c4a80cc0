package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// ProductSearchFilter 商品搜索过滤器
type ProductSearchFilter struct {
	Name         string // 商品名称
	CategoryID   int64  // 分类ID
	QualityLevel int8   // 质量等级
	Status       *int8  // 状态
	Origin       string // 产地
	AuditStatus  string // 审核状态
	SupplierName string // 供应商名称
	Offset       int    // 偏移量
	Limit        int    // 限制数量
}

// ProductDAO 商品数据访问接口
type ProductDAO interface {
	// 商品管理
	Create(ctx context.Context, product *model.Product) error
	Update(ctx context.Context, product *model.Product) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*model.Product, error)
	FindWithCategory(ctx context.Context, id int64) (*model.ProductWithCategory, error)
	List(ctx context.Context, offset, limit int) ([]*model.Product, error)
	ListByCategoryID(ctx context.Context, categoryID int64, offset, limit int) ([]*model.ProductWithCategory, error)
	ListWithCategoryAndSupplier(ctx context.Context, offset, limit int) ([]*model.ProductWithCategory, error)
	Count(ctx context.Context) (int64, error)
	CountByCategory(ctx context.Context, categoryID int64) (int64, error)
	FindByCategory(ctx context.Context, categoryID int64, offset, limit int) ([]*model.Product, error)
	SearchProducts(ctx context.Context, filter ProductSearchFilter) ([]*model.Product, error)
	CountSearchProducts(ctx context.Context, filter ProductSearchFilter) (int64, error)

	// 事务支持
	BeginTx(ctx context.Context) *gorm.DB
	UpdateWithTx(ctx context.Context, tx *gorm.DB, product *model.Product) error

	// 审核历史管理
	CreateAuditHistory(ctx context.Context, tx *gorm.DB, history *model.ProductAuditHistory) error
	GetAuditHistory(ctx context.Context, productID int64) ([]*model.ProductAuditHistory, error)

	// 分类管理
	CreateCategory(ctx context.Context, category *model.Category) error
	UpdateCategory(ctx context.Context, category *model.Category) error
	DeleteCategory(ctx context.Context, id int64) error
	FindCategoryByID(ctx context.Context, id int64) (*model.Category, error)
	FindCategoriesByParentID(ctx context.Context, parentID int64) ([]*model.Category, error)
	ListCategories(ctx context.Context) ([]*model.Category, error)
	ListAllCategories(ctx context.Context) ([]*model.Category, error)
	GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error)
}

// productDAO 商品数据访问实现
type productDAO struct {
	db *gorm.DB
}

// NewProductDAO 创建商品数据访问实例
func NewProductDAO() ProductDAO {
	return &productDAO{
		db: GetProductDB(),
	}
}

// Create 创建商品
func (d *productDAO) Create(ctx context.Context, product *model.Product) error {
	return d.db.WithContext(ctx).Create(product).Error
}

// Update 更新商品
func (d *productDAO) Update(ctx context.Context, product *model.Product) error {
	return d.db.WithContext(ctx).Save(product).Error
}

// Delete 删除商品
func (d *productDAO) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Product{}, id).Error
}

// FindByID 根据ID查找商品
func (d *productDAO) FindByID(ctx context.Context, id int64) (*model.Product, error) {
	var product model.Product
	err := d.db.WithContext(ctx).First(&product, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}

// List 分页查询商品列表
func (d *productDAO) List(ctx context.Context, offset, limit int) ([]*model.Product, error) {
	var products []*model.Product
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&products).Error
	return products, err
}

// Count 统计商品总数
func (d *productDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Product{}).Count(&count).Error
	return count, err
}

// FindByCategory 根据分类查询商品
func (d *productDAO) FindByCategory(ctx context.Context, categoryID int64, offset, limit int) ([]*model.Product, error) {
	var products []*model.Product
	err := d.db.WithContext(ctx).Where("category_id = ?", categoryID).Offset(offset).Limit(limit).Find(&products).Error
	return products, err
}

// CreateCategory 创建分类
func (d *productDAO) CreateCategory(ctx context.Context, category *model.Category) error {
	return d.db.WithContext(ctx).Create(category).Error
}

// UpdateCategory 更新分类
func (d *productDAO) UpdateCategory(ctx context.Context, category *model.Category) error {
	return d.db.WithContext(ctx).Save(category).Error
}

// DeleteCategory 删除分类
func (d *productDAO) DeleteCategory(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Category{}, id).Error
}

// FindCategoryByID 根据ID查找分类
func (d *productDAO) FindCategoryByID(ctx context.Context, id int64) (*model.Category, error) {
	var category model.Category
	err := d.db.WithContext(ctx).First(&category, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &category, nil
}

// ListCategories 查询所有分类
func (d *productDAO) ListCategories(ctx context.Context) ([]*model.Category, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Order("sort_order ASC").Find(&categories).Error
	return categories, err
}

// GetCategoryTree 获取分类树
func (d *productDAO) GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Order("sort_order ASC").Find(&categories).Error
	if err != nil {
		return nil, err
	}

	// 构建树形结构
	categoryMap := make(map[int64]*model.CategoryTree)
	var roots []model.CategoryTree

	// 先创建所有节点
	for _, cat := range categories {
		tree := model.CategoryTree{
			Category: *cat,
			Children: []model.CategoryTree{},
		}
		categoryMap[cat.ID] = &tree
	}

	// 构建父子关系
	for _, cat := range categories {
		if cat.ParentID == nil {
			// 根节点
			roots = append(roots, *categoryMap[cat.ID])
		} else {
			// 子节点
			if parent, exists := categoryMap[*cat.ParentID]; exists {
				parent.Children = append(parent.Children, *categoryMap[cat.ID])
			}
		}
	}

	return roots, nil
}

// FindWithCategory 根据ID查找商品及分类信息
func (d *productDAO) FindWithCategory(ctx context.Context, id int64) (*model.ProductWithCategory, error) {
	var result model.ProductWithCategory
	err := d.db.WithContext(ctx).
		Table("product p").
		Select("p.*, c.name as category_name").
		Joins("LEFT JOIN category c ON p.category_id = c.id").
		Where("p.id = ?", id).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

// ListByCategoryID 根据分类ID查询商品列表
func (d *productDAO) ListByCategoryID(ctx context.Context, categoryID int64, offset, limit int) ([]*model.ProductWithCategory, error) {
	var products []*model.ProductWithCategory
	err := d.db.WithContext(ctx).
		Table("product p").
		Select("p.*, c.name as category_name").
		Joins("LEFT JOIN category c ON p.category_id = c.id").
		Where("p.category_id = ?", categoryID).
		Offset(offset).
		Limit(limit).
		Find(&products).Error
	return products, err
}

// ListWithCategoryAndSupplier 查询商品列表（包含分类和供应商信息）
func (d *productDAO) ListWithCategoryAndSupplier(ctx context.Context, offset, limit int) ([]*model.ProductWithCategory, error) {
	var results []struct {
		model.Product
		CategoryName string `json:"categoryName"`
		SupplierName string `json:"supplierName"`
	}

	// 需要跨数据库查询，使用完整的表名
	err := d.db.WithContext(ctx).
		Table("product p").
		Select("p.*, c.name as category_name, u.real_name as supplier_name").
		Joins("LEFT JOIN category c ON p.category_id = c.id").
		Joins("LEFT JOIN user_db.user u ON p.supplier_id = u.id").
		Offset(offset).
		Limit(limit).
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	// 转换为ProductWithCategory
	products := make([]*model.ProductWithCategory, len(results))
	for i, result := range results {
		products[i] = &model.ProductWithCategory{
			Product: result.Product,
			Category: model.Category{
				Name: result.CategoryName,
			},
			SupplierName: result.SupplierName,
		}
	}

	return products, nil
}

// CountByCategory 根据分类统计商品数量
func (d *productDAO) CountByCategory(ctx context.Context, categoryID int64) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Product{}).Where("category_id = ?", categoryID).Count(&count).Error
	return count, err
}

// FindCategoriesByParentID 根据父分类ID查找子分类
func (d *productDAO) FindCategoriesByParentID(ctx context.Context, parentID int64) ([]*model.Category, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Where("parent_id = ?", parentID).Find(&categories).Error
	return categories, err
}

// ListAllCategories 查询所有分类
func (d *productDAO) ListAllCategories(ctx context.Context) ([]*model.Category, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Order("sort_order ASC").Find(&categories).Error
	return categories, err
}

// SearchProducts 搜索商品（支持多条件）
func (d *productDAO) SearchProducts(ctx context.Context, filter ProductSearchFilter) ([]*model.Product, error) {
	query := d.db.WithContext(ctx).Model(&model.Product{})

	// 构建查询条件
	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.CategoryID > 0 {
		query = query.Where("category_id = ?", filter.CategoryID)
	}
	if filter.QualityLevel > 0 {
		query = query.Where("quality_level = ?", filter.QualityLevel)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.Origin != "" {
		query = query.Where("origin LIKE ?", "%"+filter.Origin+"%")
	}
	if filter.AuditStatus != "" {
		query = query.Where("audit_status = ?", filter.AuditStatus)
	}
	if filter.SupplierName != "" {
		// 需要关联供应商表进行搜索
		query = query.Joins("LEFT JOIN users ON products.supplier_id = users.id").
			Where("users.username LIKE ? OR users.real_name LIKE ?", "%"+filter.SupplierName+"%", "%"+filter.SupplierName+"%")
	}

	var products []*model.Product
	err := query.Offset(filter.Offset).Limit(filter.Limit).Find(&products).Error
	return products, err
}

// CountSearchProducts 统计搜索结果数量
func (d *productDAO) CountSearchProducts(ctx context.Context, filter ProductSearchFilter) (int64, error) {
	query := d.db.WithContext(ctx).Model(&model.Product{})

	// 构建查询条件（与SearchProducts相同的条件）
	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.CategoryID > 0 {
		query = query.Where("category_id = ?", filter.CategoryID)
	}
	if filter.QualityLevel > 0 {
		query = query.Where("quality_level = ?", filter.QualityLevel)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.Origin != "" {
		query = query.Where("origin LIKE ?", "%"+filter.Origin+"%")
	}
	if filter.AuditStatus != "" {
		query = query.Where("audit_status = ?", filter.AuditStatus)
	}
	if filter.SupplierName != "" {
		// 需要关联供应商表进行搜索
		query = query.Joins("LEFT JOIN users ON products.supplier_id = users.id").
			Where("users.username LIKE ? OR users.real_name LIKE ?", "%"+filter.SupplierName+"%", "%"+filter.SupplierName+"%")
	}

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// BeginTx 开始事务
func (d *productDAO) BeginTx(ctx context.Context) *gorm.DB {
	return d.db.WithContext(ctx).Begin()
}

// UpdateWithTx 在事务中更新商品
func (d *productDAO) UpdateWithTx(ctx context.Context, tx *gorm.DB, product *model.Product) error {
	return tx.WithContext(ctx).Save(product).Error
}

// CreateAuditHistory 创建审核历史记录
func (d *productDAO) CreateAuditHistory(ctx context.Context, tx *gorm.DB, history *model.ProductAuditHistory) error {
	return tx.WithContext(ctx).Create(history).Error
}

// GetAuditHistory 获取商品审核历史
func (d *productDAO) GetAuditHistory(ctx context.Context, productID int64) ([]*model.ProductAuditHistory, error) {
	var histories []*model.ProductAuditHistory
	err := d.db.WithContext(ctx).
		Where("product_id = ?", productID).
		Order("audit_time DESC").
		Find(&histories).Error
	return histories, err
}
