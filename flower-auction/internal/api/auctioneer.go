package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/service"
)

// AuctioneerHandler 拍卖师接口处理器
type AuctioneerHandler struct {
	auctionService service.AuctionService
	userService    service.UserService
	productService service.ProductService
}

// NewAuctioneerHandler 创建拍卖师接口处理器
func NewAuctioneerHandler() *AuctioneerHandler {
	return &AuctioneerHandler{
		auctionService: service.NewAuctionService(),
		userService:    service.NewUserService(),
		productService: service.NewProductService(),
	}
}

// RegisterRoutes 注册拍卖师端路由
func (h *AuctioneerHandler) RegisterRoutes(r *gin.Engine) {
	auctioneer := r.Group("/api/v1/auctioneer")
	{
		// 控制台统计
		auctioneer.GET("/dashboard/statistics", h.GetDashboardStatistics)

		// 钟号管理
		auctioneer.GET("/clocks/status", h.GetClocksStatus)
		auctioneer.PUT("/clocks/:clockNumber/status", h.UpdateClockStatus)

		// 批次控制
		auctioneer.GET("/batches", h.GetBatches)
		auctioneer.GET("/batches/:id", h.GetBatchDetail)
		auctioneer.PUT("/batches/:id/assign-clock", h.AssignBatchToClock)

		// 拍卖控制
		auctioneer.POST("/auction/start", h.StartAuctionItem)
		auctioneer.POST("/auction/pause", h.PauseAuctionItem)
		auctioneer.POST("/auction/resume", h.ResumeAuctionItem)
		auctioneer.POST("/auction/stop", h.StopAuctionItem)
		auctioneer.POST("/auction/unsold", h.MarkAsUnsold)
		auctioneer.POST("/auction/adjust-price", h.AdjustPrice)

		// 实时数据
		auctioneer.GET("/realtime/clocks", h.GetRealtimeClockData)
		auctioneer.GET("/realtime/bids/:itemId", h.GetRealtimeBids)
	}
}

// DashboardStatistics 控制台统计数据
type DashboardStatistics struct {
	TotalClocks      int     `json:"totalClocks"`
	ActiveClocks     int     `json:"activeClocks"`
	TodayAuctions    int     `json:"todayAuctions"`
	TodayTurnover    float64 `json:"todayTurnover"`
	OnlineUsers      int     `json:"onlineUsers"`
	PendingBatches   int     `json:"pendingBatches"`
	CompletedBatches int     `json:"completedBatches"`
}

// GetDashboardStatistics 获取控制台统计数据
func (h *AuctioneerHandler) GetDashboardStatistics(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取钟号统计
	totalClocks := 12 // 固定12个钟号
	activeClocks, err := h.auctionService.GetActiveClocksCount(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取活跃钟号数量失败"})
		return
	}

	// 获取今日拍卖统计
	todayStats, err := h.auctionService.GetTodayAuctionStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取今日拍卖统计失败"})
		return
	}

	// 获取在线用户数
	onlineUsers, err := h.userService.GetOnlineUsersCount(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取在线用户数失败"})
		return
	}

	// 获取批次统计
	batchStats, err := h.auctionService.GetBatchStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取批次统计失败"})
		return
	}

	stats := DashboardStatistics{
		TotalClocks:      totalClocks,
		ActiveClocks:     activeClocks,
		TodayAuctions:    todayStats.AuctionCount,
		TodayTurnover:    todayStats.TotalTurnover,
		OnlineUsers:      onlineUsers,
		PendingBatches:   batchStats.PendingCount,
		CompletedBatches: batchStats.CompletedCount,
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    stats,
		Message: "获取统计数据成功",
	})
}

// ClockStatus 钟号状态
type ClockStatus struct {
	ClockNumber int    `json:"clockNumber"`
	Status      string `json:"status"` // idle, active, paused, error
	CurrentItem *struct {
		ID           int64   `json:"id"`
		BatchNumber  string  `json:"batchNumber"`
		ProductName  string  `json:"productName"`
		CurrentPrice float64 `json:"currentPrice"`
		BidCount     int     `json:"bidCount"`
	} `json:"currentItem"`
	LastUpdate string `json:"lastUpdate"`
}

// GetClocksStatus 获取所有钟号状态
func (h *AuctioneerHandler) GetClocksStatus(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取所有钟号状态（1-12号钟）
	clocksStatus := make([]ClockStatus, 0, 12)

	for clockNumber := 1; clockNumber <= 12; clockNumber++ {
		// 获取钟号当前状态
		clockStatus, err := h.auctionService.GetClockStatus(ctx, clockNumber)
		if err != nil {
			// 如果获取失败，设置为空闲状态
			clocksStatus = append(clocksStatus, ClockStatus{
				ClockNumber: clockNumber,
				Status:      "idle",
				CurrentItem: nil,
				LastUpdate:  time.Now().Format("2006-01-02 15:04:05"),
			})
			continue
		}

		// 获取当前钟号的拍卖商品
		currentItem, err := h.auctionService.GetCurrentItemByClock(ctx, clockNumber)
		if err != nil || currentItem == nil {
			// 没有当前商品，钟号空闲
			clocksStatus = append(clocksStatus, ClockStatus{
				ClockNumber: clockNumber,
				Status:      "idle",
				CurrentItem: nil,
				LastUpdate:  time.Now().Format("2006-01-02 15:04:05"),
			})
			continue
		}

		// 获取商品的竞价次数
		bidCount, _ := h.auctionService.GetItemBidCount(ctx, currentItem.ID)

		// 获取商品信息
		product, err := h.productService.GetProduct(ctx, currentItem.ProductID)
		productName := "未知商品"
		if err == nil && product != nil {
			productName = product.Product.Name
		}

		// 构建当前商品信息
		itemInfo := &struct {
			ID           int64   `json:"id"`
			BatchNumber  string  `json:"batchNumber"`
			ProductName  string  `json:"productName"`
			CurrentPrice float64 `json:"currentPrice"`
			BidCount     int     `json:"bidCount"`
		}{
			ID:           currentItem.ID,
			BatchNumber:  currentItem.BatchNumber,
			ProductName:  productName,
			CurrentPrice: currentItem.CurrentPrice,
			BidCount:     int(bidCount),
		}

		clocksStatus = append(clocksStatus, ClockStatus{
			ClockNumber: clockNumber,
			Status:      clockStatus,
			CurrentItem: itemInfo,
			LastUpdate:  currentItem.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    clocksStatus,
		Message: "获取钟号状态成功",
	})
}

// UpdateClockStatusRequest 更新钟号状态请求
type UpdateClockStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=idle active paused error"`
}

// UpdateClockStatus 更新钟号状态
func (h *AuctioneerHandler) UpdateClockStatus(c *gin.Context) {
	clockNumber, err := strconv.Atoi(c.Param("clockNumber"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的钟号"})
		return
	}

	// 验证钟号范围
	if clockNumber < 1 || clockNumber > 12 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "钟号必须在1-12之间"})
		return
	}

	var req UpdateClockStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 更新钟号状态
	err = h.auctionService.UpdateClockStatus(ctx, clockNumber, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "钟号状态更新成功",
	})
}

// GetBatches 获取批次列表
func (h *AuctioneerHandler) GetBatches(c *gin.Context) {
	// 获取查询参数
	status := c.Query("status")
	clockNumberStr := c.Query("clockNumber")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	ctx := c.Request.Context()
	offset := (page - 1) * pageSize

	var clockNumber *int
	if clockNumberStr != "" {
		if num, err := strconv.Atoi(clockNumberStr); err == nil {
			clockNumber = &num
		}
	}

	// 调用service获取批次列表
	items, total, err := h.auctionService.GetAuctionItemsForAuctioneer(ctx, status, clockNumber, offset, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取批次列表失败"})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  items,
		Total: total,
		Page:  page,
		Size:  pageSize,
	})
}

// GetBatchDetail 获取批次详情
func (h *AuctioneerHandler) GetBatchDetail(c *gin.Context) {
	batchID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 实现批次详情查询逻辑
	item, err := h.auctionService.GetAuctionItem(c.Request.Context(), batchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    item,
		Message: "获取批次详情成功",
	})
}

// AssignBatchToClockRequest 分配批次到钟号请求
type AssignBatchToClockRequest struct {
	ClockNumber int `json:"clockNumber" binding:"required,min=1,max=20"`
}

// AssignBatchToClock 分配批次到钟号
func (h *AuctioneerHandler) AssignBatchToClock(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignBatchToClockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现批次分配到钟号的逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "批次分配成功",
	})
}

// AuctionControlRequest 拍卖控制请求
type AuctionControlRequest struct {
	ItemID      int64   `json:"itemId" binding:"required"`
	ClockNumber int     `json:"clockNumber" binding:"required"`
	Price       float64 `json:"price,omitempty"`
	Reason      string  `json:"reason,omitempty"`
}

// StartAuctionItem 开始拍卖商品
func (h *AuctioneerHandler) StartAuctionItem(c *gin.Context) {
	var req AuctionControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现开始拍卖逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖开始成功",
	})
}

// PauseAuctionItem 暂停拍卖商品
func (h *AuctioneerHandler) PauseAuctionItem(c *gin.Context) {
	var req AuctionControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现暂停拍卖逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖暂停成功",
	})
}

// ResumeAuctionItem 恢复拍卖商品
func (h *AuctioneerHandler) ResumeAuctionItem(c *gin.Context) {
	var req AuctionControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现恢复拍卖逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖恢复成功",
	})
}

// StopAuctionItem 停止拍卖商品
func (h *AuctioneerHandler) StopAuctionItem(c *gin.Context) {
	var req AuctionControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现停止拍卖逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖停止成功",
	})
}

// MarkAsUnsold 标记为流拍
func (h *AuctioneerHandler) MarkAsUnsold(c *gin.Context) {
	var req AuctionControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现标记流拍逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "标记流拍成功",
	})
}

// AdjustPriceRequest 调价请求
type AdjustPriceRequest struct {
	ItemID      int64   `json:"itemId" binding:"required"`
	ClockNumber int     `json:"clockNumber" binding:"required"`
	NewPrice    float64 `json:"newPrice" binding:"required,min=0"`
	Reason      string  `json:"reason" binding:"required"`
}

// AdjustPrice 调整价格
func (h *AuctioneerHandler) AdjustPrice(c *gin.Context) {
	var req AdjustPriceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现价格调整逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "价格调整成功",
	})
}

// GetRealtimeClockData 获取实时钟号数据
func (h *AuctioneerHandler) GetRealtimeClockData(c *gin.Context) {
	// TODO: 实现实时钟号数据获取逻辑
	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    []interface{}{},
		Message: "获取实时数据成功",
	})
}

// GetRealtimeBids 获取实时竞价数据
func (h *AuctioneerHandler) GetRealtimeBids(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 实现实时竞价数据获取逻辑
	bids, err := h.auctionService.ListBids(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    bids,
		Message: "获取实时竞价数据成功",
	})
}
