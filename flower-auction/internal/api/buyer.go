package api

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// getCurrentUserID 获取当前用户ID的辅助函数
func getCurrentUserID(c *gin.Context) (int64, error) {
	userID, _, _, _, ok := middleware.GetCurrentUser(c)
	if !ok {
		return 0, errors.New("用户信息获取失败")
	}
	return userID, nil
}

// BuyerHandler 购买商接口处理器
type BuyerHandler struct {
	auctionService service.AuctionService
	userService    service.UserService
	financeService service.FinanceService
}

// NewBuyerHandler 创建购买商接口处理器
func NewBuyerHandler() *BuyerHandler {
	return &BuyerHandler{
		auctionService: service.NewAuctionService(),
		userService:    service.NewUserService(),
		financeService: service.NewFinanceService(),
	}
}

// RegisterRoutes 注册购买商端路由
func (h *BuyerHandler) RegisterRoutes(r *gin.Engine) {
	buyer := r.Group("/api/v1/buyer")

	// 公开接口（不需要认证）
	public := buyer.Group("/public")
	{
		// 批次浏览（公开）
		public.GET("/batches", h.GetBatches)
		public.GET("/batches/:id", h.GetBatchDetail)
		public.GET("/batches/search", h.SearchBatches)
	}

	// 需要认证的接口
	authenticated := buyer.Group("")
	authenticated.Use(middleware.JWTAuth())
	authenticated.Use(middleware.RequireUserType(2)) // 2: 购买商
	{
		// 关注管理
		authenticated.POST("/watchlist", h.AddToWatchlist)
		authenticated.DELETE("/watchlist/:itemId", h.RemoveFromWatchlist)
		authenticated.GET("/watchlist", h.GetWatchlist)

		// 竞价功能
		authenticated.POST("/bids", h.PlaceBid)
		authenticated.GET("/bids/my", h.GetMyBids)
		authenticated.GET("/bids/item/:itemId", h.GetItemBids)

		// 埋单功能
		authenticated.POST("/pre-orders", h.CreatePreOrder)
		authenticated.PUT("/pre-orders/:id", h.UpdatePreOrder)
		authenticated.DELETE("/pre-orders/:id", h.CancelPreOrder)
		authenticated.GET("/pre-orders", h.GetMyPreOrders)

		// 账户管理
		authenticated.GET("/account/balance", h.GetAccountBalance)
		authenticated.GET("/account/transactions", h.GetTransactions)
		authenticated.POST("/account/recharge", h.Recharge)

		// 通知管理
		authenticated.GET("/notifications", h.GetNotifications)
		authenticated.PUT("/notifications/:id/read", h.MarkNotificationRead)
	}
}

// BatchQueryParams 批次查询参数
type BatchQueryParams struct {
	Category    string  `form:"category"`
	Grade       string  `form:"grade"`
	MinPrice    float64 `form:"minPrice"`
	MaxPrice    float64 `form:"maxPrice"`
	Status      string  `form:"status"`
	ClockNumber int     `form:"clockNumber"`
	Keyword     string  `form:"keyword"`
	Page        int     `form:"page"`
	PageSize    int     `form:"pageSize"`
}

// GetBatches 获取批次列表
func (h *BuyerHandler) GetBatches(c *gin.Context) {
	var params BatchQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}

	ctx := c.Request.Context()

	// 获取所有拍卖商品（购买商可以看到所有批次）
	offset := (params.Page - 1) * params.PageSize
	items, total, err := h.auctionService.ListAllAuctionItems(ctx, offset, params.PageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取批次列表失败"})
		return
	}

	// 过滤数据（根据查询参数）
	filteredItems := make([]*service.AuctionItemDetail, 0)
	for _, item := range items {
		// 根据状态过滤
		if params.Status != "" {
			switch params.Status {
			case "pending":
				if item.AuctionItem.Status != 0 {
					continue
				}
			case "active":
				if item.AuctionItem.Status != 1 {
					continue
				}
			case "completed":
				if item.AuctionItem.Status != 2 {
					continue
				}
			}
		}

		// 根据价格范围过滤
		if params.MinPrice > 0 && item.AuctionItem.CurrentPrice < params.MinPrice {
			continue
		}
		if params.MaxPrice > 0 && item.AuctionItem.CurrentPrice > params.MaxPrice {
			continue
		}

		// 根据关键词过滤
		if params.Keyword != "" {
			// 这里可以搜索商品名称、描述等
			// 简化实现，只搜索商品名称
			// 实际应该在数据库层面进行搜索
		}

		filteredItems = append(filteredItems, item)
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  filteredItems,
		Total: total,
		Page:  params.Page,
		Size:  params.PageSize,
	})
}

// GetBatchDetail 获取批次详情
func (h *BuyerHandler) GetBatchDetail(c *gin.Context) {
	batchID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.auctionService.GetAuctionItem(c.Request.Context(), batchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    item,
		Message: "获取批次详情成功",
	})
}

// SearchBatches 搜索批次
func (h *BuyerHandler) SearchBatches(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "搜索关键词不能为空"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// TODO: 实现搜索逻辑
	c.JSON(http.StatusOK, PageResponse{
		List:  []interface{}{},
		Total: 0,
		Page:  page,
		Size:  pageSize,
	})
}

// AddToWatchlistRequest 添加关注请求
type AddToWatchlistRequest struct {
	ItemID int64 `json:"itemId" binding:"required"`
}

// AddToWatchlist 添加到关注列表
func (h *BuyerHandler) AddToWatchlist(c *gin.Context) {
	var req AddToWatchlistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()
	err = h.auctionService.AddToWatchlist(ctx, userID, req.ItemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "添加关注成功",
	})
}

// RemoveFromWatchlist 从关注列表移除
func (h *BuyerHandler) RemoveFromWatchlist(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()
	err = h.auctionService.RemoveFromWatchlist(ctx, userID, itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "移除关注成功",
	})
}

// GetWatchlist 获取关注列表
func (h *BuyerHandler) GetWatchlist(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()
	offset := (page - 1) * pageSize
	items, total, err := h.auctionService.GetUserWatchlist(ctx, userID, offset, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取关注列表失败"})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  items,
		Total: total,
		Page:  page,
		Size:  pageSize,
	})
}

// BuyerPlaceBidRequest 购买商出价请求
type BuyerPlaceBidRequest struct {
	ItemID   int64   `json:"itemId" binding:"required"`
	Price    float64 `json:"price" binding:"required,min=0"`
	Quantity int     `json:"quantity" binding:"required,min=1"`
}

// PlaceBid 出价
func (h *BuyerHandler) PlaceBid(c *gin.Context) {
	var req BuyerPlaceBidRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	err = h.auctionService.PlaceBid(c.Request.Context(), req.ItemID, userID, req.Price)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "出价成功",
	})
}

// GetMyBids 获取我的竞价记录
func (h *BuyerHandler) GetMyBids(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()
	offset := (page - 1) * pageSize

	// 获取用户的竞价记录
	bids, total, err := h.auctionService.GetUserBids(ctx, userID, offset, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取竞价记录失败"})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  bids,
		Total: total,
		Page:  page,
		Size:  pageSize,
	})
}

// GetItemBids 获取商品竞价记录
func (h *BuyerHandler) GetItemBids(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	bids, err := h.auctionService.ListBids(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    bids,
		Message: "获取竞价记录成功",
	})
}

// CreatePreOrderRequest 创建埋单请求
type CreatePreOrderRequest struct {
	ItemID   int64   `json:"itemId" binding:"required"`
	Price    float64 `json:"price" binding:"required,min=0"`
	Quantity int     `json:"quantity" binding:"required,min=1"`
}

// CreatePreOrder 创建埋单
func (h *BuyerHandler) CreatePreOrder(c *gin.Context) {
	var req CreatePreOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 获取当前用户ID
	_ = int64(1) // 临时硬编码，待实现

	// TODO: 实现创建埋单逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "埋单创建成功",
	})
}

// UpdatePreOrderRequest 更新埋单请求
type UpdatePreOrderRequest struct {
	Price    float64 `json:"price" binding:"required,min=0"`
	Quantity int     `json:"quantity" binding:"required,min=1"`
}

// UpdatePreOrder 更新埋单
func (h *BuyerHandler) UpdatePreOrder(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePreOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现更新埋单逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "埋单更新成功",
	})
}

// CancelPreOrder 取消埋单
func (h *BuyerHandler) CancelPreOrder(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现取消埋单逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "埋单取消成功",
	})
}

// GetMyPreOrders 获取我的埋单
func (h *BuyerHandler) GetMyPreOrders(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// TODO: 获取当前用户ID
	_ = int64(1) // 临时硬编码，待实现

	// TODO: 实现获取埋单列表逻辑
	c.JSON(http.StatusOK, PageResponse{
		List:  []interface{}{},
		Total: 0,
		Page:  page,
		Size:  pageSize,
	})
}

// AccountBalance 账户余额信息
type AccountBalance struct {
	Balance      float64 `json:"balance"`
	FrozenAmount float64 `json:"frozenAmount"`
	Available    float64 `json:"available"`
	CreditLimit  float64 `json:"creditLimit"`
}

// GetAccountBalance 获取账户余额
func (h *BuyerHandler) GetAccountBalance(c *gin.Context) {
	// 从JWT token中获取当前用户ID
	userID, err := getCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 获取用户账户信息
	account, err := h.financeService.GetUserAccount(ctx, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取账户信息失败"})
		return
	}

	balance := AccountBalance{
		Balance:      account.Balance + account.FrozenAmount,
		FrozenAmount: account.FrozenAmount,
		Available:    account.Balance,
		CreditLimit:  50000.00, // 固定信用额度，实际应该从配置或用户信息中获取
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    balance,
		Message: "获取账户余额成功",
	})
}

// GetTransactions 获取交易记录
func (h *BuyerHandler) GetTransactions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	_ = c.Query("type")

	// TODO: 获取当前用户ID
	_ = int64(1) // 临时硬编码，待实现

	// TODO: 实现获取交易记录逻辑
	c.JSON(http.StatusOK, PageResponse{
		List:  []interface{}{},
		Total: 0,
		Page:  page,
		Size:  pageSize,
	})
}

// RechargeRequest 充值请求
type RechargeRequest struct {
	Amount        float64 `json:"amount" binding:"required,min=1"`
	PaymentMethod string  `json:"paymentMethod" binding:"required,oneof=alipay wechat bank"`
}

// Recharge 充值
func (h *BuyerHandler) Recharge(c *gin.Context) {
	var req RechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 获取当前用户ID
	_ = int64(1) // 临时硬编码，待实现

	// TODO: 实现充值逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "充值申请提交成功",
	})
}

// GetNotifications 获取通知列表
func (h *BuyerHandler) GetNotifications(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// TODO: 获取当前用户ID
	_ = int64(1) // 临时硬编码，待实现

	// TODO: 实现获取通知列表逻辑
	c.JSON(http.StatusOK, PageResponse{
		List:  []interface{}{},
		Total: 0,
		Page:  page,
		Size:  pageSize,
	})
}

// MarkNotificationRead 标记通知已读
func (h *BuyerHandler) MarkNotificationRead(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现标记通知已读逻辑
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "通知已标记为已读",
	})
}
