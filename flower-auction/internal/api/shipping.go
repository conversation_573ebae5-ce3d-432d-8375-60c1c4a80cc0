package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// ShippingHandler 物流处理器
type ShippingHandler struct {
	shippingService service.ShippingService
}

// NewShippingHandler 创建物流处理器
func NewShippingHandler() *ShippingHandler {
	return &ShippingHandler{
		shippingService: service.NewShippingService(),
	}
}

// RegisterRoutes 注册路由
func (h *ShippingHandler) RegisterRoutes(r *gin.Engine) {
	shipping := r.Group("/api/v1/shipping")
	{
		// 物流信息管理
		shipping.POST("", h.CreateShipping)
		shipping.PUT("/:id", h.UpdateShipping)
		shipping.GET("/:id", h.GetShipping)
		shipping.GET("", h.ListShippings)
		shipping.DELETE("/:id", h.DeleteShipping)
		shipping.GET("/order/:orderId", h.GetShippingByOrderID)
		shipping.GET("/tracking/:trackingNumber", h.GetShippingByTrackingNumber)

		// 发货操作
		shipping.POST("/ship", h.ShipOrder)
		shipping.PUT("/:id/status", h.UpdateShippingStatus)

		// 物流跟踪
		shipping.POST("/:id/track", h.AddTrackingRecord)
		shipping.GET("/:id/tracks", h.GetTrackingHistory)
		shipping.POST("/sync/:trackingNumber", h.SyncTrackingInfo)

		// 统计信息
		shipping.GET("/statistics", h.GetShippingStatistics)
	}

	// 物流公司管理
	company := r.Group("/api/v1/shipping-companies")
	{
		company.POST("", h.CreateShippingCompany)
		company.PUT("/:id", h.UpdateShippingCompany)
		company.GET("/:id", h.GetShippingCompany)
		company.GET("", h.ListShippingCompanies)
		company.DELETE("/:id", h.DeleteShippingCompany)
	}

	// 物流模板管理
	template := r.Group("/api/v1/shipping-templates")
	{
		template.POST("", h.CreateShippingTemplate)
		template.PUT("/:id", h.UpdateShippingTemplate)
		template.GET("/:id", h.GetShippingTemplate)
		template.GET("", h.ListShippingTemplates)
		template.GET("/default", h.GetDefaultTemplate)
		template.DELETE("/:id", h.DeleteShippingTemplate)
	}
}

// CreateShipping 创建物流信息
func (h *ShippingHandler) CreateShipping(c *gin.Context) {
	var shipping model.Shipping
	if err := c.ShouldBindJSON(&shipping); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.CreateShipping(c.Request.Context(), &shipping); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "创建物流信息成功",
		Data:    shipping,
	})
}

// UpdateShipping 更新物流信息
func (h *ShippingHandler) UpdateShipping(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var shipping model.Shipping
	if err := c.ShouldBindJSON(&shipping); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	shipping.ID = id
	if err := h.shippingService.UpdateShipping(c.Request.Context(), &shipping); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "更新物流信息成功",
		Data:    shipping,
	})
}

// GetShipping 获取物流信息
func (h *ShippingHandler) GetShipping(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	shipping, err := h.shippingService.GetShippingByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "物流信息不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: shipping})
}

// ListShippings 获取物流列表
func (h *ShippingHandler) ListShippings(c *gin.Context) {
	var params dao.ShippingQueryParams

	// 解析查询参数
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			params.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			params.PageSize = ps
		}
	}
	if orderID := c.Query("orderId"); orderID != "" {
		if oid, err := strconv.ParseInt(orderID, 10, 64); err == nil {
			params.OrderID = &oid
		}
	}
	if trackingNumber := c.Query("trackingNumber"); trackingNumber != "" {
		params.TrackingNumber = trackingNumber
	}
	if companyID := c.Query("shippingCompanyId"); companyID != "" {
		if cid, err := strconv.ParseInt(companyID, 10, 64); err == nil {
			params.ShippingCompanyID = &cid
		}
	}
	if status := c.Query("status"); status != "" {
		if s, err := strconv.Atoi(status); err == nil {
			st := model.ShippingStatus(s)
			params.Status = &st
		}
	}
	if receiverName := c.Query("receiverName"); receiverName != "" {
		params.ReceiverName = receiverName
	}
	if receiverPhone := c.Query("receiverPhone"); receiverPhone != "" {
		params.ReceiverPhone = receiverPhone
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	shippings, total, err := h.shippingService.ListShippings(c.Request.Context(), params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data: gin.H{
			"list":     shippings,
			"total":    total,
			"page":     params.Page,
			"pageSize": params.PageSize,
		},
	})
}

// DeleteShipping 删除物流信息
func (h *ShippingHandler) DeleteShipping(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.DeleteShipping(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "删除物流信息成功"})
}

// GetShippingByOrderID 根据订单ID获取物流信息
func (h *ShippingHandler) GetShippingByOrderID(c *gin.Context) {
	orderID, err := ParseParamID(c, "orderId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	shipping, err := h.shippingService.GetShippingByOrderID(c.Request.Context(), orderID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "物流信息不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: shipping})
}

// GetShippingByTrackingNumber 根据快递单号获取物流信息
func (h *ShippingHandler) GetShippingByTrackingNumber(c *gin.Context) {
	trackingNumber := c.Param("trackingNumber")
	if trackingNumber == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "快递单号不能为空"})
		return
	}

	shipping, err := h.shippingService.GetShippingByTrackingNumber(c.Request.Context(), trackingNumber)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "物流信息不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: shipping})
}

// ShipOrder 发货
func (h *ShippingHandler) ShipOrder(c *gin.Context) {
	var req struct {
		OrderID      int64                `json:"orderId" binding:"required"`
		ShippingInfo service.ShippingInfo `json:"shippingInfo" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.ShipOrder(c.Request.Context(), req.OrderID, req.ShippingInfo); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "发货成功"})
}

// UpdateShippingStatus 更新物流状态
func (h *ShippingHandler) UpdateShippingStatus(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req struct {
		Status model.ShippingStatus `json:"status" binding:"required"`
		Remark string               `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.UpdateShippingStatus(c.Request.Context(), id, req.Status, req.Remark); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "更新物流状态成功"})
}

// AddTrackingRecord 添加跟踪记录
func (h *ShippingHandler) AddTrackingRecord(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var track service.TrackingRecord
	if err := c.ShouldBindJSON(&track); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if track.TrackTime.IsZero() {
		track.TrackTime = time.Now()
	}

	if err := h.shippingService.AddTrackingRecord(c.Request.Context(), id, track); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "添加跟踪记录成功"})
}

// GetTrackingHistory 获取跟踪历史
func (h *ShippingHandler) GetTrackingHistory(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	tracks, err := h.shippingService.GetTrackingHistory(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: tracks})
}

// SyncTrackingInfo 同步跟踪信息
func (h *ShippingHandler) SyncTrackingInfo(c *gin.Context) {
	trackingNumber := c.Param("trackingNumber")
	if trackingNumber == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "快递单号不能为空"})
		return
	}

	if err := h.shippingService.SyncTrackingInfo(c.Request.Context(), trackingNumber); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "同步跟踪信息成功"})
}

// GetShippingStatistics 获取物流统计
func (h *ShippingHandler) GetShippingStatistics(c *gin.Context) {
	stats, err := h.shippingService.GetShippingStatistics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: stats})
}

// 物流公司管理相关方法
func (h *ShippingHandler) CreateShippingCompany(c *gin.Context) {
	var company model.ShippingCompany
	if err := c.ShouldBindJSON(&company); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.CreateShippingCompany(c.Request.Context(), &company); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "创建物流公司成功",
		Data:    company,
	})
}

func (h *ShippingHandler) UpdateShippingCompany(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var company model.ShippingCompany
	if err := c.ShouldBindJSON(&company); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	company.ID = id
	if err := h.shippingService.UpdateShippingCompany(c.Request.Context(), &company); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "更新物流公司成功",
		Data:    company,
	})
}

func (h *ShippingHandler) GetShippingCompany(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	company, err := h.shippingService.GetShippingCompanyByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "物流公司不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: company})
}

func (h *ShippingHandler) ListShippingCompanies(c *gin.Context) {
	var status *int8
	if s := c.Query("status"); s != "" {
		if st, err := strconv.Atoi(s); err == nil {
			status8 := int8(st)
			status = &status8
		}
	}

	companies, err := h.shippingService.ListShippingCompanies(c.Request.Context(), status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: companies})
}

func (h *ShippingHandler) DeleteShippingCompany(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.DeleteShippingCompany(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "删除物流公司成功"})
}

// 物流模板管理相关方法
func (h *ShippingHandler) CreateShippingTemplate(c *gin.Context) {
	var template model.ShippingTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.CreateShippingTemplate(c.Request.Context(), &template); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "创建物流模板成功",
		Data:    template,
	})
}

func (h *ShippingHandler) UpdateShippingTemplate(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var template model.ShippingTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	template.ID = id
	if err := h.shippingService.UpdateShippingTemplate(c.Request.Context(), &template); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Message: "更新物流模板成功",
		Data:    template,
	})
}

func (h *ShippingHandler) GetShippingTemplate(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	template, err := h.shippingService.GetShippingTemplateByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "物流模板不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: template})
}

func (h *ShippingHandler) ListShippingTemplates(c *gin.Context) {
	var status *int8
	if s := c.Query("status"); s != "" {
		if st, err := strconv.Atoi(s); err == nil {
			status8 := int8(st)
			status = &status8
		}
	}

	templates, err := h.shippingService.ListShippingTemplates(c.Request.Context(), status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: templates})
}

func (h *ShippingHandler) GetDefaultTemplate(c *gin.Context) {
	template, err := h.shippingService.GetDefaultTemplate(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "默认物流模板不存在"})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{Success: true, Data: template})
}

func (h *ShippingHandler) DeleteShippingTemplate(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.shippingService.DeleteShippingTemplate(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "删除物流模板成功"})
}
