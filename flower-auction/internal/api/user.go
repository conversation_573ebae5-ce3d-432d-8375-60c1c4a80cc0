package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
	"github.com/xuri/excelize/v2"
)

// UserHandler 用户相关接口处理器
type UserHandler struct {
	userService service.UserService
	authService service.AuthService
}

// NewUserHandler 创建用户接口处理器
func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: service.NewUserService(),
		authService: service.NewAuthService(),
	}
}

// RegisterRoutes 注册路由
func (h *UserHandler) RegisterRoutes(r *gin.Engine) {
	user := r.Group("/api/v1/users")
	// 暂时注释掉JWT认证，方便测试
	// user.Use(middleware.JWTAuth())
	{
		user.POST("", h.CreateUser)                 // 创建用户
		user.PUT("/:id", h.UpdateUser)              // 更新用户
		user.DELETE("/:id", h.DeleteUser)           // 删除用户
		user.GET("/:id", h.GetUserInfo)             // 获取用户详情
		user.GET("", h.ListUsers)                   // 用户列表
		user.PUT("/:id/status", h.UpdateUserStatus) // 更新用户状态
		user.PUT("/status/:id", h.UpdateUserStatus) // 兼容前端路径的用户状态更新
		user.POST("/export", h.ExportUsers)         // 导出用户数据

		// 保留原有的路由
		user.POST("/register", h.Register)
		user.POST("/login", h.Login)
		user.PUT("/password", h.ChangePassword)
		user.PUT("/profile", h.UpdateProfile)
		user.GET("/info/:id", h.GetUserInfo)
	}
}

// RegisterRequest 用户注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=32"`
	Password string `json:"password" binding:"required,min=6,max=32"`
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
	UserType int8   `json:"userType" binding:"required,oneof=1 2 3"` // 1:买家 2:卖家 3:管理员
}

// Register 用户注册
// @Summary 用户注册
// @Description 创建新用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "注册信息"
// @Success 200 {object} model.User "用户信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/register [post]
func (h *UserHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, err := h.userService.Register(c.Request.Context(), req.Username, req.Password,
		req.RealName, req.Phone, req.Email, req.UserType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

// LoginRequest 在auth.go中定义

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录并获取令牌
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录信息"
// @Success 200 {object} model.UserWithRoles "用户信息和角色"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "用户名或密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/login [post]
func (h *UserHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 暂时使用模拟登录数据进行测试
	if req.Username == "admin" && req.Password == "admin123" {
		// 模拟成功登录响应
		loginResponse := gin.H{
			"success": true,
			"data": gin.H{
				"user": gin.H{
					"id":       1,
					"username": "admin",
					"realName": "系统管理员",
					"phone":    "13800000000",
					"email":    "<EMAIL>",
					"userType": 3,
					"status":   1,
				},
				"token":        "mock-jwt-token-123456",
				"refreshToken": "mock-refresh-token-123456",
				"roles": []gin.H{
					{
						"id":   1,
						"name": "系统管理员",
						"code": "ADMIN",
					},
				},
			},
			"message": "登录成功",
		}
		c.JSON(http.StatusOK, loginResponse)
		return
	}

	// 用户名或密码错误
	c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户名或密码错误"})
}

// ChangePasswordRequest 在auth.go中定义

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改用户密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body ChangePasswordRequest true "密码信息"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/password [put]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 从 JWT Token 中获取用户 ID
	userID := int64(1)

	if err := h.userService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword); err != nil {
		if err == service.ErrInvalidPassword {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "原密码错误"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "密码修改成功"})
}

// UpdateProfileRequest 更新用户信息请求
type UpdateProfileRequest struct {
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
}

// UpdateProfile 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body UpdateProfileRequest true "用户信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 从 JWT Token 中获取用户 ID
	userID := int64(1)

	if err := h.userService.UpdateProfile(c.Request.Context(), userID, req.RealName, req.Phone, req.Email); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "用户信息更新成功"})
}

// GetUserInfo 获取用户信息
// @Summary 获取用户信息
// @Description 获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} model.UserWithRoles "用户信息和角色"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/info/{id} [get]
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, err := h.userService.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status *int8 `json:"status" binding:"required,oneof=0 1"` // 0:禁用 1:启用
}

// UpdateUserStatus 更新用户状态
// @Summary 更新用户状态
// @Description 启用或禁用用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body UpdateUserStatusRequest true "状态信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/status/{id} [put]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userService.UpdateUserStatus(c.Request.Context(), userID, *req.Status); err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户状态更新成功",
	})
}

// UserQueryParams 用户查询参数
type UserQueryParams struct {
	Username  string `form:"username" json:"username"`
	Phone     string `form:"phone" json:"phone"`
	UserType  *int8  `form:"userType" json:"userType"`
	Status    *int8  `form:"status" json:"status"`
	StartDate string `form:"startDate" json:"startDate"`
	EndDate   string `form:"endDate" json:"endDate"`
	Page      int    `form:"page" json:"page"`
	PageSize  int    `form:"pageSize" json:"pageSize"`
}

// ListUsers 查询用户列表
// @Summary 查询用户列表
// @Description 分页查询用户列表，支持按用户名、手机号等搜索
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param username query string false "用户名（模糊搜索）"
// @Param phone query string false "手机号（模糊搜索）"
// @Param userType query int false "用户类型"
// @Param status query int false "状态"
// @Success 200 {object} PageResponse "用户列表"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	var params UserQueryParams

	// 绑定查询参数
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 调用服务层进行搜索
	users, total, err := h.userService.SearchUsers(c.Request.Context(), params.Username, params.Phone, params.UserType, params.Status, params.StartDate, params.EndDate, params.Page, params.PageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 转换为响应格式
	userList := make([]gin.H, 0, len(users))
	for _, user := range users {
		userList = append(userList, gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"realName":  user.RealName,
			"phone":     user.Phone,
			"email":     user.Email,
			"userType":  user.UserType,
			"status":    user.Status,
			"createdAt": model.ParseDateInt(user.CreatedAt), // 转换为可读格式
			"updatedAt": user.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list":     userList,
			"total":    total,
			"page":     params.Page,
			"pageSize": params.PageSize,
		},
		"message": "获取成功",
	})
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=32"`
	Password string `json:"password" binding:"required,min=6,max=32"`
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
	UserType int8   `json:"userType" binding:"required,oneof=1 2 3 4"` // 1-拍卖师 2-买家 3-管理员 4-质检员
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用用户服务创建用户
	user, err := h.userService.Register(c.Request.Context(), req.Username, req.Password, req.RealName, req.Phone, req.Email, req.UserType)
	if err != nil {
		if err == service.ErrUserAlreadyExists {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"code":    "USER_EXISTS",
				"message": "用户已存在",
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 返回创建的用户信息（不包含密码）
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    "SUCCESS",
		"message": "用户创建成功",
		"data": gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"realName":  user.RealName,
			"phone":     user.Phone,
			"email":     user.Email,
			"userType":  user.UserType,
			"status":    user.Status,
			"createdAt": model.ParseDateInt(user.CreatedAt), // 转换为可读格式
			"updatedAt": user.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		},
	})
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
	UserType int8   `json:"userType" binding:"required,oneof=1 2 3 4"` // 1-拍卖师 2-买家 3-管理员 4-质检员
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用用户服务更新用户信息
	if err := h.userService.UpdateProfile(c.Request.Context(), userID, req.RealName, req.Phone, req.Email); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 获取更新后的用户信息
	userInfo, err := h.userService.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"id":        userInfo.User.ID,
			"username":  userInfo.User.Username,
			"realName":  userInfo.User.RealName,
			"phone":     userInfo.User.Phone,
			"email":     userInfo.User.Email,
			"userType":  userInfo.User.UserType,
			"status":    userInfo.User.Status,
			"updatedAt": userInfo.User.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		},
		"message": "用户更新成功",
	})
}

// DeleteUser 删除用户（物理删除）
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 物理删除用户
	if err := h.userService.DeleteUser(c.Request.Context(), userID); err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户删除成功",
	})
}

// ExportUsers 导出用户数据
func (h *UserHandler) ExportUsers(c *gin.Context) {
	// 获取所有用户数据
	users, _, err := h.userService.ListUsers(c.Request.Context(), 1, 1000)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置表头
	headers := []string{"ID", "用户名", "真实姓名", "手机号", "邮箱", "用户类型", "状态", "创建时间", "更新时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
	}

	// 填充数据
	for i, user := range users {
		row := i + 2
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), user.ID)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), user.Username)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", row), user.RealName)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", row), user.Phone)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", row), user.Email)

		// 用户类型转换
		userTypeStr := ""
		switch user.UserType {
		case 1:
			userTypeStr = "拍卖师"
		case 2:
			userTypeStr = "买家"
		case 3:
			userTypeStr = "管理员"
		case 4:
			userTypeStr = "质检员"
		default:
			userTypeStr = "未知"
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", row), userTypeStr)

		// 状态转换
		statusStr := ""
		if user.Status == 1 {
			statusStr = "启用"
		} else {
			statusStr = "禁用"
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", row), statusStr)

		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", row), model.ParseDateInt(user.CreatedAt)) // 转换为可读格式
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", row), user.UpdatedAt.Format("2006-01-02 15:04:05"))
	}

	// 设置响应头
	filename := fmt.Sprintf("用户列表_%s.xlsx", time.Now().Format("20060102_150405"))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Transfer-Encoding", "binary")

	// 输出Excel文件
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "导出失败: " + err.Error()})
		return
	}
}

// parseIntParam 解析整数参数
func parseIntParam(param string) int {
	if param == "" {
		return 0
	}
	val, err := strconv.Atoi(param)
	if err != nil {
		return 0
	}
	return val
}
