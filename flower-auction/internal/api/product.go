package api

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// ProductHandler 商品相关接口处理器
type ProductHandler struct {
	productService service.ProductService
}

// NewProductHandler 创建商品接口处理器
func NewProductHandler() *ProductHandler {
	return &ProductHandler{
		productService: service.NewProductService(),
	}
}

// RegisterRoutes 注册路由
func (h *ProductHandler) RegisterRoutes(r *gin.Engine) {
	product := r.Group("/api/v1/products")
	{
		product.POST("", h.CreateProduct)
		product.PUT("/:id", h.UpdateProduct)
		product.GET("/:id", h.GetProduct)
		product.DELETE("/:id", h.DeleteProduct)
		product.GET("", h.ListProducts)
		product.PUT("/status/:id", h.UpdateProductStatus)
		product.DELETE("/batch", h.BatchDeleteProducts)
		product.GET("/export", h.ExportProducts)
		product.POST("/import", h.ImportProducts)
		product.POST("/upload-image", h.UploadProductImage)
		product.GET("/statistics", h.GetProductStatistics)
		product.POST("/:id/audit", h.AuditProduct)
		product.GET("/:id/audit-history", h.GetProductAuditHistory)
		product.GET("/pending-audit", h.GetPendingAuditProducts)
	}

	category := r.Group("/api/v1/categories")
	{
		category.POST("", h.CreateCategory)
		category.PUT("/:id", h.UpdateCategory)
		category.DELETE("/:id", h.DeleteCategory)
		category.GET("/tree", h.GetCategoryTree)
	}
}

// CreateProductRequest 创建商品请求
type CreateProductRequest struct {
	Name         string `json:"name" binding:"required,min=1,max=128"`
	CategoryID   int64  `json:"categoryId" binding:"required"`
	Description  string `json:"description" binding:"required"`
	QualityLevel int8   `json:"qualityLevel" binding:"required,min=1,max=5"`
	Origin       string `json:"origin" binding:"required"`
	SupplierID   int64  `json:"supplierId" binding:"required"`
}

// CreateProduct 创建商品
// @Summary 创建商品
// @Description 创建新商品
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param request body CreateProductRequest true "商品信息"
// @Success 200 {object} model.Product "商品信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products [post]
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	product, err := h.productService.CreateProduct(c.Request.Context(), req.Name, req.CategoryID,
		req.Description, req.QualityLevel, req.Origin, req.SupplierID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, product)
}

// UpdateProductRequest 更新商品请求
type UpdateProductRequest struct {
	Name         string `json:"name" binding:"required,min=1,max=128"`
	CategoryID   int64  `json:"categoryId" binding:"required"`
	Description  string `json:"description" binding:"required"`
	QualityLevel int8   `json:"qualityLevel" binding:"required,min=1,max=5"`
	Origin       string `json:"origin" binding:"required"`
}

// UpdateProduct 更新商品
// @Summary 更新商品
// @Description 更新商品信息
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Param request body UpdateProductRequest true "商品信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "商品不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products/{id} [put]
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productService.UpdateProduct(c.Request.Context(), productID, req.Name, req.CategoryID,
		req.Description, req.QualityLevel, req.Origin); err != nil {
		if err == service.ErrProductNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "商品更新成功"})
}

// GetProduct 获取商品信息
// @Summary 获取商品信息
// @Description 获取商品详细信息
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Success 200 {object} model.ProductWithCategory "商品信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "商品不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products/{id} [get]
func (h *ProductHandler) GetProduct(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	product, err := h.productService.GetProduct(c.Request.Context(), productID)
	if err != nil {
		if err == service.ErrProductNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, product)
}

// ProductQueryParams 商品查询参数
type ProductQueryParams struct {
	Name         string `form:"name" json:"name"`                 // 商品名称
	CategoryID   int64  `form:"categoryId" json:"categoryId"`     // 分类ID
	QualityLevel int8   `form:"qualityLevel" json:"qualityLevel"` // 质量等级
	Status       *int8  `form:"status" json:"status"`             // 状态（使用指针区分0和未设置）
	Origin       string `form:"origin" json:"origin"`             // 产地
	AuditStatus  string `form:"auditStatus" json:"auditStatus"`   // 审核状态
	SupplierName string `form:"supplierName" json:"supplierName"` // 供应商名称
	Page         int    `form:"page" json:"page"`                 // 页码
	PageSize     int    `form:"pageSize" json:"pageSize"`         // 每页数量
}

// ListProducts 查询商品列表
// @Summary 查询商品列表
// @Description 分页查询商品列表，支持多条件搜索
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param name query string false "商品名称"
// @Param categoryId query int false "分类ID"
// @Param qualityLevel query int false "质量等级"
// @Param status query int false "商品状态"
// @Param origin query string false "产地"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} PageResponse "商品列表"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products [get]
func (h *ProductHandler) ListProducts(c *gin.Context) {
	var params ProductQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 检查是否有搜索条件
	hasSearchConditions := params.Name != "" || params.CategoryID > 0 || params.QualityLevel > 0 || params.Status != nil || params.Origin != "" || params.AuditStatus != "" || params.SupplierName != ""

	var products []*model.ProductWithCategory
	var total int64
	var err error

	if hasSearchConditions {
		// 有搜索条件，使用搜索方法
		serviceParams := struct {
			Name         string
			CategoryID   int64
			QualityLevel int8
			Status       *int8
			Origin       string
			AuditStatus  string
			SupplierName string
			Page         int
			PageSize     int
		}{
			Name:         params.Name,
			CategoryID:   params.CategoryID,
			QualityLevel: params.QualityLevel,
			Status:       params.Status,
			Origin:       params.Origin,
			AuditStatus:  params.AuditStatus,
			SupplierName: params.SupplierName,
			Page:         params.Page,
			PageSize:     params.PageSize,
		}
		products, total, err = h.productService.SearchProducts(c.Request.Context(), serviceParams)
	} else {
		// 没有搜索条件，使用原来的列表方法
		offset := (params.Page - 1) * params.PageSize
		products, total, err = h.productService.ListProducts(c.Request.Context(), 0, offset, params.PageSize)
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  products,
		Total: total,
		Page:  params.Page,
		Size:  params.PageSize,
	})
}

// UpdateProductStatusRequest 更新商品状态请求
type UpdateProductStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"` // 0:下架 1:上架
}

// UpdateProductStatus 更新商品状态
// @Summary 更新商品状态
// @Description 上架或下架商品
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Param request body UpdateProductStatusRequest true "状态信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "商品不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products/status/{id} [put]
func (h *ProductHandler) UpdateProductStatus(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateProductStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productService.UpdateProductStatus(c.Request.Context(), productID, req.Status); err != nil {
		if err == service.ErrProductNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "商品状态更新成功"})
}

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=32"`
	Description string `json:"description"`
	ParentID    *int64 `json:"parentId"`
	Level       int8   `json:"level" binding:"required,min=1,max=3"`
	SortOrder   *int   `json:"sortOrder"` // 改为可选，系统自动计算
}

// CreateCategory 创建分类
// @Summary 创建分类
// @Description 创建新分类
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param request body CreateCategoryRequest true "分类信息"
// @Success 200 {object} model.Category "分类信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/categories [post]
func (h *ProductHandler) CreateCategory(c *gin.Context) {
	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 计算排序值：如果用户没有提供，则自动计算
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}

	category, err := h.productService.CreateCategory(c.Request.Context(), req.Name, req.Description, req.ParentID,
		req.Level, sortOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=32"`
	Description string `json:"description"`
	SortOrder   int    `json:"sortOrder" binding:"required,min=0"`
}

// UpdateCategory 更新分类
// @Summary 更新分类
// @Description 更新分类信息
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param id path int true "分类ID"
// @Param request body UpdateCategoryRequest true "分类信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "分类不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/categories/{id} [put]
func (h *ProductHandler) UpdateCategory(c *gin.Context) {
	categoryID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productService.UpdateCategory(c.Request.Context(), categoryID, req.Name, req.Description, req.SortOrder); err != nil {
		if err == service.ErrCategoryNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "分类不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "分类更新成功", Success: true})
}

// DeleteCategory 删除分类
// @Summary 删除分类
// @Description 删除分类
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param id path int true "分类ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "分类不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/categories/{id} [delete]
func (h *ProductHandler) DeleteCategory(c *gin.Context) {
	categoryID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productService.DeleteCategory(c.Request.Context(), categoryID); err != nil {
		if err == service.ErrCategoryNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "分类不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "分类下存在子分类或商品，无法删除"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Success: true, Message: "分类删除成功"})
}

// GetCategoryTree 获取分类树
// @Summary 获取分类树
// @Description 获取完整的分类树结构
// @Tags 分类管理
// @Accept json
// @Produce json
// @Success 200 {array} model.CategoryTree "分类树"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/categories/tree [get]
func (h *ProductHandler) GetCategoryTree(c *gin.Context) {
	tree, err := h.productService.GetCategoryTree(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, tree)
}

// BatchDeleteProductsRequest 批量删除商品请求
type BatchDeleteProductsRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}

// BatchDeleteProducts 批量删除商品
func (h *ProductHandler) BatchDeleteProducts(c *gin.Context) {
	var req BatchDeleteProductsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该实现批量删除逻辑
	// 为了简化，这里只返回成功消息
	c.JSON(http.StatusOK, SuccessResponse{Message: "批量删除成功"})
}

// ExportProducts 导出商品数据
func (h *ProductHandler) ExportProducts(c *gin.Context) {
	page, size := ParsePagination(c)
	categoryID, _ := strconv.ParseInt(c.DefaultQuery("categoryId", "0"), 10, 64)

	products, _, err := h.productService.ListProducts(c.Request.Context(), categoryID, page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 生成CSV内容
	csvContent := "ID,名称,分类,描述,质量等级,产地,状态,创建时间\n"
	for _, product := range products {
		status := "下架"
		if product.Status == 1 {
			status = "上架"
		}
		csvContent += fmt.Sprintf("%d,%s,%s,%s,%d,%s,%s,%s\n",
			product.ID, product.Name, "分类", product.Description,
			product.QualityLevel, product.Origin, status, product.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=products.csv")
	c.String(http.StatusOK, csvContent)
}

// ImportProducts 导入商品数据
func (h *ProductHandler) ImportProducts(c *gin.Context) {
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "文件上传失败"})
		return
	}
	defer file.Close()

	// 这里应该解析CSV文件并批量创建商品
	// 为了简化，这里只返回成功消息
	c.JSON(http.StatusOK, SuccessResponse{Message: "导入成功"})
}

// UploadProductImage 上传商品图片
func (h *ProductHandler) UploadProductImage(c *gin.Context) {
	file, _, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "图片上传失败"})
		return
	}
	defer file.Close()

	// 这里应该实现图片上传逻辑
	// 为了简化，这里返回一个模拟的URL
	c.JSON(http.StatusOK, gin.H{
		"url": "/uploads/products/sample.jpg",
	})
}

// GetProductStatistics 获取商品统计信息
func (h *ProductHandler) GetProductStatistics(c *gin.Context) {
	// 获取总商品数
	_, total, err := h.productService.ListProducts(c.Request.Context(), 0, 1, 1)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 简化统计，实际应该从数据库查询
	stats := gin.H{
		"total":            total,
		"onlineProducts":   total, // 简化处理
		"newProductsToday": 0,     // 需要实现今日新增商品统计
		"categoryDistribution": gin.H{
			"category1": total,
		},
		"qualityDistribution": gin.H{
			"1": 0,
			"2": 0,
			"3": total,
			"4": 0,
			"5": 0,
		},
	}

	c.JSON(http.StatusOK, stats)
}

// AuditProductRequest 商品审核请求
type AuditProductRequest struct {
	Status string `json:"status" binding:"required,oneof=approved rejected"`
	Reason string `json:"reason"`
}

// AuditProduct 商品审核
func (h *ProductHandler) AuditProduct(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: err.Error(), Message: err.Error()})
		return
	}

	var req AuditProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: err.Error(), Message: err.Error()})
		return
	}

	// 调用服务层进行审核
	if err := h.productService.AuditProduct(c.Request.Context(), productID, req.Status, req.Reason); err != nil {
		if err == service.ErrProductNotFound {
			c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: "商品不存在", Message: "商品不存在"})
			return
		}
		c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: err.Error(), Message: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Success: true, Message: "审核成功"})
}

// GetProductAuditHistory 获取商品审核历史
func (h *ProductHandler) GetProductAuditHistory(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: err.Error(), Message: err.Error()})
		return
	}

	histories, err := h.productService.GetAuditHistory(c.Request.Context(), productID)
	if err != nil {
		c.JSON(http.StatusOK, ErrorResponse{Success: false, Error: err.Error(), Message: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    histories,
		"message": "获取审核历史成功",
	})
}

// GetPendingAuditProducts 获取待审核商品列表
func (h *ProductHandler) GetPendingAuditProducts(c *gin.Context) {
	page, size := ParsePagination(c)

	// 这里应该查询待审核的商品
	// 为了简化，返回空列表
	c.JSON(http.StatusOK, PageResponse{
		List:  []interface{}{},
		Total: 0,
		Page:  page,
		Size:  size,
	})
}

// DeleteProduct 删除商品
// @Summary 删除商品
// @Description 删除指定商品
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "商品不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/products/{id} [delete]
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productService.DeleteProduct(c.Request.Context(), productID); err != nil {
		if err == service.ErrProductNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "商品删除成功"})
}
