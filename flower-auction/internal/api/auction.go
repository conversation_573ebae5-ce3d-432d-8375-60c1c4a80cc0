package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/service"
)

// AuctionHandler 拍卖相关接口处理器
type AuctionHandler struct {
	auctionService service.AuctionService
}

// NewAuctionHandler 创建拍卖接口处理器
func NewAuctionHandler() *AuctionHandler {
	return &AuctionHandler{
		auctionService: service.NewAuctionService(),
	}
}

// NewAuctionHandlerWithService 创建带自定义服务的拍卖处理器
func NewAuctionHandlerWithService(auctionService service.AuctionService) *AuctionHandler {
	return &AuctionHandler{
		auctionService: auctionService,
	}
}

// RegisterRoutes 注册路由
func (h *AuctionHandler) RegisterRoutes(r *gin.Engine) {
	// 拍卖会管理
	auction := r.Group("/api/v1/auctions")
	{
		auction.POST("", h.CreateAuction)
		auction.PUT("/:id", h.UpdateAuction)
		auction.GET("/:id", h.GetAuction)
		auction.GET("", h.ListAuctions)
		auction.DELETE("/:id", h.DeleteAuction)
		auction.PUT("/:id/status", h.UpdateAuctionStatus)
		auction.POST("/:id/start", h.StartAuction)
		auction.POST("/:id/pause", h.PauseAuction)
		auction.POST("/:id/resume", h.ResumeAuction)
		auction.POST("/:id/end", h.EndAuction)
		auction.POST("/:id/cancel", h.CancelAuction)
		auction.GET("/statistics", h.GetAuctionStatistics)
		auction.GET("/:id/participants", h.GetAuctionParticipants)
	}

	// 拍卖商品管理
	item := r.Group("/api/v1/auction-items")
	{
		item.GET("", h.ListAllAuctionItems) // 获取所有拍卖商品列表
		item.POST("", h.AddAuctionItem)
		item.PUT("/:id", h.UpdateAuctionItem)
		item.GET("/:id", h.GetAuctionItem)
		item.GET("/auction/:auctionId", h.ListAuctionItems) // 获取特定拍卖会的商品列表
		item.PUT("/:id/status", h.UpdateAuctionItemStatus)
	}

	// 竞价管理
	bid := r.Group("/api/v1/bids")
	{
		bid.POST("", h.PlaceBid)
		bid.GET("/item/:itemId", h.ListBids)
		bid.GET("/auction/:auctionId", h.ListBidsByAuction)
		bid.GET("/item/:itemId/highest", h.GetHighestBid)
	}
}

// CreateAuctionRequest 创建拍卖会请求
type CreateAuctionRequest struct {
	Name         string    `json:"name" binding:"required"`
	StartTime    time.Time `json:"startTime" binding:"required"`
	EndTime      time.Time `json:"endTime" binding:"required"`
	AuctioneerID int64     `json:"auctioneerId" binding:"required"`
	Description  string    `json:"description"`
	Location     string    `json:"location"`
}

// CreateAuction 创建拍卖会
func (h *AuctionHandler) CreateAuction(c *gin.Context) {
	var req CreateAuctionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	auction, err := h.auctionService.CreateAuction(c.Request.Context(), req.Name, req.StartTime, req.EndTime, req.AuctioneerID, req.Description, req.Location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, auction)
}

// UpdateAuctionRequest 更新拍卖会请求
type UpdateAuctionRequest struct {
	Name         string    `json:"name" binding:"required"`
	StartTime    time.Time `json:"startTime" binding:"required"`
	EndTime      time.Time `json:"endTime" binding:"required"`
	Description  string    `json:"description"`
	Location     string    `json:"location"`
	AuctioneerID int64     `json:"auctioneerID"`
}

// UpdateAuction 更新拍卖会
func (h *AuctionHandler) UpdateAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuction(c.Request.Context(), auctionID, req.AuctioneerID, req.Name, req.Location, req.StartTime, req.EndTime, req.Description); err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖会更新成功"})
}

// GetAuction 获取拍卖会信息
func (h *AuctionHandler) GetAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	auction, err := h.auctionService.GetAuction(c.Request.Context(), auctionID)
	if err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, auction)
}

// AuctionQueryParams 拍卖会查询参数
type AuctionQueryParams struct {
	Title    string `form:"title" json:"title"`       // 拍卖会标题
	Status   *int8  `form:"status" json:"status"`     // 状态
	Page     int    `form:"page" json:"page"`         // 页码
	PageSize int    `form:"pageSize" json:"pageSize"` // 每页数量
}

// ListAuctions 查询拍卖会列表
func (h *AuctionHandler) ListAuctions(c *gin.Context) {
	var params AuctionQueryParams

	// 绑定查询参数
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	auctions, total, err := h.auctionService.ListAuctionsWithFilter(c.Request.Context(), service.AuctionQueryParams{
		Title:    params.Title,
		Status:   params.Status,
		Page:     params.Page,
		PageSize: params.PageSize,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  auctions,
		Total: total,
		Page:  params.Page,
		Size:  params.PageSize,
	})
}

// UpdateAuctionStatusRequest 更新拍卖会状态请求
type UpdateAuctionStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1 2"` // 0-未开始 1-进行中 2-已结束
}

// UpdateAuctionStatus 更新拍卖会状态
func (h *AuctionHandler) UpdateAuctionStatus(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionStatus(c.Request.Context(), auctionID, req.Status); err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖会状态更新成功"})
}

// AddAuctionItemRequest 添加拍卖商品请求
type AddAuctionItemRequest struct {
	AuctionID  int64     `json:"auctionId" binding:"required"`
	ProductID  int64     `json:"productId" binding:"required"`
	StartPrice float64   `json:"startPrice" binding:"required,min=0"`
	StepPrice  float64   `json:"stepPrice" binding:"required,min=0"`
	StartTime  time.Time `json:"startTime" binding:"required"`
}

// AddAuctionItem 添加拍卖商品
func (h *AuctionHandler) AddAuctionItem(c *gin.Context) {
	var req AddAuctionItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.auctionService.AddAuctionItem(c.Request.Context(), req.AuctionID, req.ProductID, req.StartPrice, req.StepPrice, req.StartTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessDataResponse{
		Success: true,
		Data:    item,
		Message: "拍卖商品添加成功",
	})
}

// UpdateAuctionItemRequest 更新拍卖商品请求
type UpdateAuctionItemRequest struct {
	StartPrice float64   `json:"startPrice" binding:"required,min=0"`
	StepPrice  float64   `json:"stepPrice" binding:"required,min=0"`
	StartTime  time.Time `json:"startTime" binding:"required"`
}

// UpdateAuctionItem 更新拍卖商品
func (h *AuctionHandler) UpdateAuctionItem(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionItem(c.Request.Context(), itemID, req.StartPrice, req.StepPrice, req.StartTime); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖商品更新成功"})
}

// GetAuctionItem 获取拍卖商品信息
func (h *AuctionHandler) GetAuctionItem(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.auctionService.GetAuctionItem(c.Request.Context(), itemID)
	if err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// ListAuctionItems 查询拍卖商品列表
func (h *AuctionHandler) ListAuctionItems(c *gin.Context) {
	auctionID, err := ParseParamID(c, "auctionId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	items, err := h.auctionService.ListAuctionItems(c.Request.Context(), auctionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, items)
}

// UpdateAuctionItemStatusRequest 更新拍卖商品状态请求
type UpdateAuctionItemStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1 2 3"` // 0-未开始 1-竞拍中 2-已成交 3-流拍
}

// UpdateAuctionItemStatus 更新拍卖商品状态
func (h *AuctionHandler) UpdateAuctionItemStatus(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionItemStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionItemStatus(c.Request.Context(), itemID, req.Status); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖商品状态更新成功"})
}

// PlaceBidRequest 竞价请求
type PlaceBidRequest struct {
	ItemID int64   `json:"itemId" binding:"required"`
	UserID int64   `json:"userId" binding:"required"`
	Price  float64 `json:"price" binding:"required,min=0"`
}

// PlaceBid 竞价
func (h *AuctionHandler) PlaceBid(c *gin.Context) {
	var req PlaceBidRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.PlaceBid(c.Request.Context(), req.ItemID, req.UserID, req.Price); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		if err == service.ErrInvalidBidPrice {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "出价无效"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "竞价成功"})
}

// ListBids 查询竞价记录
func (h *AuctionHandler) ListBids(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	bids, err := h.auctionService.ListBids(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, bids)
}

// BidQueryParams 竞价记录查询参数
type BidQueryParams struct {
	Page        int    `form:"page"`
	PageSize    int    `form:"pageSize"`
	ProductName string `form:"productName"`
	Username    string `form:"username"`
	Status      *int8  `form:"status"`
}

// ListBidsByAuction 查询拍卖会的竞价记录
func (h *AuctionHandler) ListBidsByAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "auctionId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var params BidQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	bids, total, err := h.auctionService.ListBidsByAuctionWithFilter(c.Request.Context(), auctionID, service.BidQueryParams{
		Page:        params.Page,
		PageSize:    params.PageSize,
		ProductName: params.ProductName,
		Username:    params.Username,
		Status:      params.Status,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  bids,
		Total: total,
		Page:  params.Page,
		Size:  params.PageSize,
	})
}

// GetHighestBid 获取最高出价
func (h *AuctionHandler) GetHighestBid(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	bid, err := h.auctionService.GetHighestBid(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, bid)
}

// StartAuction 开始拍卖
func (h *AuctionHandler) StartAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.StartAuction(c.Request.Context(), auctionID); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖开始成功",
	})
}

// PauseAuction 暂停拍卖
func (h *AuctionHandler) PauseAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.PauseAuction(c.Request.Context(), auctionID); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖暂停成功",
	})
}

// ResumeAuction 恢复拍卖
func (h *AuctionHandler) ResumeAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.ResumeAuction(c.Request.Context(), auctionID); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖恢复成功",
	})
}

// EndAuction 结束拍卖
func (h *AuctionHandler) EndAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.EndAuction(c.Request.Context(), auctionID); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖结束成功",
	})
}

// CancelAuctionRequest 取消拍卖请求
type CancelAuctionRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// CancelAuction 取消拍卖
func (h *AuctionHandler) CancelAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req CancelAuctionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 取消拍卖
	err = h.auctionService.CancelAuction(c.Request.Context(), auctionID)
	if err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖取消成功"})
}

// GetAuctionStatistics 获取拍卖统计信息
func (h *AuctionHandler) GetAuctionStatistics(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取拍卖统计信息
	stats, err := h.auctionService.GetAuctionStatistics(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetAuctionParticipants 获取拍卖参与者列表
func (h *AuctionHandler) GetAuctionParticipants(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 获取拍卖参与者信息
	participants, err := h.auctionService.GetAuctionParticipants(ctx, auctionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, participants)
}

// AuctionItemQueryParams 拍卖商品查询参数
type AuctionItemQueryParams struct {
	AuctionID   *int64 `form:"auctionId" json:"auctionId"`     // 拍卖会ID
	ProductName string `form:"productName" json:"productName"` // 商品名称
	Status      *int8  `form:"status" json:"status"`           // 状态
	Page        int    `form:"page" json:"page"`               // 页码
	PageSize    int    `form:"pageSize" json:"pageSize"`       // 每页数量
}

// ListAllAuctionItems 查询所有拍卖商品列表
func (h *AuctionHandler) ListAllAuctionItems(c *gin.Context) {
	var params AuctionItemQueryParams

	// 绑定查询参数
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 如果指定了auctionId，使用特定拍卖会的商品接口
	if params.AuctionID != nil {
		items, err := h.auctionService.ListAuctionItems(c.Request.Context(), *params.AuctionID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
			return
		}

		c.JSON(http.StatusOK, PageResponse{
			List:  items,
			Total: int64(len(items)),
			Page:  params.Page,
			Size:  params.PageSize,
		})
		return
	}

	// 否则返回所有拍卖商品
	offset := (params.Page - 1) * params.PageSize
	items, total, err := h.auctionService.ListAllAuctionItems(c.Request.Context(), offset, params.PageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  items,
		Total: total,
		Page:  params.Page,
		Size:  params.PageSize,
	})
}

// DeleteAuction 删除拍卖会
func (h *AuctionHandler) DeleteAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.DeleteAuction(c.Request.Context(), auctionID); err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "拍卖会删除成功",
	})
}
