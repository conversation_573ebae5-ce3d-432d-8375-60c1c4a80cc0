package model

import (
	"context"
	"time"
)

// 报表相关模型

// SalesReport 销售报表
type SalesReport struct {
	TotalSales     float64 `json:"total_sales"`
	TotalOrders    int64   `json:"total_orders"`
	TotalCustomers int64   `json:"total_customers"`
	AvgOrderValue  float64 `json:"avg_order_value"`
	SalesGrowth    float64 `json:"sales_growth"`
	OrderGrowth    float64 `json:"order_growth"`
	CustomerGrowth float64 `json:"customer_growth"`
	ConversionRate float64 `json:"conversion_rate"`
}

// SalesTrendData 销售趋势数据
type SalesTrendData struct {
	Date      string  `json:"date"`
	Sales     float64 `json:"sales"`
	Orders    int64   `json:"orders"`
	Customers int64   `json:"customers"`
}

// ProductSalesRank 商品销售排行
type ProductSalesRank struct {
	ID          int64   `json:"id"`
	ProductName string  `json:"product_name"`
	Category    string  `json:"category"`
	SalesAmount int64   `json:"sales_amount"`
	SalesCount  int64   `json:"sales_count"`
	Revenue     float64 `json:"revenue"`
	Rank        int     `json:"rank"`
}

// ChannelDistribution 渠道分布
type ChannelDistribution struct {
	Channel    string  `json:"channel"`
	Value      float64 `json:"value"`
	Percentage float64 `json:"percentage"`
}

// UserReport 用户报表
type UserReport struct {
	TotalUsers         int64   `json:"total_users"`
	NewUsers           int64   `json:"new_users"`
	ActiveUsers        int64   `json:"active_users"`
	RetentionRate      float64 `json:"retention_rate"`
	UserGrowth         float64 `json:"user_growth"`
	ActiveGrowth       float64 `json:"active_growth"`
	AvgSessionDuration float64 `json:"avg_session_duration"`
	BounceRate         float64 `json:"bounce_rate"`
}

// UserGrowthData 用户增长数据
type UserGrowthData struct {
	Date        string `json:"date"`
	NewUsers    int64  `json:"new_users"`
	ActiveUsers int64  `json:"active_users"`
	TotalUsers  int64  `json:"total_users"`
}

// UserDistribution 用户分布
type UserDistribution struct {
	Category   string  `json:"category"`
	Value      int64   `json:"value"`
	Percentage float64 `json:"percentage"`
}

// UserActivityRank 用户活跃度排行
type UserActivityRank struct {
	ID            int64   `json:"id"`
	Username      string  `json:"username"`
	Email         string  `json:"email"`
	LoginCount    int64   `json:"login_count"`
	LastLoginTime string  `json:"last_login_time"`
	TotalSpent    float64 `json:"total_spent"`
	OrderCount    int64   `json:"order_count"`
	UserType      string  `json:"user_type"`
	Status        string  `json:"status"`
}

// ProductReport 商品报表
type ProductReport struct {
	TotalProducts  int64   `json:"total_products"`
	ActiveProducts int64   `json:"active_products"`
	SoldProducts   int64   `json:"sold_products"`
	AvgPrice       float64 `json:"avg_price"`
	TotalViews     int64   `json:"total_views"`
	TotalSales     int64   `json:"total_sales"`
	ConversionRate float64 `json:"conversion_rate"`
	ReturnRate     float64 `json:"return_rate"`
}

// CategorySalesData 分类销售数据
type CategorySalesData struct {
	Category string  `json:"category"`
	Sales    int64   `json:"sales"`
	Count    int64   `json:"count"`
	Revenue  float64 `json:"revenue"`
}

// ProductPerformanceRank 商品性能排行
type ProductPerformanceRank struct {
	ID             int64   `json:"id"`
	ProductName    string  `json:"product_name"`
	Category       string  `json:"category"`
	Price          float64 `json:"price"`
	Views          int64   `json:"views"`
	Sales          int64   `json:"sales"`
	Revenue        float64 `json:"revenue"`
	ConversionRate float64 `json:"conversion_rate"`
	Rating         float64 `json:"rating"`
	Status         string  `json:"status"`
}

// PriceDistribution 价格分布
type PriceDistribution struct {
	PriceRange string  `json:"price_range"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// AuctionReport 拍卖报表
type AuctionReport struct {
	TotalAuctions     int64   `json:"total_auctions"`
	ActiveAuctions    int64   `json:"active_auctions"`
	CompletedAuctions int64   `json:"completed_auctions"`
	TotalBids         int64   `json:"total_bids"`
	AvgBidsPerAuction float64 `json:"avg_bids_per_auction"`
	SuccessRate       float64 `json:"success_rate"`
	AvgDuration       float64 `json:"avg_duration"`
	TotalRevenue      float64 `json:"total_revenue"`
}

// AuctionTrendData 拍卖趋势数据
type AuctionTrendData struct {
	Date         string  `json:"date"`
	Auctions     int64   `json:"auctions"`
	Bids         int64   `json:"bids"`
	Revenue      float64 `json:"revenue"`
	Participants int64   `json:"participants"`
}

// AuctionPerformanceRank 拍卖性能排行
type AuctionPerformanceRank struct {
	ID           int64   `json:"id"`
	AuctionTitle string  `json:"auction_title"`
	ProductName  string  `json:"product_name"`
	StartPrice   float64 `json:"start_price"`
	FinalPrice   float64 `json:"final_price"`
	BidCount     int64   `json:"bid_count"`
	Participants int64   `json:"participants"`
	Duration     float64 `json:"duration"`
	Status       string  `json:"status"`
	SuccessRate  float64 `json:"success_rate"`
}

// AuctionStatusDistribution 拍卖状态分布
type AuctionStatusDistribution struct {
	Status     string  `json:"status"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// ReportService 报表服务接口
type ReportService interface {
	// 销售报表
	GetSalesReport(ctx context.Context, startDate, endDate time.Time, granularity string) (*SalesReport, error)
	GetSalesTrend(ctx context.Context, startDate, endDate time.Time, granularity string) ([]*SalesTrendData, error)
	GetProductSalesRank(ctx context.Context, startDate, endDate time.Time, limit int) ([]*ProductSalesRank, error)
	GetSalesChannelDistribution(ctx context.Context, startDate, endDate time.Time) ([]*ChannelDistribution, error)

	// 用户报表
	GetUserReport(ctx context.Context, startDate, endDate time.Time, userType string) (*UserReport, error)
	GetUserGrowthTrend(ctx context.Context, startDate, endDate time.Time) ([]*UserGrowthData, error)
	GetUserDistribution(ctx context.Context) ([]*UserDistribution, error)
	GetUserActivityRank(ctx context.Context, userType string, limit int) ([]*UserActivityRank, error)

	// 商品报表
	GetProductReport(ctx context.Context, startDate, endDate time.Time, category string) (*ProductReport, error)
	GetCategorySales(ctx context.Context, startDate, endDate time.Time) ([]*CategorySalesData, error)
	GetProductPerformance(ctx context.Context, startDate, endDate time.Time, limit int) ([]*ProductPerformanceRank, error)
	GetPriceDistribution(ctx context.Context) ([]*PriceDistribution, error)

	// 拍卖报表
	GetAuctionReport(ctx context.Context, startDate, endDate time.Time, auctionType string) (*AuctionReport, error)
	GetAuctionTrend(ctx context.Context, startDate, endDate time.Time) ([]*AuctionTrendData, error)
	GetAuctionPerformance(ctx context.Context, startDate, endDate time.Time, limit int) ([]*AuctionPerformanceRank, error)
	GetAuctionStatusDistribution(ctx context.Context) ([]*AuctionStatusDistribution, error)

	// 导出功能
	ExportReport(ctx context.Context, reportType, format string, startDate, endDate time.Time) ([]byte, string, error)
}
