package model

import (
	"time"
)

// Auction 拍卖会模型
type Auction struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	Name         string    `gorm:"column:name" json:"name"`
	StartTime    time.Time `gorm:"column:start_time" json:"startTime"`
	EndTime      time.Time `gorm:"column:end_time" json:"endTime"`
	Status       int8      `gorm:"column:status" json:"status"` // 0-未开始 1-进行中 2-已结束
	AuctioneerID int64     `gorm:"column:auctioneer_id" json:"auctioneerId"`
	Description  string    `gorm:"column:description" json:"description"`
	Location     string    `gorm:"column:location" json:"location"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Auction) TableName() string {
	return "auction"
}

// AuctionItem 拍卖商品模型
type AuctionItem struct {
	ID           int64      `gorm:"primaryKey;column:id" json:"id"`
	AuctionID    int64      `gorm:"column:auction_id" json:"auctionId"`
	ProductID    int64      `gorm:"column:product_id" json:"productId"`
	BatchNumber  string     `gorm:"column:batch_number;size:50" json:"batchNumber"` // 批次号
	ClockNumber  int        `gorm:"column:clock_number" json:"clockNumber"`         // 钟号
	StartPrice   float64    `gorm:"column:start_price" json:"startPrice"`
	CurrentPrice float64    `gorm:"column:current_price" json:"currentPrice"`
	StepPrice    float64    `gorm:"column:step_price" json:"stepPrice"`
	Status       int8       `gorm:"column:status" json:"status"` // 0-未开始 1-进行中 2-已成交 3-流拍 4-已取消
	StartTime    time.Time  `gorm:"column:start_time" json:"startTime"`
	EndTime      *time.Time `gorm:"column:end_time" json:"endTime"`
	WinnerID     *int64     `gorm:"column:winner_id" json:"winnerId"`
	WatchCount   int        `gorm:"column:watch_count;default:0" json:"watchCount"` // 关注人数
	CreatedAt    time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (AuctionItem) TableName() string {
	return "auction_item"
}

// Bid 竞价记录模型
type Bid struct {
	ID            int64     `gorm:"primaryKey;column:id" json:"id"`
	AuctionItemID int64     `gorm:"column:auction_item_id" json:"auctionItemId"`
	UserID        int64     `gorm:"column:user_id" json:"userId"`
	Price         float64   `gorm:"column:price" json:"price"`
	Status        int8      `gorm:"column:status" json:"status"` // 1-有效 0-无效
	CreatedAt     time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (Bid) TableName() string {
	return "bid"
}

// AuctionItemDetail 拍卖商品详情（包含商品信息和当前最高出价）
type AuctionItemDetail struct {
	AuctionItem
	Product    Product `json:"product"`
	HighestBid *Bid    `json:"highestBid"`
	TotalBids  int64   `json:"totalBids"`
}

// BidWithDetails 竞价记录详情（包含用户和商品信息）
type BidWithDetails struct {
	Bid
	BidderName  string `json:"bidderName"`
	ProductName string `json:"productName"`
	ProductID   int64  `json:"productId"`
	IsWinning   bool   `json:"isWinning"`
}

// AuctionWithItems 拍卖会信息（包含拍卖商品）
type AuctionWithItems struct {
	Auction
	Items      []AuctionItemDetail `json:"items"`
	Auctioneer User                `json:"auctioneer"`
}

// AuctionParticipant 拍卖参与者信息
type AuctionParticipant struct {
	UserID         int64     `json:"userId"`
	Username       string    `json:"username"`
	RealName       string    `json:"realName"`
	FirstBidTime   time.Time `json:"firstBidTime"`
	BidCount       int64     `json:"bidCount"`
	TotalBidAmount float64   `json:"totalBidAmount"`
}

// WatchList 关注列表模型
type WatchList struct {
	ID            int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID        int64     `gorm:"column:user_id;not null" json:"userId"`
	AuctionItemID int64     `gorm:"column:auction_item_id;not null" json:"auctionItemId"`
	CreatedAt     time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (WatchList) TableName() string {
	return "watch_list"
}

// PreOrder 埋单模型
type PreOrder struct {
	ID            int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID        int64     `gorm:"column:user_id;not null" json:"userId"`
	AuctionItemID int64     `gorm:"column:auction_item_id;not null" json:"auctionItemId"`
	Price         float64   `gorm:"column:price;not null" json:"price"`
	Quantity      int       `gorm:"column:quantity;default:1" json:"quantity"`
	Status        int8      `gorm:"column:status;default:0" json:"status"` // 0-待生效 1-生效中 2-已执行 3-已取消 4-已过期
	CreatedAt     time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (PreOrder) TableName() string {
	return "pre_order"
}
