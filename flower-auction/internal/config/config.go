package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server      ServerConfig      `yaml:"server"`
	Frontend    FrontendConfig    `yaml:"frontend"`
	API         APIConfig         `yaml:"api"`
	WebSocket   WebSocketConfig   `yaml:"websocket"`
	Database    DatabaseConfig    `yaml:"database"`
	Redis       RedisConfig       `yaml:"redis"`
	JWT         JWTConfig         `yaml:"jwt"`
	Logging     LoggingConfig     `yaml:"logging"`
	Upload      UploadConfig      `yaml:"upload"`
	Email       EmailConfig       `yaml:"email"`
	SMS         SMSConfig         `yaml:"sms"`
	Payment     PaymentConfig     `yaml:"payment"`
	Monitoring  MonitoringConfig  `yaml:"monitoring"`
	Security    SecurityConfig    `yaml:"security"`
	Environment string            `yaml:"-"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Domain       string `yaml:"domain"`
	Port         int    `yaml:"port"`
	Mode         string `yaml:"mode"`
	ReadTimeout  int    `yaml:"read_timeout"`
	WriteTimeout int    `yaml:"write_timeout"`
}

// FrontendConfig 前端配置
type FrontendConfig struct {
	Auctioneer FrontendAppConfig `yaml:"auctioneer"`
	Buyer      FrontendAppConfig `yaml:"buyer"`
	Display    FrontendAppConfig `yaml:"display"`
}

// FrontendAppConfig 前端应用配置
type FrontendAppConfig struct {
	URL      string `yaml:"url"`
	DistPath string `yaml:"dist_path"`
}

// APIConfig API配置
type APIConfig struct {
	BaseURL string     `yaml:"base_url"`
	CORS    CORSConfig `yaml:"cors"`
}

// CORSConfig 跨域配置
type CORSConfig struct {
	AllowedOrigins []string `yaml:"allowed_origins"`
	AllowedMethods []string `yaml:"allowed_methods"`
	AllowedHeaders []string `yaml:"allowed_headers"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	URL                   string `yaml:"url"`
	Path                  string `yaml:"path"`
	HeartbeatInterval     int    `yaml:"heartbeat_interval"`
	ReconnectInterval     int    `yaml:"reconnect_interval"`
	MaxReconnectAttempts  int    `yaml:"max_reconnect_attempts"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL MySQLConfig `yaml:"mysql"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host     string         `yaml:"host"`
	Port     int            `yaml:"port"`
	Database string         `yaml:"database"`
	Username string         `yaml:"username"`
	Password string         `yaml:"password"`
	Charset  string         `yaml:"charset"`
	Timezone string         `yaml:"timezone"`
	Pool     PoolConfig     `yaml:"pool"`
}

// PoolConfig 连接池配置
type PoolConfig struct {
	MaxIdle     int `yaml:"max_idle"`
	MaxOpen     int `yaml:"max_open"`
	MaxLifetime int `yaml:"max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string         `yaml:"host"`
	Port     int            `yaml:"port"`
	Password string         `yaml:"password"`
	Database int            `yaml:"database"`
	Pool     RedisPoolConfig `yaml:"pool"`
}

// RedisPoolConfig Redis连接池配置
type RedisPoolConfig struct {
	MaxIdle     int `yaml:"max_idle"`
	MaxActive   int `yaml:"max_active"`
	IdleTimeout int `yaml:"idle_timeout"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret    string `yaml:"secret"`
	ExpiresIn int    `yaml:"expires_in"`
	RefreshIn int    `yaml:"refresh_in"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	Path         string   `yaml:"path"`
	MaxSize      int      `yaml:"max_size"`
	AllowedTypes []string `yaml:"allowed_types"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTPHost string `yaml:"smtp_host"`
	SMTPPort int    `yaml:"smtp_port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	FromName string `yaml:"from_name"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Provider        string `yaml:"provider"`
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	SignName        string `yaml:"sign_name"`
	TemplateID      string `yaml:"template_id"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	Alipay AlipayConfig `yaml:"alipay"`
	Wechat WechatConfig `yaml:"wechat"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `yaml:"app_id"`
	PrivateKey string `yaml:"private_key"`
	PublicKey  string `yaml:"public_key"`
	GatewayURL string `yaml:"gateway_url"`
}

// WechatConfig 微信支付配置
type WechatConfig struct {
	AppID    string `yaml:"app_id"`
	MchID    string `yaml:"mch_id"`
	Key      string `yaml:"key"`
	CertPath string `yaml:"cert_path"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled     bool   `yaml:"enabled"`
	MetricsPort int    `yaml:"metrics_port"`
	HealthPath  string `yaml:"health_path"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	EnableHTTPS     bool   `yaml:"enable_https"`
	CertFile        string `yaml:"cert_file"`
	KeyFile         string `yaml:"key_file"`
	EnableRateLimit bool   `yaml:"enable_rate_limit"`
	RateLimit       int    `yaml:"rate_limit"`
}

var (
	// AppConfig 全局配置实例
	AppConfig *Config
)

// Load 加载配置文件
func Load(configPath string, env string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = "config/config.yaml"
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置环境
	if env == "" {
		env = os.Getenv("APP_ENV")
		if env == "" {
			env = "development"
		}
	}
	config.Environment = env

	// 应用环境特定配置
	if err := applyEnvironmentConfig(&config, env); err != nil {
		return nil, fmt.Errorf("应用环境配置失败: %w", err)
	}

	// 替换环境变量
	if err := replaceEnvVars(&config); err != nil {
		return nil, fmt.Errorf("替换环境变量失败: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 设置全局配置
	AppConfig = &config

	return &config, nil
}

// applyEnvironmentConfig 应用环境特定配置
func applyEnvironmentConfig(config *Config, env string) error {
	// 这里可以根据环境覆盖特定配置
	switch env {
	case "production":
		// 生产环境配置
		config.Server.Mode = "release"
		config.Logging.Level = "warn"
		config.Security.EnableHTTPS = true
	case "testing":
		// 测试环境配置
		config.Server.Mode = "release"
		config.Database.MySQL.Database = "flower_auction_test"
	case "development":
		// 开发环境配置
		config.Server.Mode = "debug"
		config.Logging.Level = "debug"
	}

	return nil
}

// replaceEnvVars 替换环境变量
func replaceEnvVars(config *Config) error {
	// 数据库配置
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Database.MySQL.Host = host
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Database.MySQL.Username = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Database.MySQL.Password = password
	}
	if database := os.Getenv("DB_NAME"); database != "" {
		config.Database.MySQL.Database = database
	}

	// Redis配置
	if host := os.Getenv("REDIS_HOST"); host != "" {
		config.Redis.Host = host
	}
	if password := os.Getenv("REDIS_PASSWORD"); password != "" {
		config.Redis.Password = password
	}

	// JWT配置
	if secret := os.Getenv("JWT_SECRET"); secret != "" {
		config.JWT.Secret = secret
	}

	// 域名配置
	if domain := os.Getenv("DOMAIN"); domain != "" {
		config.Server.Domain = domain
		// 更新相关URL
		updateDomainURLs(config, domain)
	}

	return nil
}

// updateDomainURLs 更新域名相关的URL
func updateDomainURLs(config *Config, domain string) {
	// 更新API基础URL
	if config.Environment == "production" {
		config.API.BaseURL = fmt.Sprintf("https://%s/api/v1", domain)
		config.WebSocket.URL = fmt.Sprintf("wss://%s/ws", domain)
	} else {
		config.API.BaseURL = fmt.Sprintf("http://%s:8080/api/v1", domain)
		config.WebSocket.URL = fmt.Sprintf("ws://%s:8080/ws", domain)
	}

	// 更新前端URL
	if config.Environment == "production" {
		config.Frontend.Auctioneer.URL = fmt.Sprintf("https://auctioneer.%s", domain)
		config.Frontend.Buyer.URL = fmt.Sprintf("https://buyer.%s", domain)
		config.Frontend.Display.URL = fmt.Sprintf("https://display.%s", domain)
	}

	// 更新CORS允许的源
	config.API.CORS.AllowedOrigins = []string{
		config.Frontend.Auctioneer.URL,
		config.Frontend.Buyer.URL,
		config.Frontend.Display.URL,
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证必需的配置项
	if config.Server.Domain == "" {
		return fmt.Errorf("服务器域名不能为空")
	}
	if config.Database.MySQL.Host == "" {
		return fmt.Errorf("数据库主机不能为空")
	}
	if config.JWT.Secret == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}

	return nil
}

// GetServerAddr 获取服务器监听地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf(":%d", c.Server.Port)
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	mysql := c.Database.MySQL
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=%s",
		mysql.Username,
		mysql.Password,
		mysql.Host,
		mysql.Port,
		mysql.Database,
		mysql.Charset,
		mysql.Timezone,
	)
}

// GetRedisAddr 获取Redis连接地址
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}

// IsDevelopment 是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction 是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// GetAbsPath 获取绝对路径
func GetAbsPath(path string) string {
	if filepath.IsAbs(path) {
		return path
	}
	
	// 获取项目根目录
	wd, _ := os.Getwd()
	return filepath.Join(wd, path)
}
