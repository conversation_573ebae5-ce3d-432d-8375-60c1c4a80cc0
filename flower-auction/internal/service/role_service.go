package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrRoleCodeExists = errors.New("角色编码已存在")
	ErrRoleNameExists = errors.New("角色名称已存在")
	ErrRoleHasUsers   = errors.New("角色下还有用户，无法删除")
)

// RoleService 角色服务
type RoleService struct {
	roleDAO *dao.RoleDAO
	userDAO dao.UserDAO
}

// NewRoleService 创建角色服务
func NewRoleService(roleDAO *dao.RoleDAO, userDAO dao.UserDAO) *RoleService {
	return &RoleService{
		roleDAO: roleDAO,
		userDAO: userDAO,
	}
}

// RoleQueryParams 角色查询参数
type RoleQueryParams struct {
	Name     string `form:"name" json:"name"`
	Code     string `form:"code" json:"code"`
	Status   *int   `form:"status" json:"status"`
	Page     int    `form:"page" json:"page"`
	PageSize int    `form:"pageSize" json:"pageSize"`
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Code        string `json:"code" binding:"required,min=2,max=50"`
	Description string `json:"description"`
	Status      int8   `json:"status" binding:"oneof=0 1"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Description string `json:"description"`
	Status      int8   `json:"status" binding:"oneof=0 1"`
}

// ListRoles 获取角色列表
func (s *RoleService) ListRoles(ctx context.Context, params RoleQueryParams) ([]*model.Role, int64, error) {
	// 构建查询条件
	conditions := make(map[string]interface{})

	if params.Name != "" {
		conditions["name LIKE ?"] = "%" + strings.TrimSpace(params.Name) + "%"
	}

	if params.Code != "" {
		conditions["code LIKE ?"] = "%" + strings.TrimSpace(params.Code) + "%"
	}

	if params.Status != nil {
		conditions["status"] = *params.Status
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	offset := (params.Page - 1) * params.PageSize

	return s.roleDAO.ListWithConditions(ctx, conditions, offset, params.PageSize)
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, req CreateRoleRequest) (*model.Role, error) {
	// 检查角色编码是否已存在
	existingRole, err := s.roleDAO.GetByCode(ctx, req.Code)
	if err != nil && err != dao.ErrRecordNotFound {
		return nil, fmt.Errorf("检查角色编码失败: %w", err)
	}
	if existingRole != nil {
		return nil, ErrRoleCodeExists
	}

	// 检查角色名称是否已存在
	existingRole, err = s.roleDAO.GetByName(ctx, req.Name)
	if err != nil && err != dao.ErrRecordNotFound {
		return nil, fmt.Errorf("检查角色名称失败: %w", err)
	}
	if existingRole != nil {
		return nil, ErrRoleNameExists
	}

	// 创建角色
	role := &model.Role{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Status:      req.Status,
	}

	if err := s.roleDAO.Create(ctx, role); err != nil {
		return nil, fmt.Errorf("创建角色失败: %w", err)
	}

	return role, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, id int64, req UpdateRoleRequest) error {
	// 检查角色是否存在
	role, err := s.roleDAO.GetByID(ctx, id)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return ErrRoleNotFound
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查角色名称是否已被其他角色使用
	if req.Name != role.Name {
		existingRole, err := s.roleDAO.GetByName(ctx, req.Name)
		if err != nil && err != dao.ErrRecordNotFound {
			return fmt.Errorf("检查角色名称失败: %w", err)
		}
		if existingRole != nil && existingRole.ID != id {
			return ErrRoleNameExists
		}
	}

	// 更新角色信息
	role.Name = req.Name
	role.Description = req.Description
	role.Status = req.Status

	if err := s.roleDAO.Update(ctx, role); err != nil {
		return fmt.Errorf("更新角色失败: %w", err)
	}

	return nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, id int64) error {
	// 检查角色是否存在
	_, err := s.roleDAO.GetByID(ctx, id)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return ErrRoleNotFound
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查角色下是否还有用户
	userCount, err := s.userDAO.CountByRoleID(ctx, id)
	if err != nil {
		return fmt.Errorf("检查角色用户数量失败: %w", err)
	}
	if userCount > 0 {
		return ErrRoleHasUsers
	}

	// 删除角色
	if err := s.roleDAO.Delete(ctx, id); err != nil {
		return fmt.Errorf("删除角色失败: %w", err)
	}

	return nil
}

// GetRole 获取角色详情
func (s *RoleService) GetRole(ctx context.Context, id int64) (*model.Role, error) {
	role, err := s.roleDAO.GetByID(ctx, id)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return nil, ErrRoleNotFound
		}
		return nil, fmt.Errorf("获取角色失败: %w", err)
	}

	return role, nil
}

// UpdateRoleStatus 更新角色状态
func (s *RoleService) UpdateRoleStatus(ctx context.Context, id int64, status int8) error {
	// 检查角色是否存在
	role, err := s.roleDAO.GetByID(ctx, id)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return ErrRoleNotFound
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 更新状态
	role.Status = status
	if err := s.roleDAO.Update(ctx, role); err != nil {
		return fmt.Errorf("更新角色状态失败: %w", err)
	}

	return nil
}

// GetRoleByCode 根据编码获取角色
func (s *RoleService) GetRoleByCode(ctx context.Context, code string) (*model.Role, error) {
	role, err := s.roleDAO.GetByCode(ctx, code)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return nil, ErrRoleNotFound
		}
		return nil, fmt.Errorf("获取角色失败: %w", err)
	}

	return role, nil
}

// CheckRoleCodeExists 检查角色编码是否存在
func (s *RoleService) CheckRoleCodeExists(ctx context.Context, code string, excludeID int64) (bool, error) {
	role, err := s.roleDAO.GetByCode(ctx, code)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			return false, nil
		}
		return false, fmt.Errorf("检查角色编码失败: %w", err)
	}

	// 如果是排除的ID，则认为不存在
	if role.ID == excludeID {
		return false, nil
	}

	return true, nil
}

// GetRoleStatistics 获取角色统计信息
func (s *RoleService) GetRoleStatistics(ctx context.Context) (map[string]interface{}, error) {
	total, err := s.roleDAO.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取角色总数失败: %w", err)
	}

	activeCount, err := s.roleDAO.CountByStatus(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("获取启用角色数量失败: %w", err)
	}

	return map[string]interface{}{
		"total":       total,
		"activeRoles": activeCount,
	}, nil
}
