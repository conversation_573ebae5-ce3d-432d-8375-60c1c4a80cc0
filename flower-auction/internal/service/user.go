package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrUserNotFound           = errors.New("用户不存在")
	ErrUserAlreadyExists      = errors.New("用户已存在")
	ErrUserAlreadyExistsPhone = errors.New("用户手机号已存在")
	ErrInvalidPassword        = errors.New("密码错误")
)

// UserService 用户服务接口
type UserService interface {
	// 用户注册
	Register(ctx context.Context, username, password, realName, phone, email string, userType int8) (*model.User, error)
	// 用户登录
	Login(ctx context.Context, username, password string) (*model.UserWithRoles, error)
	// 修改密码
	ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error
	// 更新用户信息
	UpdateProfile(ctx context.Context, userID int64, realName, phone, email string) error
	// 获取用户信息
	GetUserInfo(ctx context.Context, userID int64) (*model.UserWithRoles, error)
	// 禁用/启用用户
	UpdateUserStatus(ctx context.Context, userID int64, status int8) error
	// 删除用户（物理删除）
	DeleteUser(ctx context.Context, userID int64) error
	// 分页查询用户列表
	ListUsers(ctx context.Context, page, size int) ([]*model.User, int64, error)
	// 搜索用户列表
	SearchUsers(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string, page, size int) ([]*model.User, int64, error)
	// 获取在线用户数
	GetOnlineUsersCount(ctx context.Context) (int, error)
}

// userService 用户服务实现
type userService struct {
	userDAO dao.UserDAO
}

// NewUserService 创建用户服务实例
func NewUserService() UserService {
	return &userService{
		userDAO: dao.NewUserDAO(),
	}
}

// Register 用户注册
func (s *userService) Register(ctx context.Context, username, password, realName, phone, email string, userType int8) (*model.User, error) {
	// 检查用户名是否已存在
	existUser, err := s.userDAO.FindByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if existUser != nil {
		return nil, ErrUserAlreadyExists
	}

	// 检查手机号是否已存在
	existUser, err = s.userDAO.FindByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	if existUser != nil {
		return nil, ErrUserAlreadyExists
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &model.User{
		Username:  username,
		Password:  string(hashedPassword),
		RealName:  realName,
		Phone:     phone,
		Email:     email,
		UserType:  userType,
		Status:    1,                  // 默认启用
		CreatedAt: model.GetDateInt(), // 整数格式的日期 (20060102)
		UpdatedAt: time.Now(),
	}

	if err := s.userDAO.Create(ctx, user); err != nil {
		return nil, err
	}

	return user, nil
}

// Login 用户登录
func (s *userService) Login(ctx context.Context, username, password string) (*model.UserWithRoles, error) {
	// 查找用户
	user, err := s.userDAO.FindByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, ErrInvalidPassword
	}

	// 获取用户角色信息
	userWithRoles, err := s.userDAO.FindWithRoles(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	return userWithRoles, nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error {
	// 查找用户
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return ErrInvalidPassword
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	user.Password = string(hashedPassword)
	user.UpdatedAt = time.Now()
	return s.userDAO.Update(ctx, user)
}

// UpdateProfile 更新用户信息
func (s *userService) UpdateProfile(ctx context.Context, userID int64, realName, phone, email string) error {
	// 查找用户
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 如果手机号变更，检查是否已被使用
	if phone != user.Phone {
		existUser, err := s.userDAO.FindByPhone(ctx, phone)
		if err != nil {
			return err
		}
		if existUser != nil {
			return ErrUserAlreadyExistsPhone
		}
	}

	// 更新用户信息
	user.RealName = realName
	user.Phone = phone
	user.Email = email
	user.UpdatedAt = time.Now()
	return s.userDAO.Update(ctx, user)
}

// GetUserInfo 获取用户信息
func (s *userService) GetUserInfo(ctx context.Context, userID int64) (*model.UserWithRoles, error) {
	userWithRoles, err := s.userDAO.FindWithRoles(ctx, userID)
	if err != nil {
		return nil, err
	}
	if userWithRoles == nil {
		return nil, ErrUserNotFound
	}
	return userWithRoles, nil
}

// UpdateUserStatus 更新用户状态
func (s *userService) UpdateUserStatus(ctx context.Context, userID int64, status int8) error {
	// 查找用户
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 更新状态
	user.Status = status
	user.UpdatedAt = time.Now()
	return s.userDAO.Update(ctx, user)
}

// DeleteUser 删除用户（物理删除）
func (s *userService) DeleteUser(ctx context.Context, userID int64) error {
	// 查找用户
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 检查是否为管理员用户，管理员用户不能删除
	if user.UserType == 3 { // 3-管理员
		return errors.New("管理员用户不能删除")
	}

	// 物理删除用户
	return s.userDAO.Delete(ctx, userID)
}

// ListUsers 分页查询用户列表
func (s *userService) ListUsers(ctx context.Context, page, size int) ([]*model.User, int64, error) {
	offset := (page - 1) * size
	users, err := s.userDAO.List(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.userDAO.Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// SearchUsers 搜索用户列表
func (s *userService) SearchUsers(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string, page, size int) ([]*model.User, int64, error) {
	offset := (page - 1) * size
	users, err := s.userDAO.Search(ctx, username, phone, userType, status, startDate, endDate, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.userDAO.CountByCondition(ctx, username, phone, userType, status, startDate, endDate)
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetOnlineUsersCount 获取在线用户数
func (s *userService) GetOnlineUsersCount(ctx context.Context) (int, error) {
	// 这里可以通过多种方式实现：
	// 1. 查询最近活跃的用户（比如最近5分钟内有活动）
	// 2. 通过WebSocket连接数统计
	// 3. 通过Redis中的在线用户列表

	// 简单实现：查询最近5分钟内有活动的用户
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)
	count, err := s.userDAO.CountActiveUsers(ctx, fiveMinutesAgo)
	if err != nil {
		return 0, err
	}

	return int(count), nil
}
