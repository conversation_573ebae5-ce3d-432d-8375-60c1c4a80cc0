package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	websocketHandler "github.com/putonghao/flower-auction/internal/websocket"
)

// RealtimeService 实时数据推送服务
type RealtimeService struct {
	hub            *websocketHandler.Hub
	auctionService AuctionService
	userService    UserService
}

// NewRealtimeService 创建实时数据推送服务
func NewRealtimeService(hub *websocketHandler.Hub) *RealtimeService {
	return &RealtimeService{
		hub:            hub,
		auctionService: NewAuctionService(),
		userService:    NewUserService(),
	}
}

// BidUpdate 竞价更新数据
type BidUpdate struct {
	ItemID       int64   `json:"itemId"`
	BatchNumber  string  `json:"batchNumber"`
	CurrentPrice float64 `json:"currentPrice"`
	BidCount     int64   `json:"bidCount"`
	LastBidder   string  `json:"lastBidder"`
	TimeLeft     int64   `json:"timeLeft"`
	ClockNumber  int     `json:"clockNumber"`
	Timestamp    int64   `json:"timestamp"`
}

// ClockStatusUpdate 钟号状态更新数据
type ClockStatusUpdate struct {
	ClockNumber int                 `json:"clockNumber"`
	Status      string              `json:"status"`
	CurrentItem *AuctionItemSummary `json:"currentItem"`
	LastUpdate  string              `json:"lastUpdate"`
	Timestamp   int64               `json:"timestamp"`
}

// AuctionItemSummary 拍卖商品摘要
type AuctionItemSummary struct {
	ID           int64   `json:"id"`
	BatchNumber  string  `json:"batchNumber"`
	ProductName  string  `json:"productName"`
	CurrentPrice float64 `json:"currentPrice"`
	BidCount     int64   `json:"bidCount"`
	TimeLeft     int64   `json:"timeLeft"`
}

// UserActivityUpdate 用户活动更新
type UserActivityUpdate struct {
	OnlineUsers  int64 `json:"onlineUsers"`
	ActiveBuyers int64 `json:"activeBuyers"`
	Timestamp    int64 `json:"timestamp"`
}

// NotifyBidPlaced 通知新的竞价
func (s *RealtimeService) NotifyBidPlaced(ctx context.Context, bid *model.Bid, item *model.AuctionItem) error {
	// 获取竞价用户信息
	userInfo, err := s.userService.GetUserInfo(ctx, bid.UserID)
	if err != nil {
		log.Printf("获取竞价用户信息失败: %v", err)
		return err
	}

	username := "匿名用户"
	if userInfo != nil {
		username = userInfo.Username
		if userInfo.RealName != "" {
			username = userInfo.RealName
		}
	}

	// 获取竞价次数
	bidCount, err := s.auctionService.GetItemBidCount(ctx, item.ID)
	if err != nil {
		log.Printf("获取竞价次数失败: %v", err)
		bidCount = 0
	}

	// 构建竞价更新数据
	bidUpdate := BidUpdate{
		ItemID:       item.ID,
		BatchNumber:  item.BatchNumber,
		CurrentPrice: bid.Price,
		BidCount:     bidCount + 1, // 包含当前竞价
		LastBidder:   username,
		TimeLeft:     s.calculateTimeLeft(item),
		ClockNumber:  int(item.ClockNumber),
		Timestamp:    time.Now().Unix(),
	}

	// 广播到拍卖房间
	auctionID := fmt.Sprintf("item_%d", item.ID)
	s.hub.BroadcastToAuction(auctionID, "bid_update", bidUpdate)

	// 广播到钟号房间
	clockID := fmt.Sprintf("clock_%d", item.ClockNumber)
	s.hub.BroadcastToAuction(clockID, "bid_update", bidUpdate)

	// 广播到全局房间（用于拍卖师端总览）
	s.hub.BroadcastToAll("bid_placed", map[string]interface{}{
		"itemId":      item.ID,
		"clockNumber": item.ClockNumber,
		"price":       bid.Price,
		"bidder":      username,
		"timestamp":   time.Now().Unix(),
	})

	log.Printf("竞价通知已发送: 商品%d, 价格%.2f, 用户%s", item.ID, bid.Price, username)
	return nil
}

// NotifyClockStatusChanged 通知钟号状态变更
func (s *RealtimeService) NotifyClockStatusChanged(ctx context.Context, clockNumber int, status string) error {
	// 获取当前钟号的拍卖商品
	currentItem, err := s.auctionService.GetCurrentItemByClock(ctx, clockNumber)
	if err != nil {
		log.Printf("获取钟号当前商品失败: %v", err)
	}

	var itemSummary *AuctionItemSummary
	if currentItem != nil {
		// 获取竞价次数
		bidCount, err := s.auctionService.GetItemBidCount(ctx, currentItem.ID)
		if err != nil {
			log.Printf("获取竞价次数失败: %v", err)
			bidCount = 0
		}

		itemSummary = &AuctionItemSummary{
			ID:           currentItem.ID,
			BatchNumber:  currentItem.BatchNumber,
			ProductName:  "商品", // 简化处理
			CurrentPrice: currentItem.CurrentPrice,
			BidCount:     bidCount,
			TimeLeft:     s.calculateTimeLeft(currentItem),
		}
	}

	// 构建钟号状态更新数据
	statusUpdate := ClockStatusUpdate{
		ClockNumber: clockNumber,
		Status:      status,
		CurrentItem: itemSummary,
		LastUpdate:  time.Now().Format("2006-01-02 15:04:05"),
		Timestamp:   time.Now().Unix(),
	}

	// 广播到钟号房间
	clockID := fmt.Sprintf("clock_%d", clockNumber)
	s.hub.BroadcastToAuction(clockID, "clock_status_update", statusUpdate)

	// 广播到拍卖师端
	s.hub.BroadcastToAuction("auctioneer", "clock_status_update", statusUpdate)

	// 广播到全局房间
	s.hub.BroadcastToAll("clock_status_changed", map[string]interface{}{
		"clockNumber": clockNumber,
		"status":      status,
		"timestamp":   time.Now().Unix(),
	})

	log.Printf("钟号状态通知已发送: 钟号%d, 状态%s", clockNumber, status)
	return nil
}

// NotifyAuctionItemStarted 通知拍卖商品开始
func (s *RealtimeService) NotifyAuctionItemStarted(ctx context.Context, item *model.AuctionItem) error {
	// 构建拍卖开始数据
	startData := map[string]interface{}{
		"itemId":       item.ID,
		"batchNumber":  item.BatchNumber,
		"productName":  "商品", // 简化处理
		"startPrice":   item.StartPrice,
		"currentPrice": item.CurrentPrice,
		"clockNumber":  item.ClockNumber,
		"timestamp":    time.Now().Unix(),
	}

	// 广播到拍卖房间
	auctionID := fmt.Sprintf("item_%d", item.ID)
	s.hub.BroadcastToAuction(auctionID, "auction_started", startData)

	// 广播到钟号房间
	clockID := fmt.Sprintf("clock_%d", item.ClockNumber)
	s.hub.BroadcastToAuction(clockID, "auction_started", startData)

	// 广播到全局房间
	s.hub.BroadcastToAll("auction_item_started", startData)

	log.Printf("拍卖开始通知已发送: 商品%d, 钟号%d", item.ID, item.ClockNumber)
	return nil
}

// NotifyAuctionItemCompleted 通知拍卖商品完成
func (s *RealtimeService) NotifyAuctionItemCompleted(ctx context.Context, item *model.AuctionItem, winnerID int64, finalPrice float64) error {
	// 获取中标用户信息
	var winnerName string
	if winnerID > 0 {
		userInfo, err := s.userService.GetUserInfo(ctx, winnerID)
		if err != nil {
			log.Printf("获取中标用户信息失败: %v", err)
			winnerName = "未知用户"
		} else {
			winnerName = userInfo.Username
			if userInfo.RealName != "" {
				winnerName = userInfo.RealName
			}
		}
	}

	// 获取竞价次数
	bidCount, err := s.auctionService.GetItemBidCount(ctx, item.ID)
	if err != nil {
		log.Printf("获取竞价次数失败: %v", err)
		bidCount = 0
	}

	// 构建拍卖完成数据
	completedData := map[string]interface{}{
		"itemId":      item.ID,
		"batchNumber": item.BatchNumber,
		"finalPrice":  finalPrice,
		"winnerName":  winnerName,
		"clockNumber": item.ClockNumber,
		"bidCount":    bidCount,
		"timestamp":   time.Now().Unix(),
	}

	// 广播到拍卖房间
	auctionID := fmt.Sprintf("item_%d", item.ID)
	s.hub.BroadcastToAuction(auctionID, "auction_completed", completedData)

	// 广播到钟号房间
	clockID := fmt.Sprintf("clock_%d", item.ClockNumber)
	s.hub.BroadcastToAuction(clockID, "auction_completed", completedData)

	// 广播到全局房间
	s.hub.BroadcastToAll("auction_item_completed", completedData)

	log.Printf("拍卖完成通知已发送: 商品%d, 成交价%.2f, 中标者%s", item.ID, finalPrice, winnerName)
	return nil
}

// NotifyUserActivity 通知用户活动统计
func (s *RealtimeService) NotifyUserActivity(ctx context.Context) error {
	// 获取在线用户统计
	onlineUsers, err := s.userService.GetOnlineUsersCount(ctx)
	if err != nil {
		log.Printf("获取在线用户数失败: %v", err)
		onlineUsers = 0
	}

	// 简化处理，活跃购买商数量设为在线用户数的一半
	activeBuyers := int64(onlineUsers / 2)

	// 构建用户活动数据
	activityUpdate := UserActivityUpdate{
		OnlineUsers:  int64(onlineUsers),
		ActiveBuyers: activeBuyers,
		Timestamp:    time.Now().Unix(),
	}

	// 广播到拍卖师端
	s.hub.BroadcastToAuction("auctioneer", "user_activity_update", activityUpdate)

	return nil
}

// StartPeriodicUpdates 启动定期更新
func (s *RealtimeService) StartPeriodicUpdates(ctx context.Context) {
	// 每30秒更新一次用户活动统计
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println("实时数据推送服务已停止")
			return
		case <-ticker.C:
			if err := s.NotifyUserActivity(ctx); err != nil {
				log.Printf("推送用户活动统计失败: %v", err)
			}
		}
	}
}

// calculateTimeLeft 计算剩余时间（秒）
func (s *RealtimeService) calculateTimeLeft(item *model.AuctionItem) int64 {
	if item.EndTime == nil {
		// 如果没有结束时间，返回默认值
		return 300 // 5分钟
	}

	timeLeft := item.EndTime.Unix() - time.Now().Unix()
	if timeLeft < 0 {
		return 0
	}
	return timeLeft
}
