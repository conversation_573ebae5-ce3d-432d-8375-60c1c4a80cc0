package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

// FinanceService 财务服务接口
type FinanceService interface {
	// 资金账户管理
	CreateAccount(ctx context.Context, userID int64, accountType int8) (*model.Account, error)
	GetAccount(ctx context.Context, userID int64, accountType int8) (*model.Account, error)
	GetUserAccount(ctx context.Context, userID int64) (*model.Account, error)
	GetAccountBalance(ctx context.Context, userID int64, accountType int8) (float64, error)
	FreezeAmount(ctx context.Context, userID int64, amount float64, reason string) error
	UnfreezeAmount(ctx context.Context, userID int64, amount float64, reason string) error

	// 资金流水管理
	CreateTransaction(ctx context.Context, transaction *model.Transaction) error
	GetTransactionHistory(ctx context.Context, userID int64, page, size int) ([]*model.Transaction, int64, error)
	GetTransactionByNo(ctx context.Context, transactionNo string) (*model.Transaction, error)

	// 收支统计
	GetFinanceStatistics(ctx context.Context) (*model.FinanceStatistics, error)
	GetIncomeStatistics(ctx context.Context, startDate, endDate time.Time) (*model.IncomeStatistics, error)
	GetUserIncomeStatistics(ctx context.Context, userID int64, startDate, endDate time.Time) (*model.UserIncomeStatistics, error)
	GetDailyReport(ctx context.Context, date time.Time) (*model.DailyFinanceReport, error)
	GetMonthlyReport(ctx context.Context, year int, month int) (*model.MonthlyFinanceReport, error)

	// 佣金管理
	CalculateCommission(ctx context.Context, orderID int64) (*model.Commission, error)
	SettleCommission(ctx context.Context, commissionID int64) error
	GetCommissionList(ctx context.Context, page, size int) ([]*model.Commission, int64, error)

	// 对账功能
	CreateReconciliation(ctx context.Context, reconciliation *model.Reconciliation) error
	GetReconciliation(ctx context.Context, id int64) (*model.Reconciliation, error)
	ListReconciliations(ctx context.Context, page, size int) ([]*model.Reconciliation, int64, error)
	ProcessReconciliation(ctx context.Context, id int64) (*model.ReconciliationResult, error)
}

// financeService 财务服务实现
type financeService struct {
	financeDAO dao.FinanceDAO
	orderDAO   dao.OrderDAO
	userDAO    dao.UserDAO
}

// NewFinanceService 创建财务服务实例
func NewFinanceService() FinanceService {
	return &financeService{
		financeDAO: dao.NewFinanceDAO(),
		orderDAO:   dao.NewOrderDAO(),
		userDAO:    dao.NewUserDAO(),
	}
}

// CreateAccount 创建资金账户
func (s *financeService) CreateAccount(ctx context.Context, userID int64, accountType int8) (*model.Account, error) {
	// 检查用户是否存在
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// 检查账户是否已存在
	existingAccount, err := s.financeDAO.FindAccountByUserAndType(ctx, userID, accountType)
	if err != nil {
		return nil, err
	}
	if existingAccount != nil {
		return nil, errors.New("账户已存在")
	}

	// 创建新账户
	account := &model.Account{
		UserID:       userID,
		AccountType:  accountType,
		Balance:      0.0,
		FrozenAmount: 0.0,
		Status:       1, // 1: 正常
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.financeDAO.CreateAccount(ctx, account); err != nil {
		return nil, err
	}

	return account, nil
}

// GetAccount 获取资金账户
func (s *financeService) GetAccount(ctx context.Context, userID int64, accountType int8) (*model.Account, error) {
	account, err := s.financeDAO.FindAccountByUserAndType(ctx, userID, accountType)
	if err != nil {
		return nil, err
	}
	if account == nil {
		return nil, errors.New("账户不存在")
	}
	return account, nil
}

// GetUserAccount 获取用户主账户
func (s *financeService) GetUserAccount(ctx context.Context, userID int64) (*model.Account, error) {
	// 获取用户主账户（accountType = 1）
	account, err := s.financeDAO.FindAccountByUserAndType(ctx, userID, 1)
	if err != nil {
		return nil, err
	}
	if account == nil {
		// 如果账户不存在，自动创建一个
		return s.CreateAccount(ctx, userID, 1)
	}
	return account, nil
}

// GetAccountBalance 获取账户余额
func (s *financeService) GetAccountBalance(ctx context.Context, userID int64, accountType int8) (float64, error) {
	account, err := s.GetAccount(ctx, userID, accountType)
	if err != nil {
		return 0, err
	}
	return account.Balance, nil
}

// FreezeAmount 冻结金额
func (s *financeService) FreezeAmount(ctx context.Context, userID int64, amount float64, reason string) error {
	if amount <= 0 {
		return errors.New("冻结金额必须大于0")
	}

	// 获取账户
	account, err := s.GetAccount(ctx, userID, 1) // 1: 主账户
	if err != nil {
		return err
	}

	// 检查余额是否足够
	if account.Balance < amount {
		return errors.New("账户余额不足")
	}

	// 更新账户
	account.Balance -= amount
	account.FrozenAmount += amount
	account.UpdatedAt = time.Now()

	if err := s.financeDAO.UpdateAccount(ctx, account); err != nil {
		return err
	}

	// 记录交易流水
	transaction := &model.Transaction{
		UserID:        userID,
		TransactionNo: generateTransactionNo("FREEZE"),
		Type:          4, // 4: 冻结
		Amount:        amount,
		Balance:       account.Balance,
		Description:   fmt.Sprintf("冻结资金: %s", reason),
		Status:        1, // 1: 成功
		CreatedAt:     time.Now(),
	}

	return s.financeDAO.CreateTransaction(ctx, transaction)
}

// UnfreezeAmount 解冻金额
func (s *financeService) UnfreezeAmount(ctx context.Context, userID int64, amount float64, reason string) error {
	if amount <= 0 {
		return errors.New("解冻金额必须大于0")
	}

	// 获取账户
	account, err := s.GetAccount(ctx, userID, 1) // 1: 主账户
	if err != nil {
		return err
	}

	// 检查冻结金额是否足够
	if account.FrozenAmount < amount {
		return errors.New("冻结金额不足")
	}

	// 更新账户
	account.Balance += amount
	account.FrozenAmount -= amount
	account.UpdatedAt = time.Now()

	if err := s.financeDAO.UpdateAccount(ctx, account); err != nil {
		return err
	}

	// 记录交易流水
	transaction := &model.Transaction{
		UserID:        userID,
		TransactionNo: generateTransactionNo("UNFREEZE"),
		Type:          5, // 5: 解冻
		Amount:        amount,
		Balance:       account.Balance,
		Description:   fmt.Sprintf("解冻资金: %s", reason),
		Status:        1, // 1: 成功
		CreatedAt:     time.Now(),
	}

	return s.financeDAO.CreateTransaction(ctx, transaction)
}

// CreateTransaction 创建交易记录
func (s *financeService) CreateTransaction(ctx context.Context, transaction *model.Transaction) error {
	if transaction.TransactionNo == "" {
		transaction.TransactionNo = generateTransactionNo("TXN")
	}
	if transaction.CreatedAt.IsZero() {
		transaction.CreatedAt = time.Now()
	}
	return s.financeDAO.CreateTransaction(ctx, transaction)
}

// GetTransactionHistory 获取交易历史
func (s *financeService) GetTransactionHistory(ctx context.Context, userID int64, page, size int) ([]*model.Transaction, int64, error) {
	offset := (page - 1) * size
	transactions, err := s.financeDAO.ListTransactionsByUser(ctx, userID, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.financeDAO.CountTransactionsByUser(ctx, userID)
	if err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// GetTransactionByNo 根据交易号获取交易记录
func (s *financeService) GetTransactionByNo(ctx context.Context, transactionNo string) (*model.Transaction, error) {
	transaction, err := s.financeDAO.FindTransactionByNo(ctx, transactionNo)
	if err != nil {
		return nil, err
	}
	if transaction == nil {
		return nil, errors.New("交易记录不存在")
	}
	return transaction, nil
}

// GetFinanceStatistics 获取财务统计
func (s *financeService) GetFinanceStatistics(ctx context.Context) (*model.FinanceStatistics, error) {
	// 获取总收入（使用一年的时间范围）
	now := time.Now()
	startOfYear := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	endOfYear := startOfYear.AddDate(1, 0, 0)

	totalRevenue, err := s.financeDAO.GetTotalIncomeByDateRange(ctx, startOfYear, endOfYear)
	if err != nil {
		return nil, err
	}

	// 获取总交易数
	totalTransactions, err := s.financeDAO.GetTransactionCountByDateRange(ctx, startOfYear, endOfYear)
	if err != nil {
		return nil, err
	}

	// 获取今日收入
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	todayRevenue, err := s.financeDAO.GetTotalIncomeByDateRange(ctx, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	// 获取今日交易数
	todayTransactions, err := s.financeDAO.GetTransactionCountByDateRange(ctx, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	// 获取月收入
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0)

	monthlyRevenue, err := s.financeDAO.GetTotalIncomeByDateRange(ctx, startOfMonth, endOfMonth)
	if err != nil {
		return nil, err
	}

	// 获取佣金总数（简化实现）
	commissionCount, err := s.financeDAO.CountCommissions(ctx)
	if err != nil {
		return nil, err
	}

	// 简化的分布数据
	typeDistribution := map[string]int64{
		"1": totalTransactions / 4,  // 充值
		"2": totalTransactions / 6,  // 提现
		"3": totalTransactions / 3,  // 拍卖支付
		"4": totalTransactions / 8,  // 拍卖退款
		"5": commissionCount,        // 佣金收入
		"6": totalTransactions / 10, // 佣金支出
	}

	statusDistribution := map[string]int64{
		"0": totalTransactions / 10,     // 待处理
		"1": totalTransactions * 8 / 10, // 成功
		"2": totalTransactions / 20,     // 失败
		"3": totalTransactions / 20,     // 已取消
	}

	return &model.FinanceStatistics{
		TotalRevenue:       totalRevenue,
		TotalCommission:    float64(commissionCount * 500), // 假设平均佣金500元
		TotalTransactions:  totalTransactions,
		TodayRevenue:       todayRevenue,
		TodayTransactions:  todayTransactions,
		MonthlyRevenue:     monthlyRevenue,
		TypeDistribution:   typeDistribution,
		StatusDistribution: statusDistribution,
	}, nil
}

// GetIncomeStatistics 获取收入统计
func (s *financeService) GetIncomeStatistics(ctx context.Context, startDate, endDate time.Time) (*model.IncomeStatistics, error) {
	// 获取总收入
	totalIncome, err := s.financeDAO.GetTotalIncomeByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 获取总支出
	totalExpense, err := s.financeDAO.GetTotalExpenseByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 获取交易笔数
	transactionCount, err := s.financeDAO.GetTransactionCountByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &model.IncomeStatistics{
		StartDate:        startDate,
		EndDate:          endDate,
		TotalIncome:      totalIncome,
		TotalExpense:     totalExpense,
		NetIncome:        totalIncome - totalExpense,
		TransactionCount: transactionCount,
	}, nil
}

// GetUserIncomeStatistics 获取用户收入统计
func (s *financeService) GetUserIncomeStatistics(ctx context.Context, userID int64, startDate, endDate time.Time) (*model.UserIncomeStatistics, error) {
	// 获取用户总收入
	totalIncome, err := s.financeDAO.GetUserTotalIncomeByDateRange(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 获取用户总支出
	totalExpense, err := s.financeDAO.GetUserTotalExpenseByDateRange(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 获取用户交易笔数
	transactionCount, err := s.financeDAO.GetUserTransactionCountByDateRange(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &model.UserIncomeStatistics{
		UserID:           userID,
		StartDate:        startDate,
		EndDate:          endDate,
		TotalIncome:      totalIncome,
		TotalExpense:     totalExpense,
		NetIncome:        totalIncome - totalExpense,
		TransactionCount: transactionCount,
	}, nil
}

// GetDailyReport 获取日报表
func (s *financeService) GetDailyReport(ctx context.Context, date time.Time) (*model.DailyFinanceReport, error) {
	startDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endDate := startDate.Add(24 * time.Hour)

	stats, err := s.GetIncomeStatistics(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &model.DailyFinanceReport{
		Date:             date,
		TotalIncome:      stats.TotalIncome,
		TotalExpense:     stats.TotalExpense,
		NetIncome:        stats.NetIncome,
		TransactionCount: stats.TransactionCount,
	}, nil
}

// GetMonthlyReport 获取月报表
func (s *financeService) GetMonthlyReport(ctx context.Context, year int, month int) (*model.MonthlyFinanceReport, error) {
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	endDate := startDate.AddDate(0, 1, 0)

	stats, err := s.GetIncomeStatistics(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &model.MonthlyFinanceReport{
		Year:             year,
		Month:            month,
		TotalIncome:      stats.TotalIncome,
		TotalExpense:     stats.TotalExpense,
		NetIncome:        stats.NetIncome,
		TransactionCount: stats.TransactionCount,
	}, nil
}

// CalculateCommission 计算佣金
func (s *financeService) CalculateCommission(ctx context.Context, orderID int64) (*model.Commission, error) {
	// 获取订单信息
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 计算佣金（假设佣金率为5%）
	commissionRate := 0.05
	commissionAmount := order.Amount * commissionRate

	commission := &model.Commission{
		OrderID:          orderID,
		UserID:           order.UserID,
		Amount:           order.Amount,
		CommissionRate:   commissionRate,
		CommissionAmount: commissionAmount,
		Status:           0, // 0: 待结算
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := s.financeDAO.CreateCommission(ctx, commission); err != nil {
		return nil, err
	}

	return commission, nil
}

// SettleCommission 结算佣金
func (s *financeService) SettleCommission(ctx context.Context, commissionID int64) error {
	// 获取佣金记录
	commission, err := s.financeDAO.FindCommissionByID(ctx, commissionID)
	if err != nil {
		return err
	}
	if commission == nil {
		return errors.New("佣金记录不存在")
	}

	if commission.Status != 0 {
		return errors.New("佣金已结算")
	}

	// 更新佣金状态
	commission.Status = 1 // 1: 已结算
	commission.SettledAt = time.Now()
	commission.UpdatedAt = time.Now()

	if err := s.financeDAO.UpdateCommission(ctx, commission); err != nil {
		return err
	}

	// 记录交易流水
	transaction := &model.Transaction{
		UserID:        commission.UserID,
		TransactionNo: generateTransactionNo("COMMISSION"),
		Type:          6, // 6: 佣金收入
		Amount:        commission.CommissionAmount,
		Description:   fmt.Sprintf("佣金结算，订单号: %d", commission.OrderID),
		Status:        1, // 1: 成功
		CreatedAt:     time.Now(),
	}

	return s.financeDAO.CreateTransaction(ctx, transaction)
}

// GetCommissionList 获取佣金列表
func (s *financeService) GetCommissionList(ctx context.Context, page, size int) ([]*model.Commission, int64, error) {
	offset := (page - 1) * size
	commissions, err := s.financeDAO.ListCommissions(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.financeDAO.CountCommissions(ctx)
	if err != nil {
		return nil, 0, err
	}

	return commissions, total, nil
}

// CreateReconciliation 创建对账任务
func (s *financeService) CreateReconciliation(ctx context.Context, reconciliation *model.Reconciliation) error {
	if reconciliation.BatchNo == "" {
		reconciliation.BatchNo = generateBatchNo(reconciliation.Type)
	}
	if reconciliation.CreatedAt.IsZero() {
		reconciliation.CreatedAt = time.Now()
	}
	reconciliation.UpdatedAt = time.Now()

	// 这里简化实现，实际应该调用DAO层保存
	// return s.financeDAO.CreateReconciliation(ctx, reconciliation)
	return nil
}

// GetReconciliation 获取对账记录
func (s *financeService) GetReconciliation(ctx context.Context, id int64) (*model.Reconciliation, error) {
	// 这里简化实现，实际应该从数据库查询
	// return s.financeDAO.FindReconciliationByID(ctx, id)
	return nil, errors.New("对账记录不存在")
}

// ListReconciliations 获取对账记录列表
func (s *financeService) ListReconciliations(ctx context.Context, page, size int) ([]*model.Reconciliation, int64, error) {
	// 这里简化实现，实际应该从数据库查询
	// offset := (page - 1) * size
	// return s.financeDAO.ListReconciliations(ctx, offset, size)
	return []*model.Reconciliation{}, 0, nil
}

// ProcessReconciliation 处理对账
func (s *financeService) ProcessReconciliation(ctx context.Context, id int64) (*model.ReconciliationResult, error) {
	// 获取对账记录
	reconciliation, err := s.GetReconciliation(ctx, id)
	if err != nil {
		return nil, err
	}
	if reconciliation == nil {
		return nil, errors.New("对账记录不存在")
	}

	if reconciliation.Status != 0 {
		return nil, errors.New("对账任务已处理")
	}

	// 更新状态为处理中
	reconciliation.Status = 1
	reconciliation.UpdatedAt = time.Now()
	// s.financeDAO.UpdateReconciliation(ctx, reconciliation)

	// 执行对账逻辑
	result, err := s.executeReconciliation(ctx, reconciliation)
	if err != nil {
		// 更新状态为异常
		reconciliation.Status = 3
		reconciliation.UpdatedAt = time.Now()
		// s.financeDAO.UpdateReconciliation(ctx, reconciliation)
		return nil, err
	}

	// 更新对账记录
	reconciliation.Status = 2 // 已完成
	reconciliation.TotalCount = result.TotalCount
	reconciliation.SuccessCount = result.SuccessCount
	reconciliation.FailCount = result.FailCount
	reconciliation.TotalAmount = result.TotalAmount
	reconciliation.DiffAmount = result.DiffAmount
	now := time.Now()
	reconciliation.ProcessedAt = &now
	reconciliation.UpdatedAt = now
	// s.financeDAO.UpdateReconciliation(ctx, reconciliation)

	return result, nil
}

// executeReconciliation 执行对账逻辑
func (s *financeService) executeReconciliation(ctx context.Context, reconciliation *model.Reconciliation) (*model.ReconciliationResult, error) {
	switch reconciliation.Type {
	case 1: // 支付对账
		return s.executePaymentReconciliation(ctx, reconciliation)
	case 2: // 提现对账
		return s.executeWithdrawReconciliation(ctx, reconciliation)
	case 3: // 佣金对账
		return s.executeCommissionReconciliation(ctx, reconciliation)
	default:
		return nil, errors.New("不支持的对账类型")
	}
}

// executePaymentReconciliation 执行支付对账
func (s *financeService) executePaymentReconciliation(ctx context.Context, reconciliation *model.Reconciliation) (*model.ReconciliationResult, error) {
	// 获取系统内的支付记录
	systemPayments, err := s.getSystemPayments(ctx, reconciliation.StartDate, reconciliation.EndDate)
	if err != nil {
		return nil, err
	}

	// 获取第三方支付记录（这里模拟）
	thirdPayments, err := s.getThirdPartyPayments(ctx, reconciliation.StartDate, reconciliation.EndDate)
	if err != nil {
		return nil, err
	}

	// 执行对账比较
	result := &model.ReconciliationResult{
		BatchNo:     reconciliation.BatchNo,
		Type:        reconciliation.Type,
		Status:      2, // 已完成
		ProcessedAt: time.Now(),
		DiffItems:   []*model.ReconciliationDiffItem{},
		Summary: &model.ReconciliationSummary{
			AmountDiffCount:    0,
			MissingSystemCount: 0,
			MissingThirdCount:  0,
			TotalDiffAmount:    0,
		},
	}

	// 比较逻辑（简化实现）
	systemMap := make(map[string]float64)
	for _, payment := range systemPayments {
		systemMap[payment.PaymentNo] = payment.Amount
		result.TotalCount++
		result.TotalAmount += payment.Amount
	}

	for _, thirdPayment := range thirdPayments {
		if systemAmount, exists := systemMap[thirdPayment.PaymentNo]; exists {
			if systemAmount != thirdPayment.Amount {
				// 金额不匹配
				diffItem := &model.ReconciliationDiffItem{
					TransactionNo: thirdPayment.PaymentNo,
					SystemAmount:  systemAmount,
					ThirdAmount:   thirdPayment.Amount,
					DiffAmount:    systemAmount - thirdPayment.Amount,
					DiffType:      "amount_diff",
					Reason:        "金额不匹配",
				}
				result.DiffItems = append(result.DiffItems, diffItem)
				result.Summary.AmountDiffCount++
				result.Summary.TotalDiffAmount += diffItem.DiffAmount
				result.FailCount++
			} else {
				result.SuccessCount++
			}
			delete(systemMap, thirdPayment.PaymentNo)
		} else {
			// 系统中缺失
			diffItem := &model.ReconciliationDiffItem{
				TransactionNo: thirdPayment.PaymentNo,
				ThirdAmount:   thirdPayment.Amount,
				DiffAmount:    -thirdPayment.Amount,
				DiffType:      "missing_system",
				Reason:        "系统中缺失该交易",
			}
			result.DiffItems = append(result.DiffItems, diffItem)
			result.Summary.MissingSystemCount++
			result.Summary.TotalDiffAmount += diffItem.DiffAmount
			result.FailCount++
		}
	}

	// 处理第三方缺失的记录
	for paymentNo, amount := range systemMap {
		diffItem := &model.ReconciliationDiffItem{
			TransactionNo: paymentNo,
			SystemAmount:  amount,
			DiffAmount:    amount,
			DiffType:      "missing_third",
			Reason:        "第三方缺失该交易",
		}
		result.DiffItems = append(result.DiffItems, diffItem)
		result.Summary.MissingThirdCount++
		result.Summary.TotalDiffAmount += diffItem.DiffAmount
		result.FailCount++
	}

	result.DiffAmount = result.Summary.TotalDiffAmount

	return result, nil
}

// executeWithdrawReconciliation 执行提现对账
func (s *financeService) executeWithdrawReconciliation(ctx context.Context, reconciliation *model.Reconciliation) (*model.ReconciliationResult, error) {
	// 简化实现，实际应该实现提现对账逻辑
	return &model.ReconciliationResult{
		BatchNo:     reconciliation.BatchNo,
		Type:        reconciliation.Type,
		Status:      2,
		ProcessedAt: time.Now(),
		DiffItems:   []*model.ReconciliationDiffItem{},
		Summary: &model.ReconciliationSummary{
			AmountDiffCount:    0,
			MissingSystemCount: 0,
			MissingThirdCount:  0,
			TotalDiffAmount:    0,
		},
	}, nil
}

// executeCommissionReconciliation 执行佣金对账
func (s *financeService) executeCommissionReconciliation(ctx context.Context, reconciliation *model.Reconciliation) (*model.ReconciliationResult, error) {
	// 简化实现，实际应该实现佣金对账逻辑
	return &model.ReconciliationResult{
		BatchNo:     reconciliation.BatchNo,
		Type:        reconciliation.Type,
		Status:      2,
		ProcessedAt: time.Now(),
		DiffItems:   []*model.ReconciliationDiffItem{},
		Summary: &model.ReconciliationSummary{
			AmountDiffCount:    0,
			MissingSystemCount: 0,
			MissingThirdCount:  0,
			TotalDiffAmount:    0,
		},
	}, nil
}

// getSystemPayments 获取系统支付记录
func (s *financeService) getSystemPayments(ctx context.Context, startDate, endDate time.Time) ([]*model.Payment, error) {
	// 这里简化实现，实际应该从数据库查询
	return []*model.Payment{}, nil
}

// getThirdPartyPayments 获取第三方支付记录
func (s *financeService) getThirdPartyPayments(ctx context.Context, startDate, endDate time.Time) ([]*model.Payment, error) {
	// 这里简化实现，实际应该调用第三方API获取
	return []*model.Payment{}, nil
}

// generateBatchNo 生成对账批次号
func generateBatchNo(reconciliationType int8) string {
	typePrefix := map[int8]string{
		1: "PAY",
		2: "WITHDRAW",
		3: "COMMISSION",
	}
	prefix := typePrefix[reconciliationType]
	if prefix == "" {
		prefix = "RECON"
	}
	return fmt.Sprintf("%s%s%06d", prefix, time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)
}

// generateTransactionNo 生成交易流水号
func generateTransactionNo(prefix string) string {
	return fmt.Sprintf("%s%s%06d", prefix, time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)
}
