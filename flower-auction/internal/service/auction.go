package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrAuctionNotFound     = errors.New("拍卖会不存在")
	ErrAuctionItemNotFound = errors.New("拍卖商品不存在")
	ErrAuctionEnded        = errors.New("拍卖已结束")
	ErrAuctionNotStarted   = errors.New("拍卖未开始")
	ErrInvalidBidPrice     = errors.New("出价无效")
)

// AuctionQueryParams 拍卖会查询参数
type AuctionQueryParams struct {
	Title    string `form:"title" json:"title"`       // 拍卖会标题
	Status   *int8  `form:"status" json:"status"`     // 状态
	Page     int    `form:"page" json:"page"`         // 页码
	PageSize int    `form:"pageSize" json:"pageSize"` // 每页数量
}

// BidQueryParams 竞价记录查询参数
type BidQueryParams struct {
	Page        int    `form:"page" json:"page"`               // 页码
	PageSize    int    `form:"pageSize" json:"pageSize"`       // 每页数量
	ProductName string `form:"productName" json:"productName"` // 商品名称
	Username    string `form:"username" json:"username"`       // 用户名
	Status      *int8  `form:"status" json:"status"`           // 状态
}

// AuctionService 拍卖服务接口
type AuctionService interface {
	// 拍卖会管理
	CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description, locationn string) (*model.Auction, error)
	UpdateAuction(ctx context.Context, id, auctioneerID int64, name, location string, startTime, endTime time.Time, description string) error
	GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error)
	ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error)
	ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error)
	UpdateAuctionStatus(ctx context.Context, id int64, status int8) error
	DeleteAuction(ctx context.Context, id int64) error

	// 拍卖商品管理
	AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error)
	UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error
	GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error)
	ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error)
	ListAllAuctionItems(ctx context.Context, offset, limit int) ([]*model.AuctionItemDetail, int64, error)
	UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error

	// 拍卖控制
	StartAuction(ctx context.Context, auctionID int64) error
	PauseAuction(ctx context.Context, auctionID int64) error
	ResumeAuction(ctx context.Context, auctionID int64) error
	EndAuction(ctx context.Context, auctionID int64) error

	// 竞价管理
	PlaceBid(ctx context.Context, itemID, userID int64, price float64) error
	ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error)
	ListBidsByAuction(ctx context.Context, auctionID int64, page, size int) ([]*model.BidWithDetails, int64, error)
	ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error)
	GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error)
}

// WebSocketService WebSocket服务接口
type WebSocketService interface {
	BroadcastBid(auctionID, itemID, userID int64, username string, price float64)
	BroadcastAuctionStatus(auctionID int64, status int8, message string)
	BroadcastPriceUpdate(auctionID, itemID int64, currentPrice float64, bidCount int64, highestBidder string)
	GetOnlineCount(auctionID int64) int
}

// auctionService 拍卖服务实现
type auctionService struct {
	auctionDAO dao.AuctionDAO
	productDAO dao.ProductDAO
	wsService  WebSocketService
}

// NewAuctionService 创建拍卖服务实例
func NewAuctionService() AuctionService {
	return &auctionService{
		auctionDAO: dao.NewAuctionDAO(),
		productDAO: dao.NewProductDAO(),
		wsService:  nil, // 将在main.go中注入
	}
}

// NewAuctionServiceWithWebSocket 创建带WebSocket的拍卖服务实例
func NewAuctionServiceWithWebSocket(wsService WebSocketService) AuctionService {
	return &auctionService{
		auctionDAO: dao.NewAuctionDAO(),
		productDAO: dao.NewProductDAO(),
		wsService:  wsService,
	}
}

// CreateAuction 创建拍卖会
func (s *auctionService) CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description, locationn string) (*model.Auction, error) {
	auction := &model.Auction{
		Name:         name,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       0, // 未开始
		AuctioneerID: auctioneerID,
		Description:  description,
		Location:     locationn,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuction(ctx, auction); err != nil {
		return nil, err
	}

	return auction, nil
}

// UpdateAuction 更新拍卖会
func (s *auctionService) UpdateAuction(ctx context.Context, id, auctioneerID int64, name, location string, startTime, endTime time.Time, description string) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始的拍卖会才能修改
	if auction.Status != 0 {
		return errors.New("只有未开始的拍卖会才能修改!")
	}

	auction.Name = name
	auction.StartTime = startTime
	auction.EndTime = endTime
	auction.Description = description
	auction.AuctioneerID = auctioneerID
	auction.Location = location
	auction.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// GetAuction 获取拍卖会信息
func (s *auctionService) GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error) {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	items, err := s.auctionDAO.ListAuctionItems(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取每个商品的详细信息
	itemDetails := make([]model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	user := &model.User{}
	if auction.AuctioneerID > 0 {
		userDAO := dao.NewUserDAO()
		user, _ = userDAO.FindByID(ctx, auction.AuctioneerID)

	}

	return &model.AuctionWithItems{
		Auction:    *auction,
		Items:      itemDetails,
		Auctioneer: *user,
	}, nil
}

// ListAuctions 分页查询拍卖会列表
func (s *auctionService) ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error) {
	offset := (page - 1) * size
	auctions, err := s.auctionDAO.ListAuctions(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.auctionDAO.CountAuctions(ctx)
	if err != nil {
		return nil, 0, err
	}

	return auctions, total, nil
}

// ListAuctionsWithFilter 带过滤条件查询拍卖会列表
func (s *auctionService) ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error) {
	return s.auctionDAO.ListAuctionsWithFilter(ctx, dao.AuctionQueryParams{
		Title:    params.Title,
		Status:   params.Status,
		Page:     params.Page,
		PageSize: params.PageSize,
	})
}

// UpdateAuctionStatus 更新拍卖会状态
func (s *auctionService) UpdateAuctionStatus(ctx context.Context, id int64, status int8) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	auction.Status = status
	auction.UpdatedAt = time.Now()
	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// AddAuctionItem 添加拍卖商品
func (s *auctionService) AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error) {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	item := &model.AuctionItem{
		AuctionID:    auctionID,
		ProductID:    productID,
		StartPrice:   startPrice,
		CurrentPrice: startPrice,
		StepPrice:    stepPrice,
		Status:       0, // 未开始
		StartTime:    startTime,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuctionItem(ctx, item); err != nil {
		return nil, err
	}

	return item, nil
}

// UpdateAuctionItem 更新拍卖商品
func (s *auctionService) UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 只有未开始的商品才能修改
	if item.Status != 0 {
		return ErrInvalidOperation
	}

	item.StartPrice = startPrice
	item.CurrentPrice = startPrice
	item.StepPrice = stepPrice
	item.StartTime = startTime
	item.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// GetAuctionItem 获取拍卖商品信息
func (s *auctionService) GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error) {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, ErrAuctionItemNotFound
	}

	product, err := s.productDAO.FindByID(ctx, item.ProductID)
	if err != nil {
		return nil, err
	}

	highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
	if err != nil {
		return nil, err
	}

	totalBids, err := s.auctionDAO.CountBidsByItem(ctx, id)
	if err != nil {
		return nil, err
	}

	return &model.AuctionItemDetail{
		AuctionItem: *item,
		Product:     *product,
		HighestBid:  highestBid,
		TotalBids:   totalBids,
	}, nil
}

// ListAuctionItems 查询拍卖商品列表
func (s *auctionService) ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error) {
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return nil, err
	}

	itemDetails := make([]*model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, &model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	return itemDetails, nil
}

// UpdateAuctionItemStatus 更新拍卖商品状态
func (s *auctionService) UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	item.Status = status
	item.UpdatedAt = time.Now()

	// 如果是成交状态，设置成交时间和中标用户
	if status == 2 {
		now := time.Now()
		item.EndTime = &now

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
		if err != nil {
			return err
		}
		if highestBid != nil {
			item.WinnerID = &highestBid.UserID
		}
	}

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// PlaceBid 出价
func (s *auctionService) PlaceBid(ctx context.Context, itemID, userID int64, price float64) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, itemID)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 检查拍卖状态
	if item.Status != 1 {
		if item.Status == 0 {
			return ErrAuctionNotStarted
		}
		return ErrAuctionEnded
	}

	// 检查出价是否有效
	if price < item.CurrentPrice+item.StepPrice {
		return ErrInvalidBidPrice
	}

	// 创建竞价记录
	bid := &model.Bid{
		AuctionItemID: itemID,
		UserID:        userID,
		Price:         price,
		Status:        1, // 有效
		CreatedAt:     time.Now(),
	}

	if err := s.auctionDAO.CreateBid(ctx, bid); err != nil {
		return err
	}

	// 更新当前价格
	item.CurrentPrice = price
	item.UpdatedAt = time.Now()
	if err := s.auctionDAO.UpdateAuctionItem(ctx, item); err != nil {
		return err
	}

	// 如果有WebSocket服务，广播竞价消息
	if s.wsService != nil {
		// 获取用户信息
		userDAO := dao.NewUserDAO()
		user, _ := userDAO.FindByID(ctx, userID)
		username := "匿名用户"
		if user != nil {
			username = user.RealName
			if username == "" {
				username = user.Username
			}
		}

		// 获取拍卖会ID
		auctionID := item.AuctionID

		// 广播竞价消息
		s.wsService.BroadcastBid(auctionID, itemID, userID, username, price)

		// 获取竞价次数
		totalBids, _ := s.auctionDAO.CountBidsByItem(ctx, itemID)

		// 广播价格更新
		s.wsService.BroadcastPriceUpdate(auctionID, itemID, price, totalBids, username)
	}

	return nil
}

// ListBids 查询竞价记录
func (s *auctionService) ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error) {
	return s.auctionDAO.ListBidsByItem(ctx, itemID)
}

// ListBidsByAuction 查询拍卖会的竞价记录
func (s *auctionService) ListBidsByAuction(ctx context.Context, auctionID int64, page, size int) ([]*model.BidWithDetails, int64, error) {
	offset := (page - 1) * size
	return s.auctionDAO.ListBidsByAuction(ctx, auctionID, offset, size)
}

// ListBidsByAuctionWithFilter 带过滤条件查询拍卖会的竞价记录
func (s *auctionService) ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error) {
	offset := (params.Page - 1) * params.PageSize
	return s.auctionDAO.ListBidsByAuctionWithFilter(ctx, auctionID, dao.BidQueryParams{
		Offset:      offset,
		Limit:       params.PageSize,
		ProductName: params.ProductName,
		Username:    params.Username,
		Status:      params.Status,
	})
}

// GetHighestBid 获取最高出价
func (s *auctionService) GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error) {
	return s.auctionDAO.GetHighestBid(ctx, itemID)
}

// DeleteAuction 删除拍卖会
func (s *auctionService) DeleteAuction(ctx context.Context, id int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始的拍卖会才能删除
	if auction.Status != 0 {
		return ErrInvalidOperation
	}

	// 检查是否有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, id)
	if err != nil {
		return err
	}

	// 如果有拍卖商品，先删除所有拍卖商品
	for _, item := range items {
		if err := s.auctionDAO.DeleteAuctionItem(ctx, item.ID); err != nil {
			return err
		}
	}

	// 删除拍卖会
	return s.auctionDAO.DeleteAuction(ctx, id)
}

// ListAllAuctionItems 查询所有拍卖商品列表
func (s *auctionService) ListAllAuctionItems(ctx context.Context, offset, limit int) ([]*model.AuctionItemDetail, int64, error) {
	// 首先获取所有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, 0) // 传入0表示获取所有拍卖会的商品
	if err != nil {
		return nil, 0, err
	}

	// 如果没有商品，直接返回
	if len(items) == 0 {
		return []*model.AuctionItemDetail{}, 0, nil
	}

	// 获取每个商品的详细信息
	itemDetails := make([]*model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		// 获取商品信息
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil || product == nil {
			continue // 如果商品不存在，跳过
		}

		// 获取最高出价
		highestBid, _ := s.auctionDAO.GetHighestBid(ctx, item.ID)

		// 获取出价次数
		totalBids, _ := s.auctionDAO.CountBidsByItem(ctx, item.ID)

		itemDetail := &model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		}
		itemDetails = append(itemDetails, itemDetail)
	}

	// 应用分页
	total := int64(len(itemDetails))
	start := offset
	end := offset + limit
	if start > len(itemDetails) {
		return []*model.AuctionItemDetail{}, total, nil
	}
	if end > len(itemDetails) {
		end = len(itemDetails)
	}

	return itemDetails[start:end], total, nil
}

// StartAuction 开始拍卖
func (s *auctionService) StartAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 0 {
		return errors.New("拍卖会已开始或已结束")
	}

	// 检查是否有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return err
	}
	if len(items) == 0 {
		return errors.New("拍卖会没有商品，无法开始")
	}

	// 更新拍卖会状态为进行中
	if err := s.UpdateAuctionStatus(ctx, auctionID, 1); err != nil {
		return err
	}

	// 更新所有拍卖商品状态为进行中
	for _, item := range items {
		if err := s.UpdateAuctionItemStatus(ctx, item.ID, 1); err != nil {
			return err
		}
	}

	// 如果有WebSocket服务，广播拍卖开始消息
	if s.wsService != nil {
		s.wsService.BroadcastAuctionStatus(auctionID, 1, "拍卖已开始")
	}

	return nil
}

// PauseAuction 暂停拍卖
func (s *auctionService) PauseAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 1 {
		return errors.New("拍卖会未在进行中，无法暂停")
	}

	// 更新拍卖会状态为暂停（使用状态3表示暂停）
	return s.UpdateAuctionStatus(ctx, auctionID, 3)
}

// ResumeAuction 恢复拍卖
func (s *auctionService) ResumeAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 3 {
		return errors.New("拍卖会未暂停，无法恢复")
	}

	// 更新拍卖会状态为进行中
	return s.UpdateAuctionStatus(ctx, auctionID, 1)
}

// EndAuction 结束拍卖
func (s *auctionService) EndAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status == 2 {
		return errors.New("拍卖会已结束")
	}
	if auction.Status == 0 {
		return errors.New("拍卖会未开始，无法结束")
	}

	// 获取所有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return err
	}

	// 处理每个拍卖商品的结束逻辑
	for _, item := range items {
		if item.Status == 1 { // 如果商品还在进行中
			// 检查是否有出价
			highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
			if err != nil {
				return err
			}

			if highestBid != nil {
				// 有出价，标记为成交
				if err := s.UpdateAuctionItemStatus(ctx, item.ID, 2); err != nil {
					return err
				}
			} else {
				// 无出价，标记为流拍
				if err := s.UpdateAuctionItemStatus(ctx, item.ID, 3); err != nil {
					return err
				}
			}
		}
	}

	// 更新拍卖会状态为已结束
	return s.UpdateAuctionStatus(ctx, auctionID, 2)
}
