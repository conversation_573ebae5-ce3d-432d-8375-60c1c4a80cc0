package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrAuctionNotFound     = errors.New("拍卖会不存在")
	ErrAuctionItemNotFound = errors.New("拍卖商品不存在")
	ErrAuctionEnded        = errors.New("拍卖已结束")
	ErrAuctionNotStarted   = errors.New("拍卖未开始")
	ErrInvalidBidPrice     = errors.New("出价无效")
)

// AuctionQueryParams 拍卖会查询参数
type AuctionQueryParams struct {
	Title    string `form:"title" json:"title"`       // 拍卖会标题
	Status   *int8  `form:"status" json:"status"`     // 状态
	Page     int    `form:"page" json:"page"`         // 页码
	PageSize int    `form:"pageSize" json:"pageSize"` // 每页数量
}

// BidQueryParams 竞价记录查询参数
type BidQueryParams struct {
	Page        int    `form:"page" json:"page"`               // 页码
	PageSize    int    `form:"pageSize" json:"pageSize"`       // 每页数量
	ProductName string `form:"productName" json:"productName"` // 商品名称
	Username    string `form:"username" json:"username"`       // 用户名
	Status      *int8  `form:"status" json:"status"`           // 状态
}

// TodayAuctionStats 今日拍卖统计
type TodayAuctionStats struct {
	AuctionCount  int     `json:"auctionCount"`  // 拍卖次数
	TotalTurnover float64 `json:"totalTurnover"` // 总成交额
}

// BatchStats 批次统计
type BatchStats struct {
	PendingCount   int `json:"pendingCount"`   // 待拍批次数
	CompletedCount int `json:"completedCount"` // 已完成批次数
}

// AuctionItemDetail 拍卖商品详情
type AuctionItemDetail struct {
	AuctionItem *model.AuctionItem `json:"auctionItem"`
	Product     *model.Product     `json:"product"`
	BidCount    int64              `json:"bidCount"`
	WatchCount  int64              `json:"watchCount"`
}

// AuctionService 拍卖服务接口
type AuctionService interface {
	// 拍卖会管理
	CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description, locationn string) (*model.Auction, error)
	UpdateAuction(ctx context.Context, id, auctioneerID int64, name, location string, startTime, endTime time.Time, description string) error
	GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error)
	ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error)
	ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error)
	UpdateAuctionStatus(ctx context.Context, id int64, status int8) error
	DeleteAuction(ctx context.Context, id int64) error

	// 拍卖商品管理
	AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error)
	UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error
	GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error)
	ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error)
	ListAllAuctionItems(ctx context.Context, offset, limit int) ([]*AuctionItemDetail, int64, error)
	UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error

	// 拍卖控制
	StartAuction(ctx context.Context, auctionID int64) error
	PauseAuction(ctx context.Context, auctionID int64) error
	ResumeAuction(ctx context.Context, auctionID int64) error
	EndAuction(ctx context.Context, auctionID int64) error
	CancelAuction(ctx context.Context, auctionID int64) error

	// 竞价管理
	PlaceBid(ctx context.Context, itemID, userID int64, price float64) error
	ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error)
	ListBidsByAuction(ctx context.Context, auctionID int64, page, size int) ([]*model.BidWithDetails, int64, error)
	ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error)
	GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error)

	// 统计和分析
	GetAuctionStatistics(ctx context.Context) (map[string]interface{}, error)
	GetAuctionParticipants(ctx context.Context, auctionID int64) ([]map[string]interface{}, error)

	// 拍卖师端专用统计
	GetActiveClocksCount(ctx context.Context) (int, error)
	GetTodayAuctionStats(ctx context.Context) (*TodayAuctionStats, error)
	GetBatchStats(ctx context.Context) (*BatchStats, error)

	// 关注功能
	AddToWatchlist(ctx context.Context, userID, itemID int64) error
	RemoveFromWatchlist(ctx context.Context, userID, itemID int64) error
	GetUserWatchlist(ctx context.Context, userID int64, offset, limit int) ([]*AuctionItemDetail, int64, error)
	IsItemWatched(ctx context.Context, userID, itemID int64) (bool, error)

	// 钟号管理
	GetClockStatus(ctx context.Context, clockNumber int) (string, error)
	GetCurrentItemByClock(ctx context.Context, clockNumber int) (*model.AuctionItem, error)
	GetItemBidCount(ctx context.Context, itemID int64) (int64, error)
	UpdateClockStatus(ctx context.Context, clockNumber int, status string) error

	// 拍卖师端专用查询
	GetAuctionItemsForAuctioneer(ctx context.Context, status string, clockNumber *int, offset, limit int) ([]*AuctionItemDetail, int64, error)

	// 购买商端专用查询
	GetUserBids(ctx context.Context, userID int64, offset, limit int) ([]*model.Bid, int64, error)
}

// WebSocketService WebSocket服务接口
type WebSocketService interface {
	BroadcastBid(auctionID, itemID, userID int64, username string, price float64)
	BroadcastAuctionStatus(auctionID int64, status int8, message string)
	BroadcastPriceUpdate(auctionID, itemID int64, currentPrice float64, bidCount int64, highestBidder string)
	GetOnlineCount(auctionID int64) int
}

// auctionService 拍卖服务实现
type auctionService struct {
	auctionDAO      dao.AuctionDAO
	productDAO      dao.ProductDAO
	wsService       WebSocketService
	realtimeService *RealtimeService
}

// NewAuctionService 创建拍卖服务实例
func NewAuctionService() AuctionService {
	return &auctionService{
		auctionDAO: dao.NewAuctionDAO(),
		productDAO: dao.NewProductDAO(),
		wsService:  nil, // 将在main.go中注入
	}
}

// NewAuctionServiceWithWebSocket 创建带WebSocket的拍卖服务实例
func NewAuctionServiceWithWebSocket(wsService WebSocketService) AuctionService {
	return &auctionService{
		auctionDAO: dao.NewAuctionDAO(),
		productDAO: dao.NewProductDAO(),
		wsService:  wsService,
	}
}

// CreateAuction 创建拍卖会
func (s *auctionService) CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description, locationn string) (*model.Auction, error) {
	auction := &model.Auction{
		Name:         name,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       0, // 未开始
		AuctioneerID: auctioneerID,
		Description:  description,
		Location:     locationn,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuction(ctx, auction); err != nil {
		return nil, err
	}

	return auction, nil
}

// UpdateAuction 更新拍卖会
func (s *auctionService) UpdateAuction(ctx context.Context, id, auctioneerID int64, name, location string, startTime, endTime time.Time, description string) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始的拍卖会才能修改
	if auction.Status != 0 {
		return errors.New("只有未开始的拍卖会才能修改!")
	}

	auction.Name = name
	auction.StartTime = startTime
	auction.EndTime = endTime
	auction.Description = description
	auction.AuctioneerID = auctioneerID
	auction.Location = location
	auction.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// GetAuction 获取拍卖会信息
func (s *auctionService) GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error) {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	items, err := s.auctionDAO.ListAuctionItems(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取每个商品的详细信息
	itemDetails := make([]model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	user := &model.User{}
	if auction.AuctioneerID > 0 {
		userDAO := dao.NewUserDAO()
		user, _ = userDAO.FindByID(ctx, auction.AuctioneerID)

	}

	return &model.AuctionWithItems{
		Auction:    *auction,
		Items:      itemDetails,
		Auctioneer: *user,
	}, nil
}

// ListAuctions 分页查询拍卖会列表
func (s *auctionService) ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error) {
	offset := (page - 1) * size
	auctions, err := s.auctionDAO.ListAuctions(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.auctionDAO.CountAuctions(ctx)
	if err != nil {
		return nil, 0, err
	}

	return auctions, total, nil
}

// ListAuctionsWithFilter 带过滤条件查询拍卖会列表
func (s *auctionService) ListAuctionsWithFilter(ctx context.Context, params AuctionQueryParams) ([]*model.Auction, int64, error) {
	return s.auctionDAO.ListAuctionsWithFilter(ctx, dao.AuctionQueryParams{
		Title:    params.Title,
		Status:   params.Status,
		Page:     params.Page,
		PageSize: params.PageSize,
	})
}

// UpdateAuctionStatus 更新拍卖会状态
func (s *auctionService) UpdateAuctionStatus(ctx context.Context, id int64, status int8) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	auction.Status = status
	auction.UpdatedAt = time.Now()
	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// AddAuctionItem 添加拍卖商品
func (s *auctionService) AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error) {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	item := &model.AuctionItem{
		AuctionID:    auctionID,
		ProductID:    productID,
		StartPrice:   startPrice,
		CurrentPrice: startPrice,
		StepPrice:    stepPrice,
		Status:       0, // 未开始
		StartTime:    startTime,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuctionItem(ctx, item); err != nil {
		return nil, err
	}

	return item, nil
}

// UpdateAuctionItem 更新拍卖商品
func (s *auctionService) UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 只有未开始的商品才能修改
	if item.Status != 0 {
		return ErrInvalidOperation
	}

	item.StartPrice = startPrice
	item.CurrentPrice = startPrice
	item.StepPrice = stepPrice
	item.StartTime = startTime
	item.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// GetAuctionItem 获取拍卖商品信息
func (s *auctionService) GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error) {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, ErrAuctionItemNotFound
	}

	product, err := s.productDAO.FindByID(ctx, item.ProductID)
	if err != nil {
		return nil, err
	}

	highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
	if err != nil {
		return nil, err
	}

	totalBids, err := s.auctionDAO.CountBidsByItem(ctx, id)
	if err != nil {
		return nil, err
	}

	return &model.AuctionItemDetail{
		AuctionItem: *item,
		Product:     *product,
		HighestBid:  highestBid,
		TotalBids:   totalBids,
	}, nil
}

// ListAuctionItems 查询拍卖商品列表
func (s *auctionService) ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error) {
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return nil, err
	}

	itemDetails := make([]*model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, &model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	return itemDetails, nil
}

// UpdateAuctionItemStatus 更新拍卖商品状态
func (s *auctionService) UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	item.Status = status
	item.UpdatedAt = time.Now()

	// 如果是成交状态，设置成交时间和中标用户
	if status == 2 {
		now := time.Now()
		item.EndTime = &now

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
		if err != nil {
			return err
		}
		if highestBid != nil {
			item.WinnerID = &highestBid.UserID
		}
	}

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// PlaceBid 出价
func (s *auctionService) PlaceBid(ctx context.Context, itemID, userID int64, price float64) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, itemID)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 检查拍卖状态
	if item.Status != 1 {
		if item.Status == 0 {
			return ErrAuctionNotStarted
		}
		return ErrAuctionEnded
	}

	// 检查出价是否有效
	if price < item.CurrentPrice+item.StepPrice {
		return ErrInvalidBidPrice
	}

	// 创建竞价记录
	bid := &model.Bid{
		AuctionItemID: itemID,
		UserID:        userID,
		Price:         price,
		Status:        1, // 有效
		CreatedAt:     time.Now(),
	}

	if err := s.auctionDAO.CreateBid(ctx, bid); err != nil {
		return err
	}

	// 更新当前价格
	item.CurrentPrice = price
	item.UpdatedAt = time.Now()
	if err := s.auctionDAO.UpdateAuctionItem(ctx, item); err != nil {
		return err
	}

	// 如果有WebSocket服务，广播竞价消息
	if s.wsService != nil {
		// 获取用户信息
		userDAO := dao.NewUserDAO()
		user, _ := userDAO.FindByID(ctx, userID)
		username := "匿名用户"
		if user != nil {
			username = user.RealName
			if username == "" {
				username = user.Username
			}
		}

		// 获取拍卖会ID
		auctionID := item.AuctionID

		// 广播竞价消息
		s.wsService.BroadcastBid(auctionID, itemID, userID, username, price)

		// 获取竞价次数
		totalBids, _ := s.auctionDAO.CountBidsByItem(ctx, itemID)

		// 广播价格更新
		s.wsService.BroadcastPriceUpdate(auctionID, itemID, price, totalBids, username)
	}

	return nil
}

// ListBids 查询竞价记录
func (s *auctionService) ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error) {
	return s.auctionDAO.ListBidsByItem(ctx, itemID)
}

// ListBidsByAuction 查询拍卖会的竞价记录
func (s *auctionService) ListBidsByAuction(ctx context.Context, auctionID int64, page, size int) ([]*model.BidWithDetails, int64, error) {
	offset := (page - 1) * size
	return s.auctionDAO.ListBidsByAuction(ctx, auctionID, offset, size)
}

// ListBidsByAuctionWithFilter 带过滤条件查询拍卖会的竞价记录
func (s *auctionService) ListBidsByAuctionWithFilter(ctx context.Context, auctionID int64, params BidQueryParams) ([]*model.BidWithDetails, int64, error) {
	offset := (params.Page - 1) * params.PageSize
	return s.auctionDAO.ListBidsByAuctionWithFilter(ctx, auctionID, dao.BidQueryParams{
		Offset:      offset,
		Limit:       params.PageSize,
		ProductName: params.ProductName,
		Username:    params.Username,
		Status:      params.Status,
	})
}

// GetHighestBid 获取最高出价
func (s *auctionService) GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error) {
	return s.auctionDAO.GetHighestBid(ctx, itemID)
}

// DeleteAuction 删除拍卖会
func (s *auctionService) DeleteAuction(ctx context.Context, id int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始的拍卖会才能删除
	if auction.Status != 0 {
		return ErrInvalidOperation
	}

	// 检查是否有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, id)
	if err != nil {
		return err
	}

	// 如果有拍卖商品，先删除所有拍卖商品
	for _, item := range items {
		if err := s.auctionDAO.DeleteAuctionItem(ctx, item.ID); err != nil {
			return err
		}
	}

	// 删除拍卖会
	return s.auctionDAO.DeleteAuction(ctx, id)
}

// ListAllAuctionItems 查询所有拍卖商品列表
func (s *auctionService) ListAllAuctionItems(ctx context.Context, offset, limit int) ([]*AuctionItemDetail, int64, error) {
	// 首先获取所有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, 0) // 传入0表示获取所有拍卖会的商品
	if err != nil {
		return nil, 0, err
	}

	// 如果没有商品，直接返回
	if len(items) == 0 {
		return []*AuctionItemDetail{}, 0, nil
	}

	// 获取每个商品的详细信息
	itemDetails := make([]*AuctionItemDetail, 0, len(items))
	for _, item := range items {
		// 获取商品信息
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil || product == nil {
			continue // 如果商品不存在，跳过
		}

		// 获取出价次数
		bidCount, _ := s.auctionDAO.CountBidsByItem(ctx, item.ID)

		// 获取关注数量（这里简化处理，实际应该有专门的关注表）
		watchCount := int64(0) // 暂时设为0

		itemDetail := &AuctionItemDetail{
			AuctionItem: item,
			Product:     product,
			BidCount:    bidCount,
			WatchCount:  watchCount,
		}
		itemDetails = append(itemDetails, itemDetail)
	}

	// 应用分页
	total := int64(len(itemDetails))
	start := offset
	end := offset + limit
	if start > len(itemDetails) {
		return []*AuctionItemDetail{}, total, nil
	}
	if end > len(itemDetails) {
		end = len(itemDetails)
	}

	return itemDetails[start:end], total, nil
}

// StartAuction 开始拍卖
func (s *auctionService) StartAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 0 {
		return errors.New("拍卖会已开始或已结束")
	}

	// 检查是否有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return err
	}
	if len(items) == 0 {
		return errors.New("拍卖会没有商品，无法开始")
	}

	// 更新拍卖会状态为进行中
	if err := s.UpdateAuctionStatus(ctx, auctionID, 1); err != nil {
		return err
	}

	// 更新所有拍卖商品状态为进行中
	for _, item := range items {
		if err := s.UpdateAuctionItemStatus(ctx, item.ID, 1); err != nil {
			return err
		}
	}

	// 如果有WebSocket服务，广播拍卖开始消息
	if s.wsService != nil {
		s.wsService.BroadcastAuctionStatus(auctionID, 1, "拍卖已开始")
	}

	return nil
}

// PauseAuction 暂停拍卖
func (s *auctionService) PauseAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 1 {
		return errors.New("拍卖会未在进行中，无法暂停")
	}

	// 更新拍卖会状态为暂停（使用状态3表示暂停）
	return s.UpdateAuctionStatus(ctx, auctionID, 3)
}

// ResumeAuction 恢复拍卖
func (s *auctionService) ResumeAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status != 3 {
		return errors.New("拍卖会未暂停，无法恢复")
	}

	// 更新拍卖会状态为进行中
	return s.UpdateAuctionStatus(ctx, auctionID, 1)
}

// EndAuction 结束拍卖
func (s *auctionService) EndAuction(ctx context.Context, auctionID int64) error {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 检查拍卖会状态
	if auction.Status == 2 {
		return errors.New("拍卖会已结束")
	}
	if auction.Status == 0 {
		return errors.New("拍卖会未开始，无法结束")
	}

	// 获取所有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return err
	}

	// 处理每个拍卖商品的结束逻辑
	for _, item := range items {
		if item.Status == 1 { // 如果商品还在进行中
			// 检查是否有出价
			highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
			if err != nil {
				return err
			}

			if highestBid != nil {
				// 有出价，标记为成交
				if err := s.UpdateAuctionItemStatus(ctx, item.ID, 2); err != nil {
					return err
				}
			} else {
				// 无出价，标记为流拍
				if err := s.UpdateAuctionItemStatus(ctx, item.ID, 3); err != nil {
					return err
				}
			}
		}
	}

	// 更新拍卖会状态为已结束
	return s.UpdateAuctionStatus(ctx, auctionID, 2)
}

// CancelAuction 取消拍卖会
func (s *auctionService) CancelAuction(ctx context.Context, auctionID int64) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始或进行中的拍卖会才能取消
	if auction.Status != 0 && auction.Status != 1 {
		return errors.New("只有未开始或进行中的拍卖会才能取消")
	}

	// 取消所有拍卖商品
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return err
	}

	for _, item := range items {
		if item.Status == 0 || item.Status == 1 {
			// 将商品状态设置为已取消
			if err := s.UpdateAuctionItemStatus(ctx, item.ID, 4); err != nil {
				return err
			}
		}
	}

	// 更新拍卖会状态为已取消
	return s.UpdateAuctionStatus(ctx, auctionID, 3)
}

// GetAuctionStatistics 获取拍卖统计信息
func (s *auctionService) GetAuctionStatistics(ctx context.Context) (map[string]interface{}, error) {
	// 获取总拍卖会数量
	totalAuctions, err := s.auctionDAO.CountAuctions(ctx)
	if err != nil {
		return nil, err
	}

	// 获取进行中的拍卖会数量
	activeAuctions, err := s.auctionDAO.CountAuctionsByStatus(ctx, 1)
	if err != nil {
		return nil, err
	}

	// 获取已完成的拍卖会数量
	completedAuctions, err := s.auctionDAO.CountAuctionsByStatus(ctx, 2)
	if err != nil {
		return nil, err
	}

	// 获取总成交金额
	totalBidAmount, err := s.auctionDAO.GetTotalBidAmount(ctx)
	if err != nil {
		return nil, err
	}

	// 获取参与者总数
	totalParticipants, err := s.auctionDAO.CountUniqueParticipants(ctx)
	if err != nil {
		return nil, err
	}

	// 计算平均成交金额
	averageBidAmount := 0.0
	if completedAuctions > 0 {
		averageBidAmount = totalBidAmount / float64(completedAuctions)
	}

	return map[string]interface{}{
		"totalAuctions":     totalAuctions,
		"activeAuctions":    activeAuctions,
		"completedAuctions": completedAuctions,
		"totalBidAmount":    totalBidAmount,
		"totalParticipants": totalParticipants,
		"averageBidAmount":  averageBidAmount,
	}, nil
}

// GetAuctionParticipants 获取拍卖参与者列表
func (s *auctionService) GetAuctionParticipants(ctx context.Context, auctionID int64) ([]map[string]interface{}, error) {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	// 获取参与者信息
	participants, err := s.auctionDAO.GetAuctionParticipants(ctx, auctionID)
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, len(participants))
	for i, participant := range participants {
		result[i] = map[string]interface{}{
			"id":             participant.UserID,
			"userName":       participant.Username,
			"realName":       participant.RealName,
			"joinTime":       participant.FirstBidTime.Format("2006-01-02 15:04:05"),
			"bidCount":       participant.BidCount,
			"totalBidAmount": participant.TotalBidAmount,
		}
	}

	return result, nil
}

// GetActiveClocksCount 获取活跃钟号数量
func (s *auctionService) GetActiveClocksCount(ctx context.Context) (int, error) {
	// 查询状态为活跃(1)的钟号数量
	count, err := s.auctionDAO.CountActiveClocks(ctx)
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetTodayAuctionStats 获取今日拍卖统计
func (s *auctionService) GetTodayAuctionStats(ctx context.Context) (*TodayAuctionStats, error) {
	// 获取今日开始和结束时间
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	// 获取今日拍卖次数
	auctionCount, err := s.auctionDAO.CountAuctionsByDateRange(ctx, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	// 获取今日成交总额
	totalTurnover, err := s.auctionDAO.GetTurnoverByDateRange(ctx, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	return &TodayAuctionStats{
		AuctionCount:  int(auctionCount),
		TotalTurnover: totalTurnover,
	}, nil
}

// GetBatchStats 获取批次统计
func (s *auctionService) GetBatchStats(ctx context.Context) (*BatchStats, error) {
	// 获取待拍批次数量 (status = 0)
	pendingCount, err := s.auctionDAO.CountBatchesByStatus(ctx, 0)
	if err != nil {
		return nil, err
	}

	// 获取已完成批次数量 (status = 2)
	completedCount, err := s.auctionDAO.CountBatchesByStatus(ctx, 2)
	if err != nil {
		return nil, err
	}

	return &BatchStats{
		PendingCount:   int(pendingCount),
		CompletedCount: int(completedCount),
	}, nil
}

// AddToWatchlist 添加到关注列表
func (s *auctionService) AddToWatchlist(ctx context.Context, userID, itemID int64) error {
	// 检查商品是否存在
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, itemID)
	if err != nil {
		return err
	}
	if item == nil {
		return errors.New("拍卖商品不存在")
	}

	// 检查是否已经关注
	isWatched, err := s.IsItemWatched(ctx, userID, itemID)
	if err != nil {
		return err
	}
	if isWatched {
		return errors.New("已经关注过该商品")
	}

	// 添加关注记录
	return s.auctionDAO.AddWatchlistItem(ctx, userID, itemID)
}

// RemoveFromWatchlist 从关注列表移除
func (s *auctionService) RemoveFromWatchlist(ctx context.Context, userID, itemID int64) error {
	// 检查是否已关注
	isWatched, err := s.IsItemWatched(ctx, userID, itemID)
	if err != nil {
		return err
	}
	if !isWatched {
		return errors.New("未关注该商品")
	}

	// 移除关注记录
	return s.auctionDAO.RemoveWatchlistItem(ctx, userID, itemID)
}

// GetUserWatchlist 获取用户关注列表
func (s *auctionService) GetUserWatchlist(ctx context.Context, userID int64, offset, limit int) ([]*AuctionItemDetail, int64, error) {
	// 获取用户关注的商品ID列表
	itemIDs, total, err := s.auctionDAO.GetUserWatchlistItemIDs(ctx, userID, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	if len(itemIDs) == 0 {
		return []*AuctionItemDetail{}, 0, nil
	}

	// 获取商品详情
	result := make([]*AuctionItemDetail, 0, len(itemIDs))
	for _, itemID := range itemIDs {
		item, err := s.auctionDAO.FindAuctionItemByID(ctx, itemID)
		if err != nil || item == nil {
			continue
		}

		// 获取商品信息
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil || product == nil {
			continue
		}

		// 获取出价次数
		bidCount, _ := s.auctionDAO.CountBidsByItem(ctx, item.ID)

		// 获取关注数量
		watchCount, _ := s.auctionDAO.CountWatchlistByItemID(ctx, item.ID)

		detail := &AuctionItemDetail{
			AuctionItem: item,
			Product:     product,
			BidCount:    bidCount,
			WatchCount:  watchCount,
		}
		result = append(result, detail)
	}

	return result, total, nil
}

// IsItemWatched 检查商品是否被用户关注
func (s *auctionService) IsItemWatched(ctx context.Context, userID, itemID int64) (bool, error) {
	return s.auctionDAO.IsItemWatched(ctx, userID, itemID)
}

// GetClockStatus 获取钟号状态
func (s *auctionService) GetClockStatus(ctx context.Context, clockNumber int) (string, error) {
	// 从钟号状态表获取状态
	status, err := s.auctionDAO.GetClockStatusByNumber(ctx, clockNumber)
	if err != nil {
		return "idle", err
	}
	return status, nil
}

// GetCurrentItemByClock 获取钟号当前拍卖商品
func (s *auctionService) GetCurrentItemByClock(ctx context.Context, clockNumber int) (*model.AuctionItem, error) {
	// 查找分配到该钟号且状态为进行中的商品
	return s.auctionDAO.GetCurrentItemByClock(ctx, clockNumber)
}

// GetItemBidCount 获取商品竞价次数
func (s *auctionService) GetItemBidCount(ctx context.Context, itemID int64) (int64, error) {
	return s.auctionDAO.CountBidsByItem(ctx, itemID)
}

// UpdateClockStatus 更新钟号状态
func (s *auctionService) UpdateClockStatus(ctx context.Context, clockNumber int, status string) error {
	// 验证状态值
	validStatuses := map[string]bool{
		"idle":   true,
		"active": true,
		"paused": true,
		"error":  true,
	}

	if !validStatuses[status] {
		return errors.New("无效的钟号状态")
	}

	// 更新钟号状态
	return s.auctionDAO.UpdateClockStatus(ctx, clockNumber, status)
}

// GetAuctionItemsForAuctioneer 获取拍卖师端的拍卖商品列表
func (s *auctionService) GetAuctionItemsForAuctioneer(ctx context.Context, status string, clockNumber *int, offset, limit int) ([]*AuctionItemDetail, int64, error) {
	// 构建查询条件
	var statusCode *int8
	if status != "" {
		switch status {
		case "pending":
			code := int8(0)
			statusCode = &code
		case "active":
			code := int8(1)
			statusCode = &code
		case "completed":
			code := int8(2)
			statusCode = &code
		case "paused":
			code := int8(3)
			statusCode = &code
		}
	}

	// 获取拍卖商品列表
	items, total, err := s.auctionDAO.GetAuctionItemsWithFilter(ctx, statusCode, clockNumber, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	// 转换为AuctionItemDetail
	result := make([]*AuctionItemDetail, 0, len(items))
	for _, item := range items {
		// 获取商品信息
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			continue // 跳过获取失败的商品
		}

		// 获取竞价数量
		bidCount, _ := s.auctionDAO.CountBidsByItem(ctx, item.ID)

		// 获取关注数量
		watchCount, _ := s.auctionDAO.CountWatchlistByItemID(ctx, item.ID)

		detail := &AuctionItemDetail{
			AuctionItem: item,
			Product:     product,
			BidCount:    bidCount,
			WatchCount:  watchCount,
		}
		result = append(result, detail)
	}

	return result, total, nil
}

// GetUserBids 获取用户的竞价记录
func (s *auctionService) GetUserBids(ctx context.Context, userID int64, offset, limit int) ([]*model.Bid, int64, error) {
	return s.auctionDAO.GetBidsByUserID(ctx, userID, offset, limit)
}
