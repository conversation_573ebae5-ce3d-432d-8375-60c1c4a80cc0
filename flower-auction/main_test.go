package main

import (
	"fmt"
	"testing"

	"golang.org/x/crypto/bcrypt"
)

func TestMain(t *testing.T) {
	main()
}

// TestGereratePassword 测试密码生成和验证功能
// 1. 使用 bcrypt 对密码进行哈希处理
// 2. 打印原始密码和生成的哈希值
// 3. 验证哈希值与原始密码是否匹配
// 测试通过标准：能够成功生成哈希并通过密码验证
func TestGereratePassword(t *testing.T) {
	password := "admin123"

	// 生成bcrypt哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		fmt.Printf("生成密码哈希失败: %v\n", err)
		return
	}

	fmt.Printf("密码: %s\n", password)
	fmt.Printf("哈希: %s\n", string(hashedPassword))

	// 验证哈希是否正确
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	if err == nil {
		fmt.Println("✅ 密码验证成功")
	} else {
		fmt.Printf("❌ 密码验证失败: %v\n", err)
	}
}
