#!/bin/bash

# 花卉拍卖系统数据库迁移脚本
# 使用方法: ./scripts/migrate.sh [环境] [操作]
# 环境: dev|test|prod
# 操作: init|migrate|rollback|status

set -e

# 默认配置
DEFAULT_ENV="dev"
DEFAULT_ACTION="migrate"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MIGRATION_DIR="$PROJECT_ROOT/docs/database/migration"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统数据库迁移工具"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境 (默认)"
    echo "  test    测试环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  init      初始化数据库"
    echo "  migrate   执行迁移 (默认)"
    echo "  rollback  回滚迁移"
    echo "  status    查看迁移状态"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev init     # 初始化开发环境数据库"
    echo "  $0 test migrate # 执行测试环境迁移"
    echo "  $0 prod status  # 查看生产环境迁移状态"
}

# 加载环境配置
load_config() {
    local env=$1
    
    case $env in
        "dev")
            DB_HOST="localhost"
            DB_PORT="3306"
            DB_USER="root"
            DB_PASSWORD="123456"
            DB_NAME="flower_auction"
            ;;
        "test")
            DB_HOST="localhost"
            DB_PORT="3306"
            DB_USER="test_user"
            DB_PASSWORD="test_password"
            DB_NAME="flower_auction_test"
            ;;
        "prod")
            # 生产环境配置从环境变量读取
            DB_HOST="${PROD_DB_HOST:-localhost}"
            DB_PORT="${PROD_DB_PORT:-3306}"
            DB_USER="${PROD_DB_USER:-root}"
            DB_PASSWORD="${PROD_DB_PASSWORD}"
            DB_NAME="${PROD_DB_NAME:-flower_auction}"
            
            if [ -z "$DB_PASSWORD" ]; then
                log_error "生产环境必须设置 PROD_DB_PASSWORD 环境变量"
                exit 1
            fi
            ;;
        *)
            log_error "不支持的环境: $env"
            exit 1
            ;;
    esac
    
    # MySQL连接字符串
    MYSQL_CMD="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD"
    
    log_info "环境配置: $env"
    log_info "数据库: $DB_HOST:$DB_PORT/$DB_NAME"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装"
        exit 1
    fi
    
    if ! $MYSQL_CMD -e "SELECT 1;" &> /dev/null; then
        log_error "无法连接到MySQL数据库"
        log_error "请检查数据库配置和网络连接"
        exit 1
    fi
    
    log_success "MySQL连接正常"
}

# 创建迁移记录表
create_migration_table() {
    log_info "创建迁移记录表..."
    
    $MYSQL_CMD -e "
    CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    USE $DB_NAME;
    CREATE TABLE IF NOT EXISTS schema_migrations (
        version VARCHAR(255) NOT NULL PRIMARY KEY,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        execution_time_ms INT DEFAULT 0,
        checksum VARCHAR(64) DEFAULT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    "
    
    log_success "迁移记录表创建完成"
}

# 获取已应用的迁移
get_applied_migrations() {
    $MYSQL_CMD -D$DB_NAME -e "SELECT version FROM schema_migrations ORDER BY version;" 2>/dev/null | tail -n +2 || true
}

# 获取待应用的迁移文件
get_pending_migrations() {
    local applied_migrations=$(get_applied_migrations)
    
    for file in "$MIGRATION_DIR"/V*.sql; do
        if [ -f "$file" ]; then
            local version=$(basename "$file" .sql)
            if ! echo "$applied_migrations" | grep -q "^$version$"; then
                echo "$file"
            fi
        fi
    done
}

# 计算文件校验和
calculate_checksum() {
    local file=$1
    if command -v sha256sum &> /dev/null; then
        sha256sum "$file" | cut -d' ' -f1
    elif command -v shasum &> /dev/null; then
        shasum -a 256 "$file" | cut -d' ' -f1
    else
        # 如果没有校验和工具，返回空
        echo ""
    fi
}

# 执行单个迁移文件
execute_migration() {
    local file=$1
    local version=$(basename "$file" .sql)
    local checksum=$(calculate_checksum "$file")
    
    log_info "执行迁移: $version"
    
    local start_time=$(date +%s%3N)
    
    if $MYSQL_CMD -D$DB_NAME < "$file"; then
        local end_time=$(date +%s%3N)
        local execution_time=$((end_time - start_time))
        
        # 记录迁移
        $MYSQL_CMD -D$DB_NAME -e "
        INSERT INTO schema_migrations (version, execution_time_ms, checksum) 
        VALUES ('$version', $execution_time, '$checksum');
        "
        
        log_success "迁移完成: $version (${execution_time}ms)"
    else
        log_error "迁移失败: $version"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    check_mysql_connection
    create_migration_table
    
    # 执行所有迁移
    migrate_database
    
    log_success "数据库初始化完成"
}

# 执行迁移
migrate_database() {
    log_info "开始数据库迁移..."
    
    check_mysql_connection
    create_migration_table
    
    local pending_migrations=$(get_pending_migrations)
    
    if [ -z "$pending_migrations" ]; then
        log_info "没有待执行的迁移"
        return
    fi
    
    log_info "发现 $(echo "$pending_migrations" | wc -l) 个待执行的迁移"
    
    echo "$pending_migrations" | while read -r file; do
        execute_migration "$file"
    done
    
    log_success "数据库迁移完成"
}

# 查看迁移状态
show_migration_status() {
    log_info "查看迁移状态..."
    
    check_mysql_connection
    
    echo ""
    echo "已应用的迁移:"
    echo "----------------------------------------"
    
    $MYSQL_CMD -D$DB_NAME -e "
    SELECT 
        version,
        applied_at,
        CONCAT(execution_time_ms, 'ms') as execution_time
    FROM schema_migrations 
    ORDER BY version;
    " 2>/dev/null || log_warning "无法获取迁移记录"
    
    echo ""
    echo "待应用的迁移:"
    echo "----------------------------------------"
    
    local pending=$(get_pending_migrations)
    if [ -z "$pending" ]; then
        echo "无待应用的迁移"
    else
        echo "$pending" | while read -r file; do
            echo "$(basename "$file" .sql)"
        done
    fi
    
    echo ""
}

# 回滚迁移（简单实现）
rollback_migration() {
    log_warning "回滚功能需要手动实现"
    log_warning "请根据具体迁移内容编写回滚脚本"
    
    show_migration_status
}

# 主函数
main() {
    local env=${1:-$DEFAULT_ENV}
    local action=${2:-$DEFAULT_ACTION}
    
    case $action in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "init")
            load_config "$env"
            init_database
            ;;
        "migrate")
            load_config "$env"
            migrate_database
            ;;
        "rollback")
            load_config "$env"
            rollback_migration
            ;;
        "status")
            load_config "$env"
            show_migration_status
            ;;
        *)
            log_error "不支持的操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
