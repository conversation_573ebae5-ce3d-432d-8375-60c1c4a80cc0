#!/bin/bash

# 花卉拍卖系统 - 微服务编译脚本
# 编译所有微服务为独立的可执行文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/bin"

# 微服务配置
declare -A SERVICES=(
    ["auth-service"]="认证服务"
    ["user-service"]="用户服务"
    ["gateway"]="API网关"
    ["main-service"]="主服务"
)

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨"
    echo "🔨                                                      🔨"
    echo "🔨           花卉拍卖系统 - 微服务编译脚本              🔨"
    echo "🔨                                                      🔨"
    echo "🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨🔨"
    echo -e "${NC}"
    echo
    log_info "项目根目录: $PROJECT_ROOT"
    log_info "构建目录: $BUILD_DIR"
    echo
}

# 检查环境
check_environment() {
    log_header "🔍 检查编译环境..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在PATH中"
        exit 1
    fi
    
    local go_version=$(go version | cut -d' ' -f3)
    log_success "Go 环境正常: $go_version"
    
    # 检查Go模块
    if [ ! -f "$PROJECT_ROOT/go.mod" ]; then
        log_error "go.mod 文件不存在"
        exit 1
    fi
    
    log_success "Go模块文件存在"
    echo
}

# 清理构建目录
clean_build_dir() {
    log_header "🧹 清理构建目录..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_success "已清理旧的构建文件"
    fi
    
    mkdir -p "$BUILD_DIR"
    log_success "创建构建目录: $BUILD_DIR"
    echo
}

# 下载依赖
download_dependencies() {
    log_header "📦 下载依赖..."
    
    cd "$PROJECT_ROOT"
    
    log_info "执行 go mod download..."
    go mod download
    
    log_info "执行 go mod tidy..."
    go mod tidy
    
    log_success "依赖下载完成"
    echo
}

# 编译单个服务
build_service() {
    local service_name=$1
    local service_desc=$2
    local source_path=""
    local binary_name=""
    
    # 确定源文件路径和二进制文件名
    if [ "$service_name" = "main-service" ]; then
        source_path="$PROJECT_ROOT/main.go"
        binary_name="main-service"
    else
        source_path="$PROJECT_ROOT/cmd/$service_name/main.go"
        binary_name="$service_name"
    fi
    
    log_info "编译 $service_desc ($service_name)..."
    
    # 检查源文件是否存在
    if [ ! -f "$source_path" ]; then
        log_warning "源文件不存在: $source_path，跳过编译"
        return 0
    fi
    
    # 编译参数
    local output_path="$BUILD_DIR/$binary_name"
    local build_time=$(date -u '+%Y-%m-%d_%H:%M:%S_UTC')
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local version="v1.0.0"
    
    # 构建ldflags
    local ldflags="-X main.Version=$version -X main.BuildTime=$build_time -X main.GitCommit=$git_commit"
    
    # 执行编译
    if go build -ldflags "$ldflags" -o "$output_path" "$source_path"; then
        log_success "✅ $service_desc 编译成功"
        
        # 显示文件信息
        local file_size=$(ls -lh "$output_path" | awk '{print $5}')
        log_info "   输出文件: $output_path"
        log_info "   文件大小: $file_size"
        
        # 设置执行权限
        chmod +x "$output_path"
        
        return 0
    else
        log_error "❌ $service_desc 编译失败"
        return 1
    fi
}

# 编译所有服务
build_all_services() {
    log_header "🔨 开始编译所有微服务..."
    
    local success_count=0
    local total_count=${#SERVICES[@]}
    local failed_services=()
    
    for service_name in "${!SERVICES[@]}"; do
        local service_desc=${SERVICES[$service_name]}
        
        if build_service "$service_name" "$service_desc"; then
            ((success_count++))
        else
            failed_services+=("$service_name")
        fi
        echo
    done
    
    # 显示编译结果
    log_header "📊 编译结果统计"
    log_info "总服务数: $total_count"
    log_success "编译成功: $success_count"
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "编译失败: ${#failed_services[@]}"
        log_error "失败的服务: ${failed_services[*]}"
        echo
        return 1
    else
        log_success "🎉 所有服务编译成功！"
        echo
        return 0
    fi
}

# 显示编译结果
show_build_results() {
    log_header "📋 编译产物列表"
    
    if [ ! -d "$BUILD_DIR" ] || [ -z "$(ls -A "$BUILD_DIR" 2>/dev/null)" ]; then
        log_warning "构建目录为空"
        return
    fi
    
    echo "┌─────────────────────┬─────────────────┬─────────────────┐"
    echo "│       服务名        │     文件大小    │     修改时间    │"
    echo "├─────────────────────┼─────────────────┼─────────────────┤"
    
    for file in "$BUILD_DIR"/*; do
        if [ -f "$file" ] && [ -x "$file" ]; then
            local filename=$(basename "$file")
            local filesize=$(ls -lh "$file" | awk '{print $5}')
            local modtime=$(ls -l "$file" | awk '{print $6, $7, $8}')
            
            printf "│ %-19s │ %-15s │ %-15s │\n" "$filename" "$filesize" "$modtime"
        fi
    done
    
    echo "└─────────────────────┴─────────────────┴─────────────────┘"
    echo
}

# 生成启动脚本
generate_start_scripts() {
    log_header "📝 生成启动脚本..."
    
    # 为每个服务生成启动脚本
    for service_name in "${!SERVICES[@]}"; do
        local service_desc=${SERVICES[$service_name]}
        local binary_path="$BUILD_DIR/$service_name"
        
        if [ -f "$binary_path" ]; then
            local script_path="$BUILD_DIR/start-$service_name.sh"
            
            cat > "$script_path" << EOF
#!/bin/bash
# $service_desc 启动脚本

set -e

SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
BINARY_PATH="\$SCRIPT_DIR/$service_name"
CONFIG_PATH="\$SCRIPT_DIR/../configs/$service_name.yaml"

echo "🚀 启动 $service_desc..."
echo "📍 二进制文件: \$BINARY_PATH"
echo "📍 配置文件: \$CONFIG_PATH"

if [ ! -f "\$BINARY_PATH" ]; then
    echo "❌ 二进制文件不存在: \$BINARY_PATH"
    exit 1
fi

if [ ! -f "\$CONFIG_PATH" ]; then
    echo "⚠️  配置文件不存在: \$CONFIG_PATH，使用默认配置"
    exec "\$BINARY_PATH" "\$@"
else
    exec "\$BINARY_PATH" -config="\$CONFIG_PATH" "\$@"
fi
EOF
            
            chmod +x "$script_path"
            log_success "生成启动脚本: start-$service_name.sh"
        fi
    done
    
    echo
}

# 显示使用说明
show_usage() {
    log_header "📖 使用说明"
    
    echo "编译完成后，您可以使用以下方式启动服务："
    echo
    echo "1. 使用二进制文件直接启动："
    echo "   cd $BUILD_DIR"
    echo "   ./auth-service -config=../configs/auth-service.yaml"
    echo "   ./user-service -config=../configs/user-service.yaml"
    echo "   ./gateway -config=../configs/gateway.yaml"
    echo
    echo "2. 使用生成的启动脚本："
    echo "   cd $BUILD_DIR"
    echo "   ./start-auth-service.sh"
    echo "   ./start-user-service.sh"
    echo "   ./start-gateway.sh"
    echo
    echo "3. 使用微服务启动脚本："
    echo "   ./scripts/start-microservices.sh"
    echo
    echo "4. 查看服务版本信息："
    echo "   ./bin/auth-service -version"
    echo
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统微服务编译脚本"
    echo
    echo "用法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  build    编译所有微服务 (默认)"
    echo "  clean    清理构建目录"
    echo "  help     显示帮助信息"
    echo
    echo "示例:"
    echo "  $0           # 编译所有微服务"
    echo "  $0 build     # 编译所有微服务"
    echo "  $0 clean     # 清理构建目录"
}

# 主函数
main() {
    local command=${1:-build}
    
    case $command in
        "build"|"")
            show_banner
            check_environment
            clean_build_dir
            download_dependencies
            
            if build_all_services; then
                show_build_results
                generate_start_scripts
                show_usage
                log_success "🎉 编译流程完成！"
            else
                log_error "❌ 编译流程失败！"
                exit 1
            fi
            ;;
        "clean")
            log_info "清理构建目录..."
            if [ -d "$BUILD_DIR" ]; then
                rm -rf "$BUILD_DIR"
                log_success "构建目录已清理"
            else
                log_info "构建目录不存在，无需清理"
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
