-- 财务系统测试数据初始化脚本

-- 使用订单数据库（财务功能通常与订单相关）
USE order_db;

-- 创建财务账户表
CREATE TABLE IF NOT EXISTS finance_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_type TINYINT NOT NULL COMMENT '账户类型：1-保证金账户，2-交易账户，3-佣金账户',
    balance DECIMAL(15,2) DEFAULT 0.00 COMMENT '账户余额',
    frozen_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '冻结金额',
    total_income DECIMAL(15,2) DEFAULT 0.00 COMMENT '总收入',
    total_expense DECIMAL(15,2) DEFAULT 0.00 COMMENT '总支出',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_type (user_id, account_type),
    INDEX idx_user_id (user_id),
    INDEX idx_account_type (account_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务账户表';

-- 创建交易记录表
CREATE TABLE IF NOT EXISTS finance_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_no VARCHAR(32) NOT NULL UNIQUE COMMENT '交易流水号',
    user_id INT NOT NULL COMMENT '用户ID',
    type TINYINT NOT NULL COMMENT '交易类型：1-充值，2-提现，3-拍卖支付，4-拍卖退款，5-佣金收入，6-佣金支出',
    amount DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    status TINYINT DEFAULT 0 COMMENT '交易状态：0-待处理，1-成功，2-失败，3-已取消',
    description VARCHAR(255) NOT NULL COMMENT '交易描述',
    related_order_id INT NULL COMMENT '关联订单ID',
    related_order_no VARCHAR(32) NULL COMMENT '关联订单号',
    payment_method VARCHAR(50) NULL COMMENT '支付方式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_transaction_no (transaction_no),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_related_order (related_order_id, related_order_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务交易记录表';

-- 创建佣金记录表
CREATE TABLE IF NOT EXISTS finance_commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL COMMENT '订单号',
    seller_id INT NOT NULL COMMENT '卖家ID',
    seller_name VARCHAR(100) NOT NULL COMMENT '卖家名称',
    buyer_id INT NOT NULL COMMENT '买家ID',
    buyer_name VARCHAR(100) NOT NULL COMMENT '买家名称',
    order_amount DECIMAL(15,2) NOT NULL COMMENT '订单金额',
    commission_rate DECIMAL(5,4) NOT NULL COMMENT '佣金比例',
    commission_amount DECIMAL(15,2) NOT NULL COMMENT '佣金金额',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待结算，1-已结算，2-已取消',
    settled_at TIMESTAMP NULL COMMENT '结算时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_order (order_id),
    INDEX idx_order_no (order_no),
    INDEX idx_seller_id (seller_id),
    INDEX idx_buyer_id (buyer_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表';

-- 清理现有数据
DELETE FROM finance_transactions WHERE id > 0;
DELETE FROM finance_commissions WHERE id > 0;
DELETE FROM finance_accounts WHERE id > 0;

-- 重置自增ID
ALTER TABLE finance_accounts AUTO_INCREMENT = 1;
ALTER TABLE finance_transactions AUTO_INCREMENT = 1;
ALTER TABLE finance_commissions AUTO_INCREMENT = 1;

-- 插入账户数据
INSERT INTO finance_accounts (user_id, account_type, balance, frozen_amount, total_income, total_expense, created_at, updated_at) VALUES
(1, 1, 50000.00, 5000.00, 80000.00, 30000.00, NOW(), NOW()),  -- 保证金账户
(1, 2, 125000.00, 0.00, 200000.00, 75000.00, NOW(), NOW()),   -- 交易账户
(1, 3, 15000.00, 0.00, 25000.00, 10000.00, NOW(), NOW()),     -- 佣金账户
(2, 1, 30000.00, 2000.00, 45000.00, 15000.00, NOW(), NOW()),  -- 用户2保证金账户
(2, 2, 85000.00, 0.00, 120000.00, 35000.00, NOW(), NOW()),    -- 用户2交易账户
(3, 1, 25000.00, 1000.00, 35000.00, 10000.00, NOW(), NOW()),  -- 用户3保证金账户
(3, 2, 65000.00, 0.00, 90000.00, 25000.00, NOW(), NOW());     -- 用户3交易账户

-- 插入交易记录数据
INSERT INTO finance_transactions (transaction_no, user_id, type, amount, status, description, related_order_id, related_order_no, payment_method, created_at, updated_at) VALUES
-- 充值记录
('TXN202312010001', 1, 1, 50000.00, 1, '账户充值', NULL, NULL, '支付宝', '2023-12-01 09:15:00', '2023-12-01 09:15:10'),
('TXN202312010002', 2, 1, 30000.00, 1, '账户充值', NULL, NULL, '银行转账', '2023-12-01 10:30:00', '2023-12-01 10:30:15'),
('TXN202312010003', 3, 1, 25000.00, 1, '账户充值', NULL, NULL, '微信支付', '2023-12-01 11:45:00', '2023-12-01 11:45:08'),

-- 拍卖支付记录
('TXN202312010004', 1, 3, 15000.00, 1, '拍卖支付 - 玫瑰花批次#001', 1, 'ORD202312010001', '余额支付', '2023-12-01 14:20:00', '2023-12-01 14:20:05'),
('TXN202312010005', 2, 3, 8500.00, 1, '拍卖支付 - 康乃馨批次#002', 2, 'ORD202312010002', '余额支付', '2023-12-01 15:30:00', '2023-12-01 15:30:03'),
('TXN202312010006', 3, 3, 12000.00, 1, '拍卖支付 - 百合花批次#003', 3, 'ORD202312010003', '余额支付', '2023-12-01 16:45:00', '2023-12-01 16:45:07'),

-- 提现记录
('TXN202312010007', 1, 2, 20000.00, 1, '账户提现', NULL, NULL, '银行转账', '2023-12-02 09:00:00', '2023-12-02 09:00:12'),
('TXN202312010008', 2, 2, 15000.00, 0, '账户提现', NULL, NULL, '支付宝', '2023-12-02 10:15:00', '2023-12-02 10:15:00'),

-- 佣金记录
('TXN202312010009', 1, 5, 750.00, 1, '拍卖佣金收入 - 订单#001', 1, 'ORD202312010001', NULL, '2023-12-01 14:25:00', '2023-12-01 14:25:00'),
('TXN202312010010', 1, 5, 425.00, 1, '拍卖佣金收入 - 订单#002', 2, 'ORD202312010002', NULL, '2023-12-01 15:35:00', '2023-12-01 15:35:00'),
('TXN202312010011', 1, 5, 600.00, 1, '拍卖佣金收入 - 订单#003', 3, 'ORD202312010003', NULL, '2023-12-01 16:50:00', '2023-12-01 16:50:00'),

-- 退款记录
('TXN202312010012', 2, 4, 8500.00, 1, '拍卖退款 - 订单取消', 2, 'ORD202312010002', '余额退回', '2023-12-03 11:20:00', '2023-12-03 11:20:05'),

-- 今日交易记录
('TXN202312150001', 1, 1, 30000.00, 1, '账户充值', NULL, NULL, '支付宝', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR),
('TXN202312150002', 2, 3, 18000.00, 1, '拍卖支付 - 郁金香批次#004', 4, 'ORD202312150001', '余额支付', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),
('TXN202312150003', 1, 5, 900.00, 1, '拍卖佣金收入 - 订单#004', 4, 'ORD202312150001', NULL, NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 30 MINUTE);

-- 插入佣金记录数据
INSERT INTO finance_commissions (order_id, order_no, seller_id, seller_name, buyer_id, buyer_name, order_amount, commission_rate, commission_amount, status, settled_at, created_at) VALUES
(1, 'ORD202312010001', 101, '花农张三', 1, '买家李四', 15000.00, 0.05, 750.00, 1, '2023-12-01 14:25:00', '2023-12-01 14:20:00'),
(2, 'ORD202312010002', 102, '花农王五', 2, '买家赵六', 8500.00, 0.05, 425.00, 1, '2023-12-01 15:35:00', '2023-12-01 15:30:00'),
(3, 'ORD202312010003', 103, '花农孙七', 3, '买家周八', 12000.00, 0.05, 600.00, 1, '2023-12-01 16:50:00', '2023-12-01 16:45:00'),
(4, 'ORD202312150001', 104, '花农钱九', 2, '买家吴十', 18000.00, 0.05, 900.00, 0, NULL, NOW() - INTERVAL 1 HOUR),
(5, 'ORD202312140001', 105, '花农陈一', 1, '买家李四', 22000.00, 0.05, 1100.00, 0, NULL, NOW() - INTERVAL 1 DAY),
(6, 'ORD202312130001', 106, '花农林二', 3, '买家周八', 16500.00, 0.05, 825.00, 0, NULL, NOW() - INTERVAL 2 DAY),
(7, 'ORD202312120001', 107, '花农黄三', 2, '买家赵六', 14000.00, 0.05, 700.00, 1, NOW() - INTERVAL 2 DAY + INTERVAL 2 HOUR, NOW() - INTERVAL 3 DAY),
(8, 'ORD202312110001', 108, '花农刘四', 1, '买家李四', 19500.00, 0.05, 975.00, 1, NOW() - INTERVAL 3 DAY + INTERVAL 1 HOUR, NOW() - INTERVAL 4 DAY);

-- 更新统计信息（这些会在实际应用中通过触发器或定时任务更新）
-- 这里手动更新以确保数据一致性

-- 更新账户余额和统计
UPDATE finance_accounts SET 
    balance = (
        SELECT COALESCE(SUM(CASE 
            WHEN type IN (1, 4, 5) THEN amount  -- 充值、退款、佣金收入
            WHEN type IN (2, 3, 6) THEN -amount -- 提现、支付、佣金支出
            ELSE 0 
        END), 0)
        FROM finance_transactions 
        WHERE user_id = finance_accounts.user_id 
        AND status = 1
    ),
    total_income = (
        SELECT COALESCE(SUM(amount), 0)
        FROM finance_transactions 
        WHERE user_id = finance_accounts.user_id 
        AND type IN (1, 4, 5) 
        AND status = 1
    ),
    total_expense = (
        SELECT COALESCE(SUM(amount), 0)
        FROM finance_transactions 
        WHERE user_id = finance_accounts.user_id 
        AND type IN (2, 3, 6) 
        AND status = 1
    );

-- 验证数据
SELECT '=== 账户统计 ===' as info;
SELECT 
    account_type,
    COUNT(*) as account_count,
    SUM(balance) as total_balance,
    SUM(frozen_amount) as total_frozen,
    SUM(total_income) as total_income,
    SUM(total_expense) as total_expense
FROM finance_accounts 
GROUP BY account_type;

SELECT '=== 交易统计 ===' as info;
SELECT 
    type,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    status
FROM finance_transactions 
GROUP BY type, status;

SELECT '=== 佣金统计 ===' as info;
SELECT 
    status,
    COUNT(*) as commission_count,
    SUM(commission_amount) as total_commission
FROM finance_commissions 
GROUP BY status;

SELECT '=== 今日交易统计 ===' as info;
SELECT 
    COUNT(*) as today_transactions,
    SUM(amount) as today_amount
FROM finance_transactions 
WHERE DATE(created_at) = CURDATE() 
AND status = 1;
