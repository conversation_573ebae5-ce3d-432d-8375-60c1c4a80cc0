#!/usr/bin/env bash

# 花卉拍卖系统 - 环境变量统一配置脚本
# 用于统一更新所有前端项目的环境变量配置

set -e

# 检查bash版本
if [ -z "$BASH_VERSION" ]; then
    echo "错误: 此脚本需要bash环境运行"
    echo "请使用: bash $0 或 ./$(basename $0)"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 前端项目目录
ADMIN_DIR="$PROJECT_ROOT/flower-auction-admin"
AUCTIONEER_DIR="$PROJECT_ROOT/flower-auction-auctioneer"
BUYER_DIR="$PROJECT_ROOT/flower-auction-buyer"
DISPLAY_DIR="$PROJECT_ROOT/flower-auction-display"

# 默认配置
DEFAULT_DOMAIN="localhost"
DEFAULT_API_PORT="8081"
DEFAULT_ADMIN_PORT="8081"
DEFAULT_AUCTIONEER_PORT="8081"
DEFAULT_BUYER_PORT="8081"
DEFAULT_DISPLAY_PORT="8081"

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧"
    echo "🔧                                                      🔧"
    echo "🔧           花卉拍卖系统 - 环境变量配置脚本            🔧"
    echo "🔧                                                      🔧"
    echo "🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧🔧"
    echo -e "${NC}"
    echo
    log_info "项目根目录: $PROJECT_ROOT"
    echo
}

# 获取用户输入的配置
get_config() {
    log_header "📝 配置环境变量"
    
    echo "请输入配置信息（直接回车使用默认值）："
    echo
    
    # 域名配置
    read -p "域名 (默认: $DEFAULT_DOMAIN): " DOMAIN
    DOMAIN=${DOMAIN:-$DEFAULT_DOMAIN}
    
    # API端口配置
    read -p "API服务端口 (默认: $DEFAULT_API_PORT): " API_PORT
    API_PORT=${API_PORT:-$DEFAULT_API_PORT}
    
    # 前端端口配置
    read -p "管理端端口 (默认: $DEFAULT_ADMIN_PORT): " ADMIN_PORT
    ADMIN_PORT=${ADMIN_PORT:-$DEFAULT_ADMIN_PORT}
    
    read -p "拍卖师端端口 (默认: $DEFAULT_AUCTIONEER_PORT): " AUCTIONEER_PORT
    AUCTIONEER_PORT=${AUCTIONEER_PORT:-$DEFAULT_AUCTIONEER_PORT}
    
    read -p "购买商端端口 (默认: $DEFAULT_BUYER_PORT): " BUYER_PORT
    BUYER_PORT=${BUYER_PORT:-$DEFAULT_BUYER_PORT}
    
    read -p "投屏端端口 (默认: $DEFAULT_DISPLAY_PORT): " DISPLAY_PORT
    DISPLAY_PORT=${DISPLAY_PORT:-$DEFAULT_DISPLAY_PORT}
    
    # 环境类型
    echo
    echo "选择环境类型："
    echo "1) 开发环境 (http://localhost)"
    echo "2) 生产环境 (https://域名)"
    read -p "请选择 (1-2, 默认: 1): " ENV_TYPE
    ENV_TYPE=${ENV_TYPE:-1}
    
    if [ "$ENV_TYPE" = "2" ]; then
        PROTOCOL="https"
        WS_PROTOCOL="wss"
        API_BASE_URL="$PROTOCOL://$DOMAIN/api/v1"
        WS_BASE_URL="$WS_PROTOCOL://$DOMAIN/ws"
    else
        PROTOCOL="http"
        WS_PROTOCOL="ws"
        API_BASE_URL="$PROTOCOL://$DOMAIN:$API_PORT/api/v1"
        WS_BASE_URL="$WS_PROTOCOL://$DOMAIN:$API_PORT/ws"
    fi
    
    echo
    log_info "配置预览："
    echo "  域名: $DOMAIN"
    echo "  API地址: $API_BASE_URL"
    echo "  WebSocket地址: $WS_BASE_URL"
    echo "  管理端: $PROTOCOL://$DOMAIN:$ADMIN_PORT"
    echo "  拍卖师端: $PROTOCOL://$DOMAIN:$AUCTIONEER_PORT"
    echo "  购买商端: $PROTOCOL://$DOMAIN:$BUYER_PORT"
    echo "  投屏端: $PROTOCOL://$DOMAIN:$DISPLAY_PORT"
    echo
    
    read -p "确认更新配置? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "配置更新已取消"
        exit 0
    fi
}

# 更新管理端配置
update_admin_config() {
    log_header "🔧 更新管理端配置..."
    
    if [ ! -d "$ADMIN_DIR" ]; then
        log_warning "管理端目录不存在: $ADMIN_DIR"
        return
    fi
    
    cd "$ADMIN_DIR"
    
    # 更新.env文件
    cat > .env << EOF
# 管理端环境配置
# 开发环境配置

# API服务地址
REACT_APP_API_BASE_URL=$API_BASE_URL

# WebSocket服务地址
REACT_APP_WS_URL=$WS_BASE_URL

# 应用标题
REACT_APP_TITLE=花卉拍卖系统 - 管理端

# 应用版本
REACT_APP_VERSION=1.0.0

# 环境标识
REACT_APP_ENV=development

# 是否启用调试模式
REACT_APP_DEBUG=true

# 是否启用Mock数据
REACT_APP_USE_MOCK=false

# 上传文件大小限制(MB)
REACT_APP_UPLOAD_MAX_SIZE=10

# 分页默认大小
REACT_APP_PAGE_SIZE=20

# 请求超时时间(ms)
REACT_APP_REQUEST_TIMEOUT=10000

# Token刷新提前时间(分钟)
REACT_APP_TOKEN_REFRESH_BEFORE=30
EOF
    
    log_success "管理端配置已更新"
}

# 更新拍卖师端配置
update_auctioneer_config() {
    log_header "🔧 更新拍卖师端配置..."
    
    if [ ! -d "$AUCTIONEER_DIR" ]; then
        log_warning "拍卖师端目录不存在: $AUCTIONEER_DIR"
        return
    fi
    
    cd "$AUCTIONEER_DIR"
    
    # 更新.env文件
    cat > .env << EOF
# 拍卖师端环境配置
# 开发环境配置

# API服务地址
VITE_API_BASE_URL=$API_BASE_URL

# WebSocket服务地址
VITE_WS_URL=$WS_BASE_URL

# 应用标题
VITE_APP_TITLE=花卉拍卖系统 - 拍卖师端

# 应用版本
VITE_APP_VERSION=1.0.0

# 环境标识
VITE_APP_ENV=development

# 是否启用调试模式
VITE_DEBUG=true

# 是否启用Mock数据
VITE_USE_MOCK=false

# 上传文件大小限制(MB)
VITE_UPLOAD_MAX_SIZE=10

# 分页默认大小
VITE_PAGE_SIZE=20

# 请求超时时间(ms)
VITE_REQUEST_TIMEOUT=10000

# Token刷新提前时间(分钟)
VITE_TOKEN_REFRESH_BEFORE=30
EOF
    
    log_success "拍卖师端配置已更新"
}

# 更新购买商端配置
update_buyer_config() {
    log_header "🔧 更新购买商端配置..."
    
    if [ ! -d "$BUYER_DIR" ]; then
        log_warning "购买商端目录不存在: $BUYER_DIR"
        return
    fi
    
    cd "$BUYER_DIR"
    
    # 更新.env文件
    cat > .env << EOF
# 购买商端环境配置
# 开发环境配置

# API服务地址
VITE_API_BASE_URL=$API_BASE_URL

# WebSocket服务地址
VITE_WS_URL=$WS_BASE_URL

# 应用标题
VITE_APP_TITLE=花卉拍卖系统 - 购买商端

# 应用版本
VITE_APP_VERSION=1.0.0

# 环境标识
VITE_APP_ENV=development

# 是否启用调试模式
VITE_DEBUG=true

# 是否启用Mock数据
VITE_USE_MOCK=false

# 上传文件大小限制(MB)
VITE_UPLOAD_MAX_SIZE=10

# 分页默认大小
VITE_PAGE_SIZE=20

# 请求超时时间(ms)
VITE_REQUEST_TIMEOUT=10000

# Token刷新提前时间(分钟)
VITE_TOKEN_REFRESH_BEFORE=30

# 竞价最小间隔(秒)
VITE_BID_MIN_INTERVAL=2

# 自动刷新间隔(秒)
VITE_AUTO_REFRESH_INTERVAL=5
EOF
    
    log_success "购买商端配置已更新"
}

# 更新投屏端配置
update_display_config() {
    log_header "🔧 更新投屏端配置..."
    
    if [ ! -d "$DISPLAY_DIR" ]; then
        log_warning "投屏端目录不存在: $DISPLAY_DIR"
        return
    fi
    
    cd "$DISPLAY_DIR"
    
    # 更新.env文件
    cat > .env << EOF
# 投屏端环境配置
# 开发环境配置

# API服务地址
VITE_API_BASE_URL=$API_BASE_URL

# WebSocket服务地址
VITE_WS_URL=$WS_BASE_URL/display

# 应用标题
VITE_APP_TITLE=花卉拍卖系统 - 投屏端

# 应用版本
VITE_APP_VERSION=1.0.0

# 环境标识
VITE_APP_ENV=development

# 是否启用调试模式
VITE_DEBUG=true

# 是否启用Mock数据
VITE_USE_MOCK=false

# 请求超时时间(ms)
VITE_REQUEST_TIMEOUT=10000

# 数据刷新间隔(秒)
VITE_DATA_REFRESH_INTERVAL=3

# 图表动画持续时间(ms)
VITE_CHART_ANIMATION_DURATION=1000

# 是否启用全屏模式
VITE_ENABLE_FULLSCREEN=true

# 屏幕保护时间(分钟)
VITE_SCREENSAVER_TIMEOUT=30
EOF
    
    log_success "投屏端配置已更新"
}

# 显示配置结果
show_result() {
    log_header "✅ 配置更新完成"
    
    echo "所有前端项目的环境变量已统一更新："
    echo
    echo "📁 配置文件位置："
    echo "  - $ADMIN_DIR/.env"
    echo "  - $AUCTIONEER_DIR/.env"
    echo "  - $BUYER_DIR/.env"
    echo "  - $DISPLAY_DIR/.env"
    echo
    echo "🌐 服务地址："
    echo "  - API服务: $API_BASE_URL"
    echo "  - WebSocket: $WS_BASE_URL"
    echo "  - 管理端: $PROTOCOL://$DOMAIN:$ADMIN_PORT"
    echo "  - 拍卖师端: $PROTOCOL://$DOMAIN:$AUCTIONEER_PORT"
    echo "  - 购买商端: $PROTOCOL://$DOMAIN:$BUYER_PORT"
    echo "  - 投屏端: $PROTOCOL://$DOMAIN:$DISPLAY_PORT"
    echo
    echo "💡 提示："
    echo "  - 重启前端服务以应用新配置"
    echo "  - 生产环境部署时请修改相应的.env.production文件"
    echo "  - 可以随时运行此脚本重新配置"
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统环境变量配置脚本"
    echo
    echo "用法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  config   交互式配置环境变量 (默认)"
    echo "  help     显示帮助信息"
    echo
    echo "功能:"
    echo "  - 统一配置所有前端项目的环境变量"
    echo "  - 支持开发和生产环境配置"
    echo "  - 自动更新API地址和WebSocket地址"
    echo "  - 支持自定义域名和端口"
    echo
    echo "示例:"
    echo "  $0           # 交互式配置"
    echo "  $0 config    # 交互式配置"
}

# 主函数
main() {
    local command=${1:-config}
    
    case $command in
        "config"|"")
            show_banner
            get_config
            update_admin_config
            update_auctioneer_config
            update_buyer_config
            update_display_config
            show_result
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
