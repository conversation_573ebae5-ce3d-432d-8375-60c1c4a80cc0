#!/usr/bin/env bash

# 花卉拍卖系统 - 环境变量检查脚本
# 检查所有前端项目的环境变量配置

set -e

# 检查bash版本
if [ -z "$BASH_VERSION" ]; then
    echo "错误: 此脚本需要bash环境运行"
    echo "请使用: bash $0 或 ./$(basename $0)"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 前端项目配置
PROJECTS=(
    "flower-auction-admin:.env:REACT_APP_API_BASE_URL:REACT_APP_WS_URL:管理端"
    "flower-auction-auctioneer:.env:VITE_API_BASE_URL:VITE_WS_URL:拍卖师端"
    "flower-auction-buyer:.env:VITE_API_BASE_URL:VITE_WS_URL:购买商端"
    "flower-auction-display:.env:VITE_API_BASE_URL:VITE_WS_URL:投屏端"
)

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍"
    echo "🔍                                                      🔍"
    echo "🔍           花卉拍卖系统 - 环境变量检查脚本            🔍"
    echo "🔍                                                      🔍"
    echo "🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍"
    echo -e "${NC}"
    echo
    log_info "项目根目录: $PROJECT_ROOT"
    echo
}

# 检查单个项目的环境变量
check_project_env() {
    local project_dir=$1
    local env_file=$2
    local api_var=$3
    local ws_var=$4
    local project_name=$5
    
    local full_path="$PROJECT_ROOT/$project_dir"
    local env_path="$full_path/$env_file"
    
    log_header "🔍 检查 $project_name ($project_dir)"
    
    # 检查项目目录是否存在
    if [ ! -d "$full_path" ]; then
        log_error "项目目录不存在: $full_path"
        return 1
    fi
    
    # 检查环境文件是否存在
    if [ ! -f "$env_path" ]; then
        log_error "环境文件不存在: $env_path"
        return 1
    fi
    
    log_success "项目目录存在: $full_path"
    log_success "环境文件存在: $env_path"
    
    # 检查API地址配置
    local api_url=""
    if grep -q "^$api_var=" "$env_path"; then
        api_url=$(grep "^$api_var=" "$env_path" | cut -d'=' -f2)
        log_success "API地址: $api_url"
        
        # 检查是否使用localhost
        if [[ "$api_url" == *"localhost"* ]]; then
            log_warning "使用localhost地址，适用于开发环境"
        else
            log_info "使用自定义域名，适用于生产环境"
        fi
    else
        log_error "未找到API地址配置: $api_var"
        return 1
    fi
    
    # 检查WebSocket地址配置
    local ws_url=""
    if grep -q "^$ws_var=" "$env_path"; then
        ws_url=$(grep "^$ws_var=" "$env_path" | cut -d'=' -f2)
        log_success "WebSocket地址: $ws_url"
        
        # 检查是否使用localhost
        if [[ "$ws_url" == *"localhost"* ]]; then
            log_warning "使用localhost地址，适用于开发环境"
        else
            log_info "使用自定义域名，适用于生产环境"
        fi
    else
        log_error "未找到WebSocket地址配置: $ws_var"
        return 1
    fi
    
    # 检查协议一致性
    if [[ "$api_url" == "https://"* && "$ws_url" == "ws://"* ]]; then
        log_warning "API使用HTTPS但WebSocket使用WS，建议WebSocket使用WSS"
    elif [[ "$api_url" == "http://"* && "$ws_url" == "wss://"* ]]; then
        log_warning "API使用HTTP但WebSocket使用WSS，可能存在混合内容问题"
    fi
    
    echo
    return 0
}

# 检查所有项目
check_all_projects() {
    log_header "🔍 开始检查所有前端项目的环境变量配置..."
    echo
    
    local success_count=0
    local total_count=${#PROJECTS[@]}
    local failed_projects=()
    
    for project_info in "${PROJECTS[@]}"; do
        IFS=':' read -r project_dir env_file api_var ws_var project_name <<< "$project_info"
        
        if check_project_env "$project_dir" "$env_file" "$api_var" "$ws_var" "$project_name"; then
            ((success_count++))
        else
            failed_projects+=("$project_name")
        fi
    done
    
    # 显示检查结果
    log_header "📊 检查结果统计"
    log_info "总项目数: $total_count"
    log_success "检查通过: $success_count"
    
    if [ ${#failed_projects[@]} -gt 0 ]; then
        log_error "检查失败: ${#failed_projects[@]}"
        log_error "失败的项目: ${failed_projects[*]}"
        echo
        log_warning "建议运行配置脚本修复问题: bash scripts/update-env.sh"
        return 1
    else
        log_success "🎉 所有项目环境变量配置正常！"
        echo
        return 0
    fi
}

# 显示配置摘要
show_config_summary() {
    log_header "📋 配置摘要"
    
    echo "┌─────────────────┬─────────────────────────────────────┬─────────────────────────────────────┐"
    echo "│     项目        │              API地址                │           WebSocket地址             │"
    echo "├─────────────────┼─────────────────────────────────────┼─────────────────────────────────────┤"
    
    for project_info in "${PROJECTS[@]}"; do
        IFS=':' read -r project_dir env_file api_var ws_var project_name <<< "$project_info"
        
        local env_path="$PROJECT_ROOT/$project_dir/$env_file"
        local api_url="N/A"
        local ws_url="N/A"
        
        if [ -f "$env_path" ]; then
            if grep -q "^$api_var=" "$env_path"; then
                api_url=$(grep "^$api_var=" "$env_path" | cut -d'=' -f2)
            fi
            if grep -q "^$ws_var=" "$env_path"; then
                ws_url=$(grep "^$ws_var=" "$env_path" | cut -d'=' -f2)
            fi
        fi
        
        # 截断长URL以适应表格
        if [ ${#api_url} -gt 35 ]; then
            api_url="${api_url:0:32}..."
        fi
        if [ ${#ws_url} -gt 35 ]; then
            ws_url="${ws_url:0:32}..."
        fi
        
        printf "│ %-15s │ %-35s │ %-35s │\n" "$project_name" "$api_url" "$ws_url"
    done
    
    echo "└─────────────────┴─────────────────────────────────────┴─────────────────────────────────────┘"
    echo
}

# 检查硬编码的localhost
check_hardcoded_localhost() {
    log_header "🔍 检查硬编码的localhost地址..."
    
    local found_hardcoded=false
    
    for project_info in "${PROJECTS[@]}"; do
        IFS=':' read -r project_dir env_file api_var ws_var project_name <<< "$project_info"
        
        local full_path="$PROJECT_ROOT/$project_dir"
        
        if [ ! -d "$full_path" ]; then
            continue
        fi
        
        log_info "检查 $project_name 中的硬编码localhost..."
        
        # 检查TypeScript/JavaScript文件中的硬编码localhost
        local hardcoded_files=$(find "$full_path/src" -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" 2>/dev/null | xargs grep -l "localhost" 2>/dev/null || true)
        
        if [ ! -z "$hardcoded_files" ]; then
            found_hardcoded=true
            log_warning "发现硬编码localhost的文件:"
            echo "$hardcoded_files" | while read -r file; do
                echo "    - $file"
                # 显示具体的行
                grep -n "localhost" "$file" | head -3 | while read -r line; do
                    echo "      $line"
                done
            done
            echo
        else
            log_success "$project_name 未发现硬编码localhost"
        fi
    done
    
    if [ "$found_hardcoded" = true ]; then
        log_warning "发现硬编码的localhost地址，建议替换为环境变量"
        return 1
    else
        log_success "所有项目都正确使用环境变量"
        return 0
    fi
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统环境变量检查脚本"
    echo
    echo "用法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  check     检查环境变量配置 (默认)"
    echo "  summary   显示配置摘要"
    echo "  hardcode  检查硬编码的localhost"
    echo "  all       执行所有检查"
    echo "  help      显示帮助信息"
    echo
    echo "功能:"
    echo "  - 检查所有前端项目的环境变量配置"
    echo "  - 验证API地址和WebSocket地址"
    echo "  - 检查协议一致性"
    echo "  - 发现硬编码的localhost地址"
    echo
    echo "示例:"
    echo "  $0           # 检查环境变量配置"
    echo "  $0 check     # 检查环境变量配置"
    echo "  $0 summary   # 显示配置摘要"
    echo "  $0 all       # 执行所有检查"
}

# 主函数
main() {
    local command=${1:-check}
    
    case $command in
        "check"|"")
            show_banner
            check_all_projects
            ;;
        "summary")
            show_banner
            show_config_summary
            ;;
        "hardcode")
            show_banner
            check_hardcoded_localhost
            ;;
        "all")
            show_banner
            check_all_projects
            echo
            show_config_summary
            echo
            check_hardcoded_localhost
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
