import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { setUser, clearUser, setLoading } from '../store/slices/authSlice';
import { authService } from '../services/authService';

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  realName?: string;
  userType?: number;
  phone?: string;
  companyName?: string;
  balance?: number;
  frozenAmount?: number;
  creditLevel?: number;
  roles?: Array<{
    id: number;
    name: string;
    code?: string;
  }>;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);

  // 登录
  const login = async (credentials: LoginCredentials) => {
    try {
      dispatch(setLoading(true));
      console.log('开始登录:', credentials.username);
      const response = await authService.login(credentials);
      console.log('登录响应:', response);

      if (response.success && response.data) {
        // 保存token到localStorage
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);

        // 保存token过期时间
        if (response.data.expiresAt) {
          localStorage.setItem('tokenExpiresAt', response.data.expiresAt.toString());
        }

        // 直接使用登录响应中的用户信息
        console.log('设置用户信息:', response.data.user);
        dispatch(setUser(response.data.user));

        console.log('登录成功，用户已认证');
        return { success: true };
      }

      console.log('登录失败:', response.message);
      return { success: false, message: response.message || '登录失败' };
    } catch (error: any) {
      console.error('登录异常:', error);
      return { success: false, message: error.message || '登录失败' };
    } finally {
      dispatch(setLoading(false));
    }
  };

  // 登出
  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('tokenExpiresAt');
      localStorage.removeItem('user');
      // 清除Redux状态
      dispatch(clearUser());
    }
  };

  // 刷新token
  const refreshToken = async () => {
    const refreshTokenValue = localStorage.getItem('refreshToken');
    if (!refreshTokenValue) {
      return false;
    }

    try {
      const response = await authService.refreshToken(refreshTokenValue);
      if (response.success && response.data) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Refresh token error:', error);
      return false;
    }
  };

  return {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    refreshToken,
  };
};
