import React, { useState } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Input,
  Button,
  List,
  Space,
  Tag,
  Divider,
  Collapse,
} from 'antd';
import {
  QuestionCircleOutlined,
  SearchOutlined,
  BookOutlined,
  CustomerServiceOutlined,
  FileTextOutlined,
  VideoCameraOutlined,
  BulbOutlined,
  ToolOutlined,
  SafetyOutlined,
  RocketOutlined,
  StarOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Search } = Input;
const { Panel } = Collapse;

// 帮助分类接口
interface HelpCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  articleCount: number;
  color: string;
}

// 热门文章接口
interface PopularArticle {
  id: string;
  title: string;
  summary: string;
  category: string;
  views: number;
  updateTime: string;
  tags: string[];
}

// 常见问题接口
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
}

const HelpCenter: React.FC = () => {
  const [categories] = useState<HelpCategory[]>([
    {
      id: '1',
      name: '快速入门',
      icon: <RocketOutlined />,
      description: '新用户指南，快速上手系统功能',
      articleCount: 12,
      color: '#1890ff',
    },
    {
      id: '2',
      name: '用户管理',
      icon: <CustomerServiceOutlined />,
      description: '用户注册、认证、权限管理相关',
      articleCount: 8,
      color: '#52c41a',
    },
    {
      id: '3',
      name: '商品管理',
      icon: <BookOutlined />,
      description: '商品上架、分类、质检等功能',
      articleCount: 15,
      color: '#faad14',
    },
    {
      id: '4',
      name: '拍卖管理',
      icon: <ToolOutlined />,
      description: '拍卖创建、管理、出价等操作',
      articleCount: 10,
      color: '#722ed1',
    },
    {
      id: '5',
      name: '订单管理',
      icon: <FileTextOutlined />,
      description: '订单处理、支付、物流管理',
      articleCount: 9,
      color: '#eb2f96',
    },
    {
      id: '6',
      name: '系统设置',
      icon: <SafetyOutlined />,
      description: '系统配置、安全设置、备份恢复',
      articleCount: 6,
      color: '#f5222d',
    },
  ]);

  const [popularArticles] = useState<PopularArticle[]>([
    {
      id: '1',
      title: '如何创建第一个拍卖',
      summary: '详细介绍如何在系统中创建和管理拍卖活动，包括商品选择、时间设置等',
      category: '拍卖管理',
      views: 1256,
      updateTime: '2023-12-15',
      tags: ['新手指南', '拍卖'],
    },
    {
      id: '2',
      title: '用户认证流程说明',
      summary: '用户身份认证的完整流程，包括资料提交、审核状态查看等',
      category: '用户管理',
      views: 987,
      updateTime: '2023-12-14',
      tags: ['认证', '用户'],
    },
    {
      id: '3',
      title: '商品质检标准设置',
      summary: '如何设置和管理商品质检标准，确保商品质量符合要求',
      category: '商品管理',
      views: 756,
      updateTime: '2023-12-13',
      tags: ['质检', '标准'],
    },
    {
      id: '4',
      title: '支付方式配置指南',
      summary: '配置各种支付方式，包括支付宝、微信支付、银行转账等',
      category: '系统设置',
      views: 654,
      updateTime: '2023-12-12',
      tags: ['支付', '配置'],
    },
    {
      id: '5',
      title: '数据备份与恢复',
      summary: '系统数据的备份策略和恢复操作，保障数据安全',
      category: '系统设置',
      views: 543,
      updateTime: '2023-12-11',
      tags: ['备份', '安全'],
    },
  ]);

  const [faqs] = useState<FAQ[]>([
    {
      id: '1',
      question: '忘记密码怎么办？',
      answer: '您可以在登录页面点击"忘记密码"，输入注册邮箱或手机号，系统会发送重置密码的链接到您的邮箱。',
      category: '账户问题',
      helpful: 45,
    },
    {
      id: '2',
      question: '如何修改个人信息？',
      answer: '登录后点击右上角头像，选择"个人设置"，在个人信息页面可以修改姓名、联系方式等信息。',
      category: '账户问题',
      helpful: 38,
    },
    {
      id: '3',
      question: '拍卖结束后如何处理？',
      answer: '拍卖结束后，系统会自动生成订单，买家需要在规定时间内完成支付，卖家需要及时发货。',
      category: '拍卖问题',
      helpful: 52,
    },
    {
      id: '4',
      question: '商品审核需要多长时间？',
      answer: '一般情况下，商品审核会在1-3个工作日内完成。如有特殊情况，审核时间可能会延长。',
      category: '商品问题',
      helpful: 29,
    },
    {
      id: '5',
      question: '如何联系客服？',
      answer: '您可以通过以下方式联系客服：1. 在线客服（工作时间9:00-18:00）2. 客服电话：400-123-4567 3. 邮箱：<EMAIL>',
      category: '其他问题',
      helpful: 67,
    },
  ]);

  // 搜索帮助内容
  const handleSearch = (value: string) => {
    // 这里可以调用搜索API
    console.log('搜索关键词:', value);
  };

  // 获取分类图标颜色
  const getCategoryIcon = (category: HelpCategory) => {
    return (
      <span style={{ color: category.color, fontSize: '24px' }}>
        {category.icon}
      </span>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <QuestionCircleOutlined /> 帮助中心
      </Title>
      
      <Paragraph>
        欢迎来到昆明花卉拍卖系统帮助中心！这里为您提供详细的使用指南、常见问题解答和技术支持。
      </Paragraph>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Title level={3}>有什么可以帮助您的？</Title>
          <Search
            placeholder="搜索帮助内容、功能说明、常见问题..."
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            style={{ maxWidth: 600, margin: '20px 0' }}
            onSearch={handleSearch}
          />
          <div>
            <Text type="secondary">
              热门搜索：
              <Button type="link" size="small" onClick={() => handleSearch('用户认证')}>用户认证</Button>
              <Button type="link" size="small" onClick={() => handleSearch('创建拍卖')}>创建拍卖</Button>
              <Button type="link" size="small" onClick={() => handleSearch('支付设置')}>支付设置</Button>
              <Button type="link" size="small" onClick={() => handleSearch('数据备份')}>数据备份</Button>
            </Text>
          </div>
        </div>
      </Card>

      <Row gutter={24}>
        <Col span={16}>
          {/* 帮助分类 */}
          <Card title="帮助分类" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              {categories.map((category) => (
                <Col span={8} key={category.id}>
                  <Card
                    hoverable
                    style={{ height: '100%' }}
                    bodyStyle={{ padding: 16 }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ marginBottom: 12 }}>
                        {getCategoryIcon(category)}
                      </div>
                      <Title level={5} style={{ marginBottom: 8 }}>
                        {category.name}
                      </Title>
                      <Paragraph 
                        type="secondary" 
                        style={{ fontSize: 12, marginBottom: 8 }}
                      >
                        {category.description}
                      </Paragraph>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {category.articleCount} 篇文章
                      </Text>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>

          {/* 热门文章 */}
          <Card title={<span><StarOutlined /> 热门文章</span>}>
            <List
              itemLayout="vertical"
              dataSource={popularArticles}
              renderItem={(article) => (
                <List.Item
                  key={article.id}
                  actions={[
                    <Space key="views">
                      <Text type="secondary">{article.views} 次查看</Text>
                    </Space>,
                    <Space key="time">
                      <ClockCircleOutlined />
                      <Text type="secondary">{article.updateTime}</Text>
                    </Space>,
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Button type="link" style={{ padding: 0, height: 'auto' }}>
                        {article.title}
                      </Button>
                    }
                    description={
                      <div>
                        <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: 8 }}>
                          {article.summary}
                        </Paragraph>
                        <Space>
                          <Tag color="blue">{article.category}</Tag>
                          {article.tags.map((tag) => (
                            <Tag key={tag}>{tag}</Tag>
                          ))}
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={8}>
          {/* 快速链接 */}
          <Card title="快速链接" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                icon={<VideoCameraOutlined />} 
                block
              >
                视频教程
              </Button>
              <Button 
                icon={<CustomerServiceOutlined />} 
                block
              >
                在线客服
              </Button>
              <Button 
                icon={<FileTextOutlined />} 
                block
              >
                用户手册下载
              </Button>
              <Button 
                icon={<BulbOutlined />} 
                block
              >
                功能建议
              </Button>
            </Space>
          </Card>

          {/* 常见问题 */}
          <Card title={<span><QuestionCircleOutlined /> 常见问题</span>}>
            <Collapse ghost>
              {faqs.map((faq) => (
                <Panel 
                  header={faq.question} 
                  key={faq.id}
                  extra={
                    <Space>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {faq.helpful} 人觉得有用
                      </Text>
                    </Space>
                  }
                >
                  <Paragraph>{faq.answer}</Paragraph>
                  <Divider style={{ margin: '12px 0' }} />
                  <Space>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      这个回答对您有帮助吗？
                    </Text>
                    <Button size="small" type="link">
                      有用
                    </Button>
                    <Button size="small" type="link">
                      没用
                    </Button>
                  </Space>
                </Panel>
              ))}
            </Collapse>
          </Card>

          {/* 联系我们 */}
          <Card title="联系我们">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>客服电话：</Text>
                <Text>400-123-4567</Text>
              </div>
              <div>
                <Text strong>工作时间：</Text>
                <Text>周一至周五 9:00-18:00</Text>
              </div>
              <div>
                <Text strong>邮箱：</Text>
                <Text><EMAIL></Text>
              </div>
              <div>
                <Text strong>地址：</Text>
                <Text>云南省昆明市呈贡区花卉大道123号</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HelpCenter;
