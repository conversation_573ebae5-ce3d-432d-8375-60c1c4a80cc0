import React, { useState } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Menu,
  Anchor,
  Button,
  Space,
  Divider,
  Steps,
  Alert,
  Image,
  Tag,
} from 'antd';
import {
  BookOutlined,
  DownloadOutlined,
  PrinterOutlined,
  ShareAltOutlined,
  RocketOutlined,
  UserOutlined,
  ShoppingOutlined,
  TrophyOutlined,
  SettingOutlined,
  SafetyOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const { Link } = Anchor;

const UserManual: React.FC = () => {
  const [selectedSection, setSelectedSection] = useState('getting-started');

  // 手册章节
  const sections = [
    {
      key: 'getting-started',
      title: '快速入门',
      icon: <RocketOutlined />,
      content: (
        <div>
          <Title level={3}>快速入门指南</Title>
          <Alert
            message="欢迎使用昆明花卉拍卖系统"
            description="本指南将帮助您快速了解系统的基本功能和操作流程。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
          
          <Title level={4}>系统概述</Title>
          <Paragraph>
            昆明花卉拍卖系统是一个专业的花卉交易平台，为花卉种植者、批发商和零售商提供便捷的在线拍卖服务。
            系统支持多种拍卖模式，包括荷兰式拍卖、英式拍卖等。
          </Paragraph>

          <Title level={4}>首次登录</Title>
          <Steps direction="vertical" size="small">
            <Step
              title="访问系统"
              description="在浏览器中输入系统地址，进入登录页面"
            />
            <Step
              title="输入凭据"
              description="输入管理员提供的用户名和密码"
            />
            <Step
              title="完善信息"
              description="首次登录后，请完善个人信息和企业认证"
            />
            <Step
              title="开始使用"
              description="认证通过后，即可开始使用系统功能"
            />
          </Steps>

          <Title level={4}>主要功能模块</Title>
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <UserOutlined style={{ color: '#1890ff' }} />
                  <div>
                    <Text strong>用户管理</Text>
                    <br />
                    <Text type="secondary">用户注册、认证、权限管理</Text>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Space>
                  <ShoppingOutlined style={{ color: '#52c41a' }} />
                  <div>
                    <Text strong>商品管理</Text>
                    <br />
                    <Text type="secondary">商品上架、分类、质检</Text>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'user-management',
      title: '用户管理',
      icon: <UserOutlined />,
      content: (
        <div>
          <Title level={3}>用户管理</Title>
          
          <Title level={4}>用户注册流程</Title>
          <Paragraph>
            新用户可以通过以下步骤完成注册：
          </Paragraph>
          <Steps direction="vertical" size="small">
            <Step title="填写基本信息" description="姓名、联系方式、邮箱等" />
            <Step title="选择用户类型" description="买家、卖家或双重身份" />
            <Step title="上传认证资料" description="身份证、营业执照等" />
            <Step title="等待审核" description="1-3个工作日内完成审核" />
          </Steps>

          <Title level={4}>用户认证</Title>
          <Paragraph>
            为保障交易安全，所有用户都需要完成身份认证：
          </Paragraph>
          <ul>
            <li><Text strong>个人用户：</Text>需要提供身份证正反面照片</li>
            <li><Text strong>企业用户：</Text>需要提供营业执照、法人身份证等</li>
            <li><Text strong>认证时效：</Text>认证资料有效期为1年，到期需重新认证</li>
          </ul>

          <Title level={4}>权限管理</Title>
          <Paragraph>
            系统采用基于角色的权限管理模式：
          </Paragraph>
          <Row gutter={16}>
            <Col span={8}>
              <Card size="small" title="管理员">
                <ul style={{ paddingLeft: 16, margin: 0 }}>
                  <li>系统配置</li>
                  <li>用户管理</li>
                  <li>数据统计</li>
                  <li>系统监控</li>
                </ul>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" title="卖家">
                <ul style={{ paddingLeft: 16, margin: 0 }}>
                  <li>商品管理</li>
                  <li>拍卖创建</li>
                  <li>订单处理</li>
                  <li>收益查看</li>
                </ul>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" title="买家">
                <ul style={{ paddingLeft: 16, margin: 0 }}>
                  <li>商品浏览</li>
                  <li>参与拍卖</li>
                  <li>订单管理</li>
                  <li>支付结算</li>
                </ul>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'product-management',
      title: '商品管理',
      icon: <ShoppingOutlined />,
      content: (
        <div>
          <Title level={3}>商品管理</Title>
          
          <Title level={4}>商品上架流程</Title>
          <Steps direction="vertical" size="small">
            <Step title="创建商品" description="填写商品基本信息、规格、描述等" />
            <Step title="上传图片" description="上传商品图片，支持多张图片" />
            <Step title="设置分类" description="选择合适的商品分类和标签" />
            <Step title="质检申请" description="提交质检申请，等待质检结果" />
            <Step title="审核通过" description="质检通过后，商品可参与拍卖" />
          </Steps>

          <Title level={4}>商品分类</Title>
          <Paragraph>
            系统支持多级商品分类，主要包括：
          </Paragraph>
          <Row gutter={16}>
            <Col span={6}>
              <Tag color="red">玫瑰</Tag>
              <br />
              <Text type="secondary">红玫瑰、白玫瑰、粉玫瑰等</Text>
            </Col>
            <Col span={6}>
              <Tag color="orange">康乃馨</Tag>
              <br />
              <Text type="secondary">标准康乃馨、迷你康乃馨等</Text>
            </Col>
            <Col span={6}>
              <Tag color="yellow">百合</Tag>
              <br />
              <Text type="secondary">白百合、香水百合等</Text>
            </Col>
            <Col span={6}>
              <Tag color="green">其他花卉</Tag>
              <br />
              <Text type="secondary">郁金香、向日葵等</Text>
            </Col>
          </Row>

          <Title level={4}>质检标准</Title>
          <Paragraph>
            商品质检是保证交易质量的重要环节：
          </Paragraph>
          <ul>
            <li><Text strong>外观检查：</Text>花朵完整度、颜色鲜艳度</li>
            <li><Text strong>新鲜度：</Text>花期、保鲜状态</li>
            <li><Text strong>规格标准：</Text>长度、重量、包装规格</li>
            <li><Text strong>病虫害：</Text>是否有病虫害迹象</li>
          </ul>
        </div>
      ),
    },
    {
      key: 'auction-management',
      title: '拍卖管理',
      icon: <TrophyOutlined />,
      content: (
        <div>
          <Title level={3}>拍卖管理</Title>
          
          <Title level={4}>拍卖类型</Title>
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small" title="荷兰式拍卖">
                <Paragraph>
                  价格从高到低递减，第一个出价者获得商品。适合批量销售。
                </Paragraph>
                <Text type="secondary">特点：快速成交、价格透明</Text>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="英式拍卖">
                <Paragraph>
                  价格从低到高递增，最高出价者获得商品。适合精品销售。
                </Paragraph>
                <Text type="secondary">特点：竞争激烈、价格最优</Text>
              </Card>
            </Col>
          </Row>

          <Title level={4}>创建拍卖</Title>
          <Steps direction="vertical" size="small">
            <Step title="选择商品" description="从已通过质检的商品中选择" />
            <Step title="设置参数" description="起拍价、保留价、拍卖时长等" />
            <Step title="选择模式" description="荷兰式或英式拍卖" />
            <Step title="发布拍卖" description="确认信息后发布拍卖" />
          </Steps>

          <Title level={4}>拍卖监控</Title>
          <Paragraph>
            拍卖过程中可以实时监控：
          </Paragraph>
          <ul>
            <li>当前价格和出价记录</li>
            <li>参与人数和竞拍情况</li>
            <li>剩余时间和延时规则</li>
            <li>异常情况处理</li>
          </ul>
        </div>
      ),
    },
    {
      key: 'system-settings',
      title: '系统设置',
      icon: <SettingOutlined />,
      content: (
        <div>
          <Title level={3}>系统设置</Title>
          
          <Title level={4}>基础配置</Title>
          <Paragraph>
            系统基础配置包括：
          </Paragraph>
          <ul>
            <li><Text strong>网站信息：</Text>网站名称、描述、联系方式</li>
            <li><Text strong>拍卖参数：</Text>默认拍卖时长、加价幅度</li>
            <li><Text strong>用户设置：</Text>注册审核、角色权限</li>
            <li><Text strong>支付配置：</Text>支付方式、手续费率</li>
          </ul>

          <Title level={4}>安全设置</Title>
          <Paragraph>
            系统安全配置包括：
          </Paragraph>
          <ul>
            <li><Text strong>密码策略：</Text>密码复杂度、过期时间</li>
            <li><Text strong>登录安全：</Text>双因子认证、登录限制</li>
            <li><Text strong>IP白名单：</Text>允许访问的IP地址</li>
            <li><Text strong>操作日志：</Text>用户操作记录和审计</li>
          </ul>

          <Title level={4}>数据备份</Title>
          <Paragraph>
            定期备份系统数据，确保数据安全：
          </Paragraph>
          <ul>
            <li><Text strong>自动备份：</Text>设置自动备份计划</li>
            <li><Text strong>手动备份：</Text>重要操作前手动备份</li>
            <li><Text strong>备份恢复：</Text>从备份文件恢复数据</li>
            <li><Text strong>备份验证：</Text>定期验证备份文件完整性</li>
          </ul>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <BookOutlined /> 用户手册
        </Title>
        <Space>
          <Button icon={<DownloadOutlined />} type="primary">
            下载PDF版本
          </Button>
          <Button icon={<PrinterOutlined />}>
            打印手册
          </Button>
          <Button icon={<ShareAltOutlined />}>
            分享手册
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={6}>
          <Card title="目录" size="small">
            <Menu
              mode="vertical"
              selectedKeys={[selectedSection]}
              onClick={({ key }) => setSelectedSection(key as string)}
            >
              {sections.map((section) => (
                <Menu.Item key={section.key} icon={section.icon}>
                  {section.title}
                </Menu.Item>
              ))}
            </Menu>
          </Card>
        </Col>

        <Col span={18}>
          <Card>
            {sections.find(s => s.key === selectedSection)?.content}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default UserManual;
