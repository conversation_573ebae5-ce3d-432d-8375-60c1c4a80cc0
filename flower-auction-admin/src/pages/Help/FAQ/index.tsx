import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Input,
  Select,
  Collapse,
  Button,
  Space,
  Tag,
  Rate,
  message,
  Row,
  Col,
  Statistic,
  Empty,
  Divider,
} from 'antd';
import {
  QuestionCircleOutlined,
  SearchOutlined,
  LikeOutlined,
  DislikeOutlined,
  PlusOutlined,
  FilterOutlined,
  StarOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;

// FAQ接口
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  helpful: number;
  notHelpful: number;
  views: number;
  lastUpdated: string;
  priority: 'high' | 'medium' | 'low';
}

// FAQ分类接口
interface FAQCategory {
  key: string;
  name: string;
  count: number;
}

const FAQ: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredFAQs, setFilteredFAQs] = useState<FAQ[]>([]);

  const [categories] = useState<FAQCategory[]>([
    { key: 'all', name: '全部', count: 25 },
    { key: 'account', name: '账户问题', count: 8 },
    { key: 'auction', name: '拍卖问题', count: 6 },
    { key: 'product', name: '商品问题', count: 5 },
    { key: 'payment', name: '支付问题', count: 4 },
    { key: 'system', name: '系统问题', count: 2 },
  ]);

  const [faqs] = useState<FAQ[]>([
    {
      id: '1',
      question: '忘记密码怎么办？',
      answer: '您可以通过以下步骤重置密码：\n1. 在登录页面点击"忘记密码"\n2. 输入注册时使用的邮箱或手机号\n3. 查收验证码或重置链接\n4. 按照提示设置新密码\n\n如果仍无法重置，请联系客服协助处理。',
      category: 'account',
      tags: ['密码', '登录', '重置'],
      helpful: 45,
      notHelpful: 3,
      views: 1256,
      lastUpdated: '2023-12-15',
      priority: 'high',
    },
    {
      id: '2',
      question: '如何修改个人信息？',
      answer: '修改个人信息的步骤：\n1. 登录系统后，点击右上角头像\n2. 选择"个人设置"或"账户设置"\n3. 在个人信息页面修改相关信息\n4. 点击"保存"按钮确认修改\n\n注意：某些关键信息（如实名认证信息）修改后可能需要重新审核。',
      category: 'account',
      tags: ['个人信息', '修改', '设置'],
      helpful: 38,
      notHelpful: 2,
      views: 987,
      lastUpdated: '2023-12-14',
      priority: 'medium',
    },
    {
      id: '3',
      question: '拍卖结束后如何处理订单？',
      answer: '拍卖结束后的处理流程：\n1. 系统自动生成订单\n2. 买家在规定时间内完成支付（通常为24小时）\n3. 卖家确认收款后安排发货\n4. 买家确认收货，交易完成\n\n如果买家超时未支付，订单将自动取消，商品可重新上架拍卖。',
      category: 'auction',
      tags: ['拍卖', '订单', '支付', '发货'],
      helpful: 52,
      notHelpful: 1,
      views: 1543,
      lastUpdated: '2023-12-13',
      priority: 'high',
    },
    {
      id: '4',
      question: '商品审核需要多长时间？',
      answer: '商品审核时间说明：\n1. 一般商品：1-3个工作日\n2. 特殊商品：3-5个工作日\n3. 节假日期间可能延长\n\n审核内容包括：\n- 商品信息完整性\n- 图片质量和真实性\n- 分类和标签准确性\n- 质检报告有效性\n\n如有疑问，可在"商品管理"页面查看审核进度。',
      category: 'product',
      tags: ['审核', '商品', '时间'],
      helpful: 29,
      notHelpful: 5,
      views: 756,
      lastUpdated: '2023-12-12',
      priority: 'medium',
    },
    {
      id: '5',
      question: '支持哪些支付方式？',
      answer: '系统支持以下支付方式：\n\n在线支付：\n- 支付宝\n- 微信支付\n- 银联在线支付\n- 网银支付\n\n线下支付：\n- 银行转账\n- 现金支付（限线下交易）\n\n注意事项：\n- 不同支付方式可能有不同的手续费\n- 大额交易建议使用银行转账\n- 支付完成后请保留凭证',
      category: 'payment',
      tags: ['支付', '方式', '手续费'],
      helpful: 67,
      notHelpful: 2,
      views: 2134,
      lastUpdated: '2023-12-11',
      priority: 'high',
    },
    {
      id: '6',
      question: '如何参与拍卖？',
      answer: '参与拍卖的步骤：\n1. 完成用户注册和实名认证\n2. 浏览拍卖商品，选择感兴趣的商品\n3. 点击"参与拍卖"按钮\n4. 根据拍卖类型进行出价：\n   - 荷兰式拍卖：价格递减，点击即可购买\n   - 英式拍卖：输入出价金额，提交出价\n5. 关注拍卖进度，及时调整出价策略\n6. 拍卖结束后，最高出价者获得商品',
      category: 'auction',
      tags: ['拍卖', '出价', '参与'],
      helpful: 43,
      notHelpful: 4,
      views: 1876,
      lastUpdated: '2023-12-10',
      priority: 'high',
    },
    {
      id: '7',
      question: '系统维护期间能否正常使用？',
      answer: '系统维护期间的影响：\n\n计划维护：\n- 提前3天发布维护公告\n- 维护期间系统暂停服务\n- 正在进行的拍卖会暂停计时\n- 维护完成后自动恢复\n\n紧急维护：\n- 可能无法提前通知\n- 尽量选择业务低峰期\n- 维护时间通常不超过2小时\n\n建议在维护期间：\n- 不要进行重要操作\n- 保存好未完成的工作\n- 关注官方公告获取最新信息',
      category: 'system',
      tags: ['维护', '系统', '公告'],
      helpful: 21,
      notHelpful: 8,
      views: 543,
      lastUpdated: '2023-12-09',
      priority: 'low',
    },
    {
      id: '8',
      question: '如何联系客服？',
      answer: '多种方式联系客服：\n\n在线客服：\n- 工作时间：周一至周五 9:00-18:00\n- 点击页面右下角客服图标\n- 支持文字和语音交流\n\n电话客服：\n- 客服热线：400-123-4567\n- 工作时间：周一至周日 8:00-20:00\n- 紧急情况24小时值班\n\n邮件客服：\n- 邮箱：<EMAIL>\n- 响应时间：24小时内回复\n- 适合复杂问题咨询\n\n现场服务：\n- 地址：云南省昆明市呈贡区花卉大道123号\n- 时间：周一至周五 9:00-17:00',
      category: 'account',
      tags: ['客服', '联系', '支持'],
      helpful: 89,
      notHelpful: 1,
      views: 3421,
      lastUpdated: '2023-12-08',
      priority: 'high',
    },
  ]);

  // 过滤FAQ
  useEffect(() => {
    let filtered = faqs;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // 按关键词搜索
    if (searchKeyword) {
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()))
      );
    }

    // 按优先级排序
    filtered.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    setFilteredFAQs(filtered);
  }, [faqs, selectedCategory, searchKeyword]);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  // 分类选择处理
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
  };

  // 有用/无用反馈
  const handleFeedback = (faqId: string, isHelpful: boolean) => {
    message.success(isHelpful ? '感谢您的反馈！' : '我们会改进这个回答');
    // 这里可以调用API更新反馈数据
  };

  // 获取优先级标签
  const getPriorityTag = (priority: string) => {
    const config = {
      high: { color: 'red', text: '重要' },
      medium: { color: 'orange', text: '一般' },
      low: { color: 'default', text: '普通' },
    };
    const { color, text } = config[priority as keyof typeof config];
    return <Tag color={color}>{text}</Tag>;
  };

  // 获取分类名称
  const getCategoryName = (categoryKey: string) => {
    const category = categories.find(c => c.key === categoryKey);
    return category ? category.name : categoryKey;
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <QuestionCircleOutlined /> 常见问题
      </Title>

      <Paragraph>
        这里收集了用户最常遇到的问题和解答。如果您没有找到需要的答案，请联系客服获取帮助。
      </Paragraph>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索问题、关键词..."
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
              style={{ maxWidth: 400 }}
            />
          </Col>
          <Col>
            <Space>
              <Text>分类：</Text>
              <Select
                value={selectedCategory}
                onChange={handleCategoryChange}
                style={{ width: 120 }}
              >
                {categories.map(category => (
                  <Option key={category.key} value={category.key}>
                    {category.name} ({category.count})
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={24}>
        <Col span={18}>
          {/* FAQ列表 */}
          <Card title={`常见问题 (${filteredFAQs.length})`}>
            {filteredFAQs.length > 0 ? (
              <Collapse ghost>
                {filteredFAQs.map((faq) => (
                  <Panel
                    header={
                      <div>
                        <Space>
                          {getPriorityTag(faq.priority)}
                          <Text strong>{faq.question}</Text>
                        </Space>
                        <div style={{ marginTop: 4 }}>
                          <Space size="small">
                            <Tag>{getCategoryName(faq.category)}</Tag>
                            {faq.tags.map(tag => (
                              <Tag key={tag} color="blue">{tag}</Tag>
                            ))}
                          </Space>
                        </div>
                      </div>
                    }
                    key={faq.id}
                    extra={
                      <Space onClick={(e) => e.stopPropagation()}>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {faq.views} 次查看
                        </Text>
                      </Space>
                    }
                  >
                    <div style={{ paddingLeft: 24 }}>
                      <Paragraph style={{ whiteSpace: 'pre-line' }}>
                        {faq.answer}
                      </Paragraph>
                      
                      <Divider style={{ margin: '16px 0' }} />
                      
                      <Row justify="space-between" align="middle">
                        <Col>
                          <Space>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              这个回答对您有帮助吗？
                            </Text>
                            <Button
                              type="text"
                              size="small"
                              icon={<LikeOutlined />}
                              onClick={() => handleFeedback(faq.id, true)}
                            >
                              有用 ({faq.helpful})
                            </Button>
                            <Button
                              type="text"
                              size="small"
                              icon={<DislikeOutlined />}
                              onClick={() => handleFeedback(faq.id, false)}
                            >
                              没用 ({faq.notHelpful})
                            </Button>
                          </Space>
                        </Col>
                        <Col>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            更新时间：{faq.lastUpdated}
                          </Text>
                        </Col>
                      </Row>
                    </div>
                  </Panel>
                ))}
              </Collapse>
            ) : (
              <Empty description="没有找到相关问题" />
            )}
          </Card>
        </Col>

        <Col span={6}>
          {/* 统计信息 */}
          <Card title="统计信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总问题数"
                  value={faqs.length}
                  prefix={<QuestionCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="解决率"
                  value={96.8}
                  suffix="%"
                  prefix={<StarOutlined />}
                />
              </Col>
            </Row>
          </Card>

          {/* 热门标签 */}
          <Card title="热门标签" style={{ marginBottom: 16 }}>
            <Space wrap>
              {['密码', '登录', '拍卖', '支付', '审核', '客服', '订单', '商品'].map(tag => (
                <Tag
                  key={tag}
                  style={{ cursor: 'pointer' }}
                  onClick={() => setSearchKeyword(tag)}
                >
                  {tag}
                </Tag>
              ))}
            </Space>
          </Card>

          {/* 快速操作 */}
          <Card title="需要帮助？">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" block icon={<PlusOutlined />}>
                提交新问题
              </Button>
              <Button block>
                联系在线客服
              </Button>
              <Button block>
                查看用户手册
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default FAQ;
