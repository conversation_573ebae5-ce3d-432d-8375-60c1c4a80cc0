import React, { useState, useEffect } from 'react';
import {
  Card,
  Tree,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Switch,
  InputNumber,
  Descriptions,
  Tag,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FolderOutlined,
  FileOutlined,
  MinusSquareOutlined,
  PlusSquareOutlined,
} from '@ant-design/icons';
import type { DataNode, EventDataNode } from 'antd/es/tree';
import { categoryService } from '../../../services/categoryService';
import FormMessage from '../../../components/FormMessage';
import { useFormMessage, handleApiError } from '../../../hooks/useFormMessage';
import './index.css';
import './CategoryManagement.css';

const { Title } = Typography;
const { TextArea } = Input;



// 分类数据接口
export interface Category {
  id: number;
  name: string;
  code: string;
  description?: string;
  parentId?: number;
  level: number;
  sort: number;
  sortOrder?: number; // 添加sortOrder字段
  status: number;
  productCount: number;
  children?: Category[];
  createdAt: string;
  updatedAt: string;
}

const CategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();
  const [currentLevel, setCurrentLevel] = useState<number>(1);
  const [selectedParentForNew, setSelectedParentForNew] = useState<Category | null>(null);


  const {
    formError,
    formSuccess,
    setFormError,
    setFormSuccess,
    clearAllMessages
  } = useFormMessage();





  // 获取同级分类的下一个排序值
  const getNextSortOrder = (parentId: number | null): number => {
    try {
      const siblings = parentId
        ? categories.find(cat => cat.id === parentId)?.children || []
        : categories;

      if (!siblings || siblings.length === 0) return 1;

      const sortValues = siblings
        .map(cat => cat.sortOrder || cat.sort || 0)
        .filter(sort => typeof sort === 'number' && !isNaN(sort));

      if (sortValues.length === 0) return 1;

      const maxSort = Math.max(...sortValues);
      return maxSort + 1;
    } catch (error) {
      console.error('Error in getNextSortOrder:', error);
      return 1;
    }
  };



  // 获取分类列表
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await categoryService.getCategoryTree();
      console.log('分类树API响应:', response);

      // 处理API响应：如果是直接数组，则直接使用；如果是包装格式，则使用data字段
      let categoriesData: Category[];
      if (Array.isArray(response)) {
        // API直接返回数组
        categoriesData = response;
      } else if (response.success && response.data) {
        // API返回包装格式
        categoriesData = response.data;
      } else {
        // 处理错误情况
        message.error(response.message || '获取分类列表失败');
        return;
      }

      // 检查是否有子分类数据，如果没有则尝试手动构建树形结构
      const hasChildrenData = categoriesData.some(cat => cat.children && cat.children.length > 0);

      if (!hasChildrenData) {
        console.log('后端未返回子分类数据，尝试手动构建树形结构');
        // 如果后端没有返回子分类，我们手动构建树形结构
        categoriesData = buildTreeStructure(categoriesData);
      }

      setCategories(categoriesData);
      // 默认展开第一级分类（有子分类的节点）
      const firstLevelKeysWithChildren = categoriesData
        .filter((cat: Category) => cat.children && cat.children.length > 0)
        .map((cat: Category) => cat.id);
      setExpandedKeys(firstLevelKeysWithChildren);

      console.log('处理后的分类数据:', categoriesData);
    } catch (error: any) {
      console.error('获取分类列表失败:', error);
      message.error(error.message || '获取分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 手动构建树形结构
  const buildTreeStructure = (flatCategories: Category[]): Category[] => {
    const categoryMap = new Map<number, Category>();
    const rootCategories: Category[] = [];

    // 创建分类映射
    flatCategories.forEach(cat => {
      categoryMap.set(cat.id, { ...cat, children: [] });
    });

    // 构建树形结构
    flatCategories.forEach(cat => {
      const category = categoryMap.get(cat.id)!;
      if (cat.parentId === null || cat.parentId === undefined) {
        // 根分类
        rootCategories.push(category);
      } else {
        // 子分类
        const parent = categoryMap.get(cat.parentId);
        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(category);
        }
      }
    });

    return rootCategories;
  };

  // 初始化加载
  useEffect(() => {
    fetchCategories();
  }, []);

  // 监听categories变化，更新selectedCategory
  useEffect(() => {
    if (selectedCategory && categories.length > 0) {
      const updatedCategory = findCategoryById(categories, selectedCategory.id);
      if (updatedCategory && JSON.stringify(updatedCategory) !== JSON.stringify(selectedCategory)) {
        setSelectedCategory(updatedCategory);
      }
    }
  }, [categories]);



  // 处理删除按钮点击
  const handleDeleteClick = (category: Category) => {
   
    handleDeleteWithChildren(category);
    

    console.log('🔥🔥🔥 [DELETE] 步骤2完成: Modal.confirm 已调用');
  };

  // 查找分类
  const findCategoryById = (cats: Category[], id: number): Category | null => {
    for (const cat of cats) {
      if (cat.id === id) return cat;
      if (cat.children) {
        const found = findCategoryById(cat.children, id);
        if (found) return found;
      }
    }
    return null;
  };



  // 新增分类 - 简化流程
  const handleAdd = (parent?: Category) => {
    setEditingCategory(null);
    form.resetFields();
    clearAllMessages();

    if (parent) {
      // 从分类树节点添加子分类
      setSelectedParentForNew(parent);
      setCurrentLevel(parent.level + 1);

      // 直接设置表单值
      const nextSortOrder = getNextSortOrder(parent.id);
      form.setFieldsValue({
        parentId: parent.id,
        level: parent.level + 1,
        sortOrder: nextSortOrder,
        status: true,
      });
    } else {
      // 添加根分类
      setSelectedParentForNew(null);
      setCurrentLevel(1);

      // 直接设置表单值
      const nextSortOrder = getNextSortOrder(null);
      form.setFieldsValue({
        parentId: null,
        level: 1,
        sortOrder: nextSortOrder,
        status: true,
      });
    }

    setIsModalVisible(true);
  };



  // 编辑分类
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setCurrentLevel(category.level);

    form.setFieldsValue({
      name: category.name,
      description: category.description,
      status: category.status === 1,
      sortOrder: category.sortOrder || 0,
    });
    clearAllMessages();
    setIsModalVisible(true);
  };

  // 删除分类及其所有子分类（物理删除）
  const handleDeleteWithChildren = async (category: Category) => {

    try {
      // 收集所有需要删除的分类ID（包括子分类）
      const collectCategoryIds = (cat: Category): number[] => {
        let ids = [cat.id];
        if (cat.children && cat.children.length > 0) {
          cat.children.forEach(child => {
            ids = ids.concat(collectCategoryIds(child));
          });
        }
        return ids;
      };

      const idsToDelete = collectCategoryIds(category);

      // 按层级从深到浅删除（先删除子分类，再删除父分类）
      const sortedIds = idsToDelete.sort((a, b) => {
        const catA = findCategoryById(categories, a);
        const catB = findCategoryById(categories, b);
        return (catB?.level || 0) - (catA?.level || 0);
      });

      let deletedCount = 0;
      for (const id of sortedIds) {
        try {
          
          const response = await categoryService.deleteCategory(id);

          // 改进删除成功的判断逻辑
          let isSuccess = false;
          if (response) {
            // 如果响应包含success字段且为true（删除API的标准响应）
            if ('success' in response && response.success === true) {
              isSuccess = true;
            }
            // 检查后端标准响应格式 {code: 200, data: {...}, message: "..."}
            else if ('code' in response && response.code === 200) {
              isSuccess = true;
            }
            // 如果message包含"成功"，则认为是成功的
            else if (response.message && response.message.includes('成功')) {
              isSuccess = true;
            }
            // 如果响应是对象且没有错误信息，认为成功
            else if (typeof response === 'object' && !(response as any).error && !response.message?.includes('失败')) {
              isSuccess = true;
            }
          }

          if (isSuccess) {
            deletedCount++;
          } 
        } catch (error) {
          console.error(`🔥🔥🔥 [DELETE] 删除分类 ${id} 失败:`, error);
        }
      }


      if (deletedCount > 0) {
        message.success(`成功删除 ${deletedCount} 个分类`);
        fetchCategories();
        // 清除选中状态
        setSelectedCategory(null);
      } else {
        message.error('删除失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('🔥🔥🔥 [DELETE] handleDeleteWithChildren 异常:', error);
      message.error(error.message || '删除失败');
    }
  };





  // 保存分类
  const handleSave = async (values: any) => {
    setSaving(true);
    clearAllMessages();

    try {
      let response;

      if (editingCategory) {
        // 编辑分类
        const updateData = {
          name: values.name,
          description: values.description,
          status: values.status ? 1 : 0,
          sortOrder: values.sortOrder,
        };
        response = await categoryService.updateCategory(editingCategory.id, updateData);
      } else {
        // 新增分类
        const createData = {
          name: values.name,
          description: values.description,
          parentId: selectedParentForNew?.id || null,
          level: currentLevel,
          sortOrder: getNextSortOrder(selectedParentForNew?.id || null),
          status: values.status ? 1 : 0,
        };
        response = await categoryService.createCategory(createData);
      }

      const successMsg = editingCategory ? '分类更新成功！' : '分类创建成功！';

      // 处理API响应：检查是否成功
      let isSuccess = false;
      if (response && typeof response === 'object') {
        // 如果直接返回分类对象且包含id字段，说明创建/更新成功（后端直接返回对象）
        if ('id' in response && 'name' in response) {
          isSuccess = true;
        }
        // 检查后端标准响应格式 {code: 200, data: {...}, message: "..."}
        else if ('code' in response && response.code === 200) {
          isSuccess = true;
        }
        // 特殊处理：后端bug - 返回 {success: false, message: "分类更新成功"}
        else if ('success' in response && 'message' in response &&
                 response.message && response.message.includes('成功')) {
          isSuccess = true;
        }
        // 检查前端service层包装的响应格式 {success: true, data: {...}}
        else if ('success' in response) {
          isSuccess = response.success;
          if (!isSuccess && response.message) {
            setFormError(response.message);
          }
        }
      }

      if (isSuccess) {
        setFormSuccess(successMsg);
        // 成功：延迟关闭模态框
        setTimeout(() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingCategory(null);
          setSelectedParentForNew(null);
          clearAllMessages();
          fetchCategories();
        }, 1500);
      } else {
        setFormError('操作失败，请稍后重试');
      }
    } catch (error: any) {
      handleApiError(error, setFormError);
    } finally {
      setSaving(false);
    }
  };

  // 转换分类数据为树形结构 - 优化版
  const convertCategoriesToTreeData = (categories: Category[]): DataNode[] => {
    return categories.map((category) => {
      const hasChildren = category.children && category.children.length > 0;
      const productCount = category.productCount || 0;

      return {
        title: (
          <div className="category-tree-node" style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
            paddingRight: '8px'
          }}>
            <div className="category-info" style={{ flex: 1 }}>
              <span className="category-name">{category.name}</span>
              <div className="category-meta">
                <span className="category-code">({category.code})</span>
                {productCount > 0 && (
                  <span className="product-count">{productCount}个商品</span>
                )}
                {hasChildren && (
                  <span className="children-count">
                    {category.children!.length}个子分类
                  </span>
                )}
                {category.status === 0 && (
                  <Tag color="volcano" style={{ fontSize: '11px' }}>
                    已禁用
                  </Tag>
                )}
              </div>
            </div>

            {/* 操作按钮组 */}
            <div
              className="category-actions"
              style={{
                display: 'flex',
                gap: '4px',
                opacity: 1,
                transition: 'all 0.2s',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '4px',
                padding: '2px',
                boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 1)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                e.currentTarget.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(category);
                }}
                style={{
                  padding: '2px 4px',
                  height: '22px',
                  minWidth: '22px',
                  color: '#1890ff',
                  border: 'none'
                }}
                title="编辑分类"
              />
              {category.level < 3 && (
                <Button
                  type="text"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAdd(category);
                  }}
                  style={{
                    padding: '2px 4px',
                    height: '22px',
                    minWidth: '22px',
                    color: '#52c41a',
                    border: 'none'
                  }}
                  title="添加子分类"
                />
              )}
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClick(category);
                }}
                style={{
                  padding: '2px 4px',
                  height: '22px',
                  minWidth: '22px',
                  color: '#ff4d4f',
                  border: 'none',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                title="删除分类"
              />
            </div>
          </div>
        ),
        key: category.id,
        icon: hasChildren ? <FolderOutlined /> : <FileOutlined />,
        children: hasChildren ? convertCategoriesToTreeData(category.children!) : undefined,
        isLeaf: !hasChildren,
      };
    });
  };

  // 树节点选择处理
  const handleTreeSelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const category = findCategoryById(categories, selectedKeys[0] as number);
      setSelectedCategory(category);
    } else {
      setSelectedCategory(null);
    }
  };

  return (
    <div className="category-management-container">
      <Title level={2}>分类管理</Title>

      <Row gutter={24}>
        {/* 左侧分类树 */}
        <Col xs={24} lg={16}>
          <Card
            title="分类树"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAdd()}
                >
                  添加根分类
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchCategories}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Tree
              showIcon
              showLine={false}
              expandedKeys={expandedKeys}
              onExpand={setExpandedKeys}
              onSelect={handleTreeSelect}
              selectedKeys={selectedCategory ? [selectedCategory.id] : []}
              treeData={convertCategoriesToTreeData(categories)}
              height={600}
              blockNode
              switcherIcon={({ expanded, isLeaf }) => {
                if (isLeaf) return null;
                return expanded ? (
                  <MinusSquareOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
                ) : (
                  <PlusSquareOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
                );
              }}
            />


          </Card>
        </Col>

        {/* 右侧分类详情 */}
        <Col xs={24} lg={8}>
          <Card title="分类详情">
            {selectedCategory ? (
              <Descriptions column={1} size="small">
                <Descriptions.Item label="分类名称">
                  {selectedCategory.name}
                </Descriptions.Item>
                <Descriptions.Item label="分类编码">
                  {selectedCategory.code}
                </Descriptions.Item>
                <Descriptions.Item label="分类描述">
                  {selectedCategory.description || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="分类级别">
                  第{selectedCategory.level}级
                </Descriptions.Item>
                <Descriptions.Item label="排序">
                  {selectedCategory.sortOrder || selectedCategory.sort || 0}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={selectedCategory.status === 1 ? 'green' : 'red'}>
                    {selectedCategory.status === 1 ? '启用' : '禁用'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="商品数量">
                  {selectedCategory.productCount}个
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {new Date(selectedCategory.createdAt).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(selectedCategory.updatedAt).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                请选择一个分类查看详情
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 分类编辑模态框 - 简化设计 */}
      <Modal
        title={editingCategory ? '编辑分类' : '新增分类'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingCategory(null);
          clearAllMessages();
        }}
        footer={null}
        width={600}
        destroyOnClose
      >
        {/* 位置信息显示 */}
        {!editingCategory && (
          <Alert
            message={
              selectedParentForNew
                ? `在 "${selectedParentForNew.name}" 下创建 ${currentLevel} 级分类`
                : "创建一级分类"
            }
            type="info"
            style={{ marginBottom: 16 }}
            showIcon
          />
        )}

        {(
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            autoComplete="off"
          >
            <Form.Item
              name="name"
              label="分类名称"
              rules={[
                { required: true, message: '请输入分类名称' },
                { max: 50, message: '分类名称不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入分类名称" />
            </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[
              { max: 200, message: '分类描述不能超过200个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入分类描述"
              rows={4}
              showCount
              maxLength={200}
            />
          </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="sortOrder"
                  label="排序"
                  tooltip="数值越小排序越靠前，编辑时可以调整"
                >
                  {editingCategory ? (
                    <InputNumber
                      placeholder="排序值"
                      min={1}
                      max={9999}
                      style={{ width: '100%' }}
                    />
                  ) : (
                    <div style={{
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#f5f5f5',
                      color: '#666'
                    }}>
                      系统自动分配: {getNextSortOrder(selectedParentForNew?.id || null)}
                    </div>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
              </Col>
            </Row>

            {/* 隐藏字段 */}
            <Form.Item name="parentId" hidden>
              <Input />
            </Form.Item>
            <Form.Item name="level" hidden>
              <Input />
            </Form.Item>

            {/* 错误和成功消息显示 */}
            <FormMessage type="error" message={formError} visible={!!formError} />
            <FormMessage type="success" message={formSuccess} visible={!!formSuccess} />

            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                <Button
                  onClick={() => {
                    setIsModalVisible(false);
                    form.resetFields();
                    setEditingCategory(null);
                    clearAllMessages();
                  }}
                  disabled={saving}
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  disabled={saving}
                >
                  {saving ? '保存中...' : (editingCategory ? '更新' : '创建')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default CategoryManagement;