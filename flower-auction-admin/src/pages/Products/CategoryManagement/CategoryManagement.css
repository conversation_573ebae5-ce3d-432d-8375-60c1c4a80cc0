/* 分类管理页面样式 */
.category-management-container {
  padding: 24px;
}

/* 优化的树节点样式 */
.category-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.category-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-name {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  line-height: 1.4;
}

.category-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.category-code {
  color: #8c8c8c;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f5f5;
  padding: 1px 4px;
  border-radius: 3px;
}

.product-count {
  color: #1890ff;
  font-size: 11px;
  background: #e6f7ff;
  padding: 1px 6px;
  border-radius: 10px;
  border: 1px solid #91d5ff;
  white-space: nowrap;
}

.children-count {
  color: #52c41a;
  font-size: 11px;
  background: #f6ffed;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #b7eb8f;
  white-space: nowrap;
}

/* 树形结构样式优化 */
.ant-tree {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.ant-tree .ant-tree-node-content-wrapper {
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin: 3px 0;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.ant-tree .ant-tree-node-content-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(24, 144, 255, 0.05) 50%, transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.ant-tree .ant-tree-node-content-wrapper:hover::before {
  transform: translateX(100%);
}

.ant-tree .ant-tree-node-content-wrapper:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-color: #91d5ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
  color: white;
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  transform: translateY(-2px);
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .category-name {
  color: white;
  font-weight: 700;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .category-code {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .product-count {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .children-count {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tag {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 树节点图标样式 */
.ant-tree .ant-tree-iconEle {
  margin-right: 10px;
  color: #1890ff;
  font-size: 16px;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-iconEle {
  color: white;
}

/* 展开/收缩图标样式 */
.ant-tree .ant-tree-switcher {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
  margin-right: 8px;
}

.ant-tree .ant-tree-switcher:hover {
  background: #1890ff;
  border-color: #1890ff;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-tree .ant-tree-switcher:hover .anticon {
  color: white !important;
}

/* 自定义展开/收起图标 */
.ant-tree .ant-tree-switcher .anticon {
  transition: all 0.3s ease;
}

/* 连接线样式 */
.ant-tree .ant-tree-indent-unit {
  width: 24px;
}

.ant-tree .ant-tree-treenode {
  padding: 2px 0;
}

/* 树形连接线 */
.ant-tree.ant-tree-show-line .ant-tree-indent-unit::before {
  border-left: 1px solid #d9d9d9;
}

.ant-tree.ant-tree-show-line .ant-tree-switcher {
  background: #ffffff;
  border: 1px solid #d9d9d9;
}

.ant-tree.ant-tree-show-line .ant-tree-switcher:hover {
  border-color: #1890ff;
}

/* 右键菜单样式 */
.ant-dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-dropdown-menu-item {
  padding: 8px 12px;
  font-size: 14px;
}

.ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
}

.ant-dropdown-menu-item-danger:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 分类详情样式 */
.ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
}

.ant-descriptions-item-content {
  color: #262626;
}

/* 模态框样式优化 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input-number {
  width: 100%;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-management-container {
    padding: 16px;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    padding: 6px 8px;
  }

  .category-tree-node-simple .category-name {
    font-size: 13px;
  }
}

.ant-alert-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.ant-alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

/* 按钮组样式 */
.ant-space-item .ant-btn-link {
  padding: 0 4px;
  height: auto;
  line-height: 1.2;
}

/* 排序按钮样式 */
.category-actions .ant-btn-link[title="上移"],
.category-actions .ant-btn-link[title="下移"] {
  color: #722ed1;
}

.category-actions .ant-btn-link[title="上移"]:hover,
.category-actions .ant-btn-link[title="下移"]:hover {
  color: #531dab;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .category-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }
  
  .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .ant-modal-body {
    padding: 16px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  min-height: 200px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  font-size: 12px;
}

/* 分类级别标识 */
.category-level-1 .category-name {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.category-level-2 .category-name {
  font-size: 14px;
  font-weight: 500;
  color: #52c41a;
}

.category-level-3 .category-name {
  font-size: 13px;
  font-weight: 400;
  color: #fa8c16;
}

/* 自动分配排序值提示 */
.sort-auto-hint {
  color: #666;
  font-size: 12px;
  font-style: italic;
}

/* 分类路径显示 */
.category-path {
  color: #8c8c8c;
  font-size: 12px;
  background: #fafafa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

/* 编码选择提示 */
.code-selection-hint {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 16px;
}

.code-selection-hint .anticon {
  color: #52c41a;
  margin-right: 8px;
}

/* 表单验证样式 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 成功状态样式 */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-input-number,
.ant-form-item-has-success .ant-select-selector {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}
