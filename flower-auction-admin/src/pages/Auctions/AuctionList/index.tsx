import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { auctionService } from '../../../services/auctionService';
import FormMessage from '../../../components/FormMessage';
import { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';
import dayjs from 'dayjs';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 拍卖会状态枚举
export enum AuctionStatus {
  DRAFT = 1,        // 草稿
  SCHEDULED = 2,    // 已安排
  ONGOING = 3,      // 进行中
  PAUSED = 4,       // 已暂停
  COMPLETED = 5,    // 已完成
  CANCELLED = 6,    // 已取消
}

// 拍卖会数据接口
export interface Auction {
  id: number;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: AuctionStatus;
  totalItems: number;
  soldItems: number;
  totalAmount: number;
  participantCount: number;
  creatorName: string;
  location: string;
  createdAt: string;
  updatedAt: string;
  auctioneerID?: number; // 添加可选的auctioneerID字段
  auctioneerId?: number; // 添加后端实际返回的auctioneerId字段
}

// 查询参数接口
interface AuctionQueryParams {
  title?: string;
  status?: AuctionStatus;
  creatorName?: string;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const AuctionList: React.FC = () => {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);
  const [viewingAuction, setViewingAuction] = useState<Auction | null>(null);
  const [saving, setSaving] = useState(false);
  const [statistics, setStatistics] = useState({
    totalAuctions: 0,
    ongoingAuctions: 0,
    todayAuctions: 0,
    totalParticipants: 0,
  });
  const [auctioneers, setAuctioneers] = useState<{id: number, name: string}[]>([]);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  const {
    formError,
    formSuccess,
    setFormError,
    setFormSuccess,
    clearAllMessages
  } = useFormMessage();

  // 拍卖会状态映射
  const auctionStatusMap = {
    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },
    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },
    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },
    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },
    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },
    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },
  };

  // 获取拍卖会列表
  const fetchAuctions = async () => {
    setLoading(true);
    try {
      const response = await auctionService.getAuctionList(queryParams);
      if (response.success) {
        // 将后端的name字段映射为前端的title字段
        const mappedAuctions = response.data.list.map((auction: any) => ({
          ...auction,
          title: auction.name || auction.title, // 后端返回name，前端使用title
          totalItems: auction.totalItems || 0,
          totalAmount: auction.totalAmount || 0,
          participantCount: auction.participantCount || 0,
          soldItems: auction.soldItems || 0,
          creatorName: auction.creatorName || '未知',
          // 确保auctioneerId字段被保留
          auctioneerId: auction.auctioneerId,
        }));
        setAuctions(mappedAuctions);
        setTotal(response.data.total);
        console.log('获取到拍卖会列表:', mappedAuctions);
      } else {
        message.error(response.message || '获取拍卖会列表失败');
        setAuctions([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取拍卖会列表失败:', error);
      let errorMsg = '获取拍卖会列表失败';
      if (error.response) {
        const { status } = error.response;
        if (status === 401) {
          errorMsg = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMsg = '没有权限访问拍卖会列表';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请稍后重试';
        }
      }
      message.error(errorMsg);
      setAuctions([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取拍卖会统计
  const fetchStatistics = async () => {
    try {
      const response = await auctionService.getAuctionStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取拍卖会统计失败:', error);
    }
  };

  // 获取拍卖师列表
  const fetchAuctioneers = async () => {
    try {
      // 获取拍卖师类型的用户 (user_type=1)
      const response = await fetch('http://localhost:8081/api/v1/users?user_type=1&page=1&pageSize=100');
      const data = await response.json();

      // 处理用户API的响应格式：{success: true, data: {list: []}}
      if (data.success && data.data && data.data.list) {
        // 过滤出拍卖师用户（userType=1）
        const auctioneerUsers = data.data.list.filter((user: any) => user.userType === 1);
        const auctioneerList = auctioneerUsers.map((user: any) => ({
          id: user.id,
          name: user.realName || user.username
        }));
        setAuctioneers(auctioneerList);
        console.log('获取到拍卖师列表:', auctioneerList);
      } else {
        console.warn('拍卖师数据格式异常:', data);
        setAuctioneers([]);
      }
    } catch (error) {
      console.error('获取拍卖师列表失败:', error);
      setAuctioneers([]);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchAuctions();
    fetchStatistics();
    fetchAuctioneers();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增拍卖会
  const handleAdd = () => {
    setEditingAuction(null);
    form.resetFields();
    
    // 设置默认值，不默认选择拍卖师
    form.setFieldsValue({
      // 默认时间范围为当前时间后的一天
      timeRange: [dayjs().add(1, 'hour'), dayjs().add(1, 'day')],
    });
    
    clearAllMessages();
    setIsModalVisible(true);
  };

  // 查看拍卖会详情
  const handleView = (auction: Auction) => {
    setViewingAuction(auction);
    setIsViewModalVisible(true);
  };

  // 编辑拍卖会
  const handleEdit = async (auction: Auction) => {
    setEditingAuction(auction);
    try {
      // 获取拍卖会详情，以获取更完整的信息（包括auctioneerId）
      const response = await auctionService.getAuctionDetail(auction.id);
      if (response.success) {
        const auctionDetail = response.data as Auction & { auctioneerID?: number, auctioneerId?: number };
        // 使用详情中的auctioneerId，如果存在的话
        const auctioneerId = auctionDetail.auctioneerId || auctionDetail.auctioneerID || null;
        
        console.log('拍卖会详情:', auctionDetail);
        console.log('拍卖师ID:', auctioneerId);
        
    form.setFieldsValue({
      title: auction.title, // 前端表单使用title字段
      description: auction.description,
          location: auction.location || '', // 确保location有值
          auctioneerID: auctioneerId, // 设置拍卖师ID，如果没有则为null
      timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],
    });
      } else {
        // 如果获取详情失败，使用现有数据
        form.setFieldsValue({
          title: auction.title,
          description: auction.description,
          location: auction.location || '',
          auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId
          timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],
        });
      }
    } catch (error) {
      console.error('获取拍卖会详情失败:', error);
      // 出错时使用现有数据
      form.setFieldsValue({
        title: auction.title,
        description: auction.description,
        location: auction.location || '',
        auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId
        timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],
      });
    }
    
    clearAllMessages();
    setIsModalVisible(true);
  };

  // 删除拍卖会
  const handleDelete = async (id: number) => {
    try {
      const response = await auctionService.deleteAuction(id);
      if (response.success) {
        message.success('删除成功');
        fetchAuctions();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存拍卖会
  const handleSave = async (values: any) => {
    setSaving(true);
    clearAllMessages();

    try {
      const auctionData = {
        name: values.title, // 将title字段映射为name
        description: values.description,
        auctioneerId: values.auctioneerID, // 将前端的auctioneerID映射为后端的auctioneerId
        location: values.location,
        startTime: values.timeRange[0].toISOString(),
        endTime: values.timeRange[1].toISOString(),
      };

      let response;
      if (editingAuction) {
        response = await auctionService.updateAuction(editingAuction.id, auctionData);
      } else {
        response = await auctionService.createAuction(auctionData);
      }

      const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';

      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {
        // 成功：延迟关闭模态框
        setTimeout(() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingAuction(null);
          clearAllMessages();
          fetchAuctions();
          fetchStatistics(); // 刷新统计数据
        }, 1500);
      }
    } catch (error: any) {
      handleApiError(error, setFormError);
    } finally {
      setSaving(false);
    }
  };

  // 开始拍卖
  const handleStart = async (id: number) => {
    const auction = auctions.find(a => a.id === id);
    if (!auction) return;

    Modal.confirm({
      title: '确认开始拍卖',
      content: (
        <div>
          <p>确定要开始拍卖会 <strong>"{auction.title}"</strong> 吗？</p>
          <p style={{ color: '#999', fontSize: '12px' }}>
            开始后将无法修改拍卖信息，请确保所有设置正确
          </p>
        </div>
      ),
      okText: '确认开始',
      cancelText: '取消',
      okButtonProps: { type: 'primary' },
      onOk: async () => {
        try {
          const response = await auctionService.startAuction(id);
          if (response.success) {
            message.success({
              content: `拍卖会"${auction.title}"已成功开始！`,
              duration: 3,
            });
            fetchAuctions();
          } else {
            message.error({
              content: response.message || '开始拍卖失败，请稍后重试',
              duration: 5,
            });
          }
        } catch (error: any) {
          console.error('开始拍卖失败:', error);
          let errorMsg = '开始拍卖失败';
          if (error.response?.status === 400) {
            errorMsg = '拍卖会状态不允许开始，请检查拍卖设置';
          } else if (error.response?.status === 409) {
            errorMsg = '拍卖会已经开始或存在冲突';
          } else {
            errorMsg = error.message || '网络错误，请检查连接后重试';
          }
          message.error({
            content: errorMsg,
            duration: 5,
          });
        }
      },
    });
  };

  // 暂停拍卖
  const handlePause = async (id: number) => {
    const auction = auctions.find(a => a.id === id);
    if (!auction) return;

    Modal.confirm({
      title: '确认暂停拍卖',
      content: (
        <div>
          <p>确定要暂停拍卖会 <strong>"{auction.title}"</strong> 吗？</p>
          <p style={{ color: '#999', fontSize: '12px' }}>
            暂停后可以重新开始，但会影响参与者的竞拍体验
          </p>
        </div>
      ),
      okText: '确认暂停',
      cancelText: '取消',
      okButtonProps: { type: 'default' },
      onOk: async () => {
        try {
          const response = await auctionService.pauseAuction(id);
          if (response.success) {
            message.success({
              content: `拍卖会"${auction.title}"已暂停`,
              duration: 3,
            });
            fetchAuctions();
          } else {
            message.error({
              content: response.message || '暂停拍卖失败，请稍后重试',
              duration: 5,
            });
          }
        } catch (error: any) {
          console.error('暂停拍卖失败:', error);
          message.error({
            content: error.message || '暂停拍卖失败，请检查网络连接后重试',
            duration: 5,
          });
        }
      },
    });
  };

  // 结束拍卖
  const handleEnd = async (id: number) => {
    const auction = auctions.find(a => a.id === id);
    if (!auction) return;

    Modal.confirm({
      title: '确认结束拍卖',
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      content: (
        <div>
          <p>确定要结束拍卖会 <strong>"{auction.title}"</strong> 吗？</p>
          <p style={{ color: '#ff4d4f', fontSize: '12px', fontWeight: 'bold' }}>
            ⚠️ 警告：结束后将无法恢复，请确保所有交易已完成
          </p>
        </div>
      ),
      okText: '确认结束',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          const response = await auctionService.endAuction(id);
          if (response.success) {
            message.success({
              content: `拍卖会"${auction.title}"已成功结束！`,
              duration: 3,
            });
            fetchAuctions();
          } else {
            message.error({
              content: response.message || '结束拍卖失败，请稍后重试',
              duration: 5,
            });
          }
        } catch (error: any) {
          console.error('结束拍卖失败:', error);
          message.error({
            content: error.message || '结束拍卖失败，请检查网络连接后重试',
            duration: 5,
          });
        }
      },
    });
  };

  // 表格列定义
  const columns: ColumnsType<Auction> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '拍卖会标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '拍卖状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: AuctionStatus) => {
        const statusInfo = auctionStatusMap[status];
        return (
          <Badge
            status={
              status === AuctionStatus.ONGOING ? 'processing' :
              status === AuctionStatus.COMPLETED ? 'success' :
              status === AuctionStatus.CANCELLED ? 'error' : 'default'
            }
            text={
              <Tag color={statusInfo?.color || 'default'}>
                {statusInfo?.label || '未知'}
              </Tag>
            }
          />
        );
      },
    },
    {
      title: '商品数量',
      dataIndex: 'totalItems',
      key: 'totalItems',
      width: 100,
      render: (total: number, record: Auction) => (
        <div>
          <div>{total}件</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            已售: {record.soldItems}
          </div>
        </div>
      ),
    },
    {
      title: '成交金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => (
        <div style={{ fontWeight: 500, color: '#f50' }}>
          ¥{amount.toFixed(2)}
        </div>
      ),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 100,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 100,
    },
    {
      title: '拍卖时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
      render: (text: string, record: Auction) => (
        <div>
          <div>{new Date(text).toLocaleString()}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            至 {new Date(record.endTime).toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: Auction) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === AuctionStatus.SCHEDULED && (
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStart(record.id)}
            >
              开始
            </Button>
          )}
          {record.status === AuctionStatus.ONGOING && (
            <>
              <Button
                type="link"
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handlePause(record.id)}
              >
                暂停
              </Button>
              <Button
                type="link"
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleEnd(record.id)}
              >
                结束
              </Button>
            </>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="auction-list-container">
      <Title level={2}>拍卖管理</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总拍卖会"
              value={statistics.totalAuctions}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="进行中"
              value={statistics.ongoingAuctions}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日拍卖"
              value={statistics.todayAuctions}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总参与人数"
              value={statistics.totalParticipants}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="title" label="拍卖会标题">
                <Input placeholder="请输入拍卖会标题" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="拍卖状态">
                <Select placeholder="请选择拍卖状态" allowClear>
                  <Option value={AuctionStatus.DRAFT}>草稿</Option>
                  <Option value={AuctionStatus.SCHEDULED}>已安排</Option>
                  <Option value={AuctionStatus.ONGOING}>进行中</Option>
                  <Option value={AuctionStatus.PAUSED}>已暂停</Option>
                  <Option value={AuctionStatus.COMPLETED}>已完成</Option>
                  <Option value={AuctionStatus.CANCELLED}>已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="creatorName" label="创建人">
                <Input placeholder="请输入创建人姓名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="dateRange" label="拍卖时间">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增拍卖会
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAuctions}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 拍卖会列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={auctions}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 拍卖会编辑模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {editingAuction ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingAuction ? `编辑拍卖会 - ${editingAuction.title}` : '新增拍卖会'}</span>
          </div>
        }
        open={isModalVisible}
        onCancel={() => {
          if (saving) {
            message.warning('正在保存中，请稍候...');
            return;
          }
          setIsModalVisible(false);
          form.resetFields();
          setEditingAuction(null);
        }}
        footer={null}
        width={700}
        destroyOnClose
        maskClosable={!saving}
        closable={!saving}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Form.Item
            name="title"
            label="拍卖会标题"
            rules={[
              { required: true, message: '请输入拍卖会标题' },
              { min: 2, max: 100, message: '标题长度为2-100个字符' },
            ]}
          >
            <Input
              placeholder="请输入拍卖会标题"
              showCount
              maxLength={100}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="拍卖会描述"
            rules={[
              { max: 500, message: '描述不能超过500个字符' },
            ]}
          >
            <Input.TextArea
              placeholder="请输入拍卖会描述"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="timeRange"
            label="拍卖时间"
            rules={[
              { required: true, message: '请选择拍卖时间' },
              {
                validator: (_, value) => {
                  if (!value || !value[0] || !value[1]) {
                    return Promise.resolve();
                  }
                  const [start, end] = value;
                  const now = new Date();

                  // 检查开始时间不能早于当前时间
                  if (start.isBefore(now)) {
                    return Promise.reject(new Error('开始时间不能早于当前时间'));
                  }

                  // 检查结束时间必须晚于开始时间
                  if (end.isBefore(start)) {
                    return Promise.reject(new Error('结束时间必须晚于开始时间'));
                  }

                  // 检查拍卖时长不能少于30分钟
                  const duration = end.diff(start, 'minutes');
                  if (duration < 30) {
                    return Promise.reject(new Error('拍卖时长不能少于30分钟'));
                  }

                  // 检查拍卖时长不能超过24小时
                  if (duration > 24 * 60) {
                    return Promise.reject(new Error('拍卖时长不能超过24小时'));
                  }

                  return Promise.resolve();
                },
              },
            ]}
            extra="拍卖时长建议在30分钟到24小时之间"
          >
            <RangePicker
              showTime={{
                format: 'HH:mm',
                minuteStep: 15, // 15分钟间隔
              }}
              format="YYYY-MM-DD HH:mm"
              style={{ width: '100%' }}
              placeholder={['选择开始时间', '选择结束时间']}
              disabledDate={(current) => {
                // 禁用今天之前的日期
                return current && current.isBefore(new Date(), 'day');
              }}
              disabledTime={(current, type) => {
                const now = new Date();
                const isToday = current && current.isSame(now, 'day');

                if (type === 'start' && isToday) {
                  // 如果是今天，禁用当前时间之前的时间
                  return {
                    disabledHours: () => {
                      const hours = [];
                      for (let i = 0; i < now.getHours(); i++) {
                        hours.push(i);
                      }
                      return hours;
                    },
                    disabledMinutes: (selectedHour: number) => {
                      if (selectedHour === now.getHours()) {
                        const minutes = [];
                        for (let i = 0; i < now.getMinutes(); i++) {
                          minutes.push(i);
                        }
                        return minutes;
                      }
                      return [];
                    },
                  };
                }
                return {};
              }}
              showNow={false}
              allowClear={false}
            />
          </Form.Item>

          <Form.Item
            name="location"
            label="拍卖地点"
            rules={[
              { required: true, message: '请输入拍卖地点' },
              { min: 2, max: 200, message: '地点长度为2-200个字符' },
            ]}
          >
            <Input
              placeholder="请输入拍卖地点"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="auctioneerID"
            label="拍卖师"
            rules={[
              { required: true, message: '请选择拍卖师' },
            ]}
          >
            <Select
              placeholder="请选择拍卖师"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                String(option?.children ?? '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {auctioneers.map(auctioneer => (
                <Option key={auctioneer.id} value={auctioneer.id}>
                  {auctioneer.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 错误和成功消息显示 */}
          <FormMessage type="error" message={formError} visible={!!formError} />
          <FormMessage type="success" message={formSuccess} visible={!!formSuccess} />

          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>
            <div style={{
              borderTop: '1px solid #f0f0f0',
              paddingTop: '16px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div style={{ color: '#666', fontSize: '12px' }}>
                {editingAuction ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建拍卖会'}
              </div>
              <Space>
                <Button
                  onClick={() => {
                    if (saving) {
                      message.warning('正在保存中，请稍候...');
                      return;
                    }
                    setIsModalVisible(false);
                    form.resetFields();
                    setEditingAuction(null);
                    clearAllMessages();
                  }}
                  disabled={saving}
                  size="middle"
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  disabled={saving}
                  size="middle"
                  icon={editingAuction ? <EditOutlined /> : <PlusOutlined />}
                >
                  {saving ? '保存中...' : (editingAuction ? '更新拍卖会' : '创建拍卖会')}
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看详情模态框 */}
      <Modal
        title="拍卖会详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false);
          setViewingAuction(null);
        }}
        footer={[
          <Button key="close" onClick={() => {
            setIsViewModalVisible(false);
            setViewingAuction(null);
          }}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingAuction && (
          <div style={{ padding: '16px 0' }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>拍卖会标题：</strong>
                  <div style={{ marginTop: 4 }}>{viewingAuction.title}</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>拍卖状态：</strong>
                  <div style={{ marginTop: 4 }}>
                    <Tag color={auctionStatusMap[viewingAuction.status]?.color || 'default'}>
                      {auctionStatusMap[viewingAuction.status]?.label || '未知'}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div style={{ marginBottom: 16 }}>
                  <strong>拍卖描述：</strong>
                  <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                    {viewingAuction.description || '暂无描述'}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>拍卖地点：</strong>
                  <div style={{ marginTop: 4 }}>{viewingAuction.location || '暂无地点信息'}</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>创建人：</strong>
                  <div style={{ marginTop: 4 }}>{viewingAuction.creatorName}</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>开始时间：</strong>
                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.startTime).toLocaleString()}</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <strong>结束时间：</strong>
                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.endTime).toLocaleString()}</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 16 }}>
                  <strong>商品总数：</strong>
                  <div style={{ marginTop: 4, fontSize: '18px', color: '#1890ff' }}>{viewingAuction.totalItems}件</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 16 }}>
                  <strong>已售商品：</strong>
                  <div style={{ marginTop: 4, fontSize: '18px', color: '#52c41a' }}>{viewingAuction.soldItems}件</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 16 }}>
                  <strong>参与人数：</strong>
                  <div style={{ marginTop: 4, fontSize: '18px', color: '#722ed1' }}>{viewingAuction.participantCount}人</div>
                </div>
              </Col>
              <Col span={24}>
                <div style={{ marginBottom: 16 }}>
                  <strong>成交金额：</strong>
                  <div style={{ marginTop: 4, fontSize: '24px', color: '#f50', fontWeight: 'bold' }}>
                    ¥{viewingAuction.totalAmount.toFixed(2)}
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AuctionList;