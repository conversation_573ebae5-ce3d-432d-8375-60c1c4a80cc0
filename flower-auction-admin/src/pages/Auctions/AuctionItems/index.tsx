import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  InputNumber,
  Image,
  Popconfirm,
  Statistic,
  Badge,
  Tooltip,
  DatePicker,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  AuditOutlined,
} from '@ant-design/icons';
// import type { ColumnsType } from 'antd/es/table';
import { auctionService } from '../../../services/auctionService';
import { productService } from '../../../services/productService';
import FormMessage from '../../../components/FormMessage';
import { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';

const { Title } = Typography;
const { Option } = Select;

// 拍卖商品状态枚举
export enum AuctionItemStatus {
  PENDING = 0,     // 待拍卖
  ONGOING = 1,     // 拍卖中
  SOLD = 2,        // 已成交
  UNSOLD = 3,      // 流拍
  WITHDRAWN = 4,   // 撤回
}

// 拍卖商品接口
export interface AuctionItem {
  id: number;
  auctionId: number;
  productId: number;
  productName: string;
  productCode: string;
  startingPrice: number;
  currentPrice: number;
  reservePrice?: number;
  bidIncrement: number;
  bidCount: number;
  status: AuctionItemStatus;
  images?: string[];
  category: string;
  quality: string;
  quantity: number;
  unit: string;
  highestBidder?: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface AuctionItemQueryParams {
  auctionId?: number;
  productName?: string;
  status?: AuctionItemStatus;
  category?: string;
  page: number;
  pageSize: number;
}

const AuctionItems: React.FC = () => {
  const [auctionItems, setAuctionItems] = useState<AuctionItem[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [auctions, setAuctions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuctionItemQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<AuctionItem | null>(null);
  const [saving, setSaving] = useState(false);
  const [statistics, setStatistics] = useState({
    totalItems: 0,
    pendingItems: 0,
    ongoingItems: 0,
    soldItems: 0,
    totalValue: 0,
  });
  const [form] = Form.useForm();

  const {
    formError,
    formSuccess,
    setFormError,
    setFormSuccess,
    clearAllMessages
  } = useFormMessage();

  // 拍卖商品状态映射
  const itemStatusMap = {
    [AuctionItemStatus.PENDING]: { label: '待拍卖', color: 'default' },
    [AuctionItemStatus.ONGOING]: { label: '拍卖中', color: 'blue' },
    [AuctionItemStatus.SOLD]: { label: '已成交', color: 'green' },
    [AuctionItemStatus.UNSOLD]: { label: '流拍', color: 'orange' },
    [AuctionItemStatus.WITHDRAWN]: { label: '撤回', color: 'red' },
  };

  // 获取拍卖商品列表
  const fetchAuctionItems = async () => {
    setLoading(true);
    try {
      // 调用后端API获取拍卖商品列表
      const apiParams = {
        ...queryParams,
        // 确保状态参数是数字类型
        status: typeof queryParams.status === 'number' ? queryParams.status : undefined,
      };
      
      console.log('请求参数:', apiParams);
      console.log('请求URL:', `/auction-items`);
      
      const response = await auctionService.getAuctionItemList(apiParams);
      console.log('API响应完整数据:', JSON.stringify(response));

      if (response.success && response.data) {
        // 检查数据结构
        console.log('响应数据结构:', Object.keys(response.data));
        
        // 确保list字段存在且是数组
        const itemsList = response.data.list || [];
        if (!Array.isArray(itemsList)) {
          console.error('列表数据不是数组:', itemsList);
          setAuctionItems([]);
          setTotal(0);
          return;
        }
        
        console.log('原始拍卖商品数据:', itemsList);
        
        // 处理拍卖商品数据，映射字段
        const mappedItems = itemsList.map((item: any) => {
          console.log('处理项目:', item);
          
          // 从product中获取商品信息
          const product = item.product || {};
          console.log('商品信息:', product);
          
          // 创建映射后的对象
          const mappedItem: AuctionItem = {
            id: item.id || 0,
            auctionId: item.auctionId || 0,
            productId: item.productId || 0,
            productName: product.name || item.productName || '未知商品',
            productCode: product.code || item.productCode || `商品${item.productId || 0}`,
            quantity: product.quantity || item.quantity || 1,
            unit: product.unit || item.unit || '件',
            images: product.images || item.images || [],
            startingPrice: item.startPrice || item.startingPrice || 0,
            currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,
            reservePrice: item.reservePrice || 0,
            bidIncrement: item.stepPrice || item.bidIncrement || 10,
            bidCount: item.totalBids || item.bidCount || 0,
            highestBidder: item.winnerName || item.highestBidder || '',
            status: typeof item.status === 'number' ? item.status : 
                   item.status === 'PENDING' ? AuctionItemStatus.PENDING :
                   item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :
                   item.status === 'SOLD' ? AuctionItemStatus.SOLD :
                   item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :
                   item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : 
                   AuctionItemStatus.PENDING,
            createdAt: item.createdAt || '',
            updatedAt: item.updatedAt || '',
            category: product.category || item.category || '',
            quality: product.qualityLevel || item.quality || '',
          };
          
          console.log('映射后的项目:', mappedItem);
          return mappedItem;
        });

        console.log('最终映射后的数据:', mappedItems);
        setAuctionItems(mappedItems);
        setTotal(response.data.total || 0);
        
        // 更新统计信息
        const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;
        const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;
        const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;
        const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);
        
        setStatistics({
          totalItems: mappedItems.length,
          pendingItems,
          ongoingItems,
          soldItems,
          totalValue,
        });
      } else {
        console.warn('拍卖商品数据格式异常:', response);
        setAuctionItems([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取拍卖商品列表失败:', error);
      message.error(error.message || '获取拍卖商品列表失败');
      setAuctionItems([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取商品列表（用于添加拍卖商品）
  const fetchProducts = async () => {
    try {
      const response = await productService.getProductList({
        page: 1,
        pageSize: 100,
        auditStatus: 'approved', // 只获取已审核的商品
      });

      if (response.success && response.data && response.data.list) {
        setProducts(response.data.list);
        console.log('获取到商品列表:', response.data.list);
      } else {
        console.warn('商品数据格式异常:', response);
        setProducts([]);
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error);
      setProducts([]);
    }
  };

  // 获取拍卖会列表
  const fetchAuctions = async () => {
    try {
      const response = await auctionService.getAuctionList({
        page: 1,
        pageSize: 100,
      });

      if (response.success && response.data && response.data.list) {
        // 将后端的name字段映射为前端的title字段
        const mappedAuctions = response.data.list.map((auction: any) => ({
          ...auction,
          title: auction.name || auction.title, // 后端返回name，前端使用title
        }));
        setAuctions(mappedAuctions);
        console.log('获取到拍卖会列表:', mappedAuctions);
      } else {
        console.warn('拍卖会数据格式异常:', response);
        setAuctions([]);
      }
    } catch (error: any) {
      console.error('获取拍卖会列表失败:', error);
      setAuctions([]);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      // 这里需要调用后端API获取统计信息
      // 暂时使用模拟数据
      setStatistics({
        totalItems: 0,
        pendingItems: 0,
        ongoingItems: 0,
        soldItems: 0,
        totalValue: 0,
      });
    } catch (error: any) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 检查API连接
  const checkApiConnection = async () => {
    try {
      message.loading('正在检查API连接...');
      
      // 尝试请求API基础URL
      const baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';
      
      // 使用fetch直接请求，避免axios拦截器的影响
      const response = await fetch(`${baseUrl}/auction-items?page=1&pageSize=1`);
      const data = await response.json();
      
      console.log('API连接测试结果:', data);
      
      if (response.ok) {
        message.success(`API连接正常，响应状态: ${response.status}`);
      } else {
        message.error(`API连接异常，响应状态: ${response.status}`);
      }
      
      // 显示响应数据结构
      if (data) {
        console.log('响应数据结构:', Object.keys(data));
        message.info(`响应数据结构: ${JSON.stringify(Object.keys(data))}`);
      }
    } catch (error: any) {
      console.error('API连接测试失败:', error);
      message.error(`API连接测试失败: ${error.message}`);
    }
  };

  // 初始化加载
  useEffect(() => {
    // 修改初始化请求，减少重复请求
    const initData = async () => {
      setLoading(true);
      try {
        console.log('开始初始化数据加载');
        // 并行请求数据，减少请求次数
        const [itemsResponse, auctionsResponse, productsResponse] = await Promise.all([
          auctionService.getAuctionItemList({
            ...queryParams,
            // 枚举现在已经是数字类型，可以直接使用
            status: queryParams.status,
            pageSize: 50, // 使用更大的页面大小，减少请求次数
          }),
          auctionService.getAuctionList({
            page: 1,
            pageSize: 50, // 使用更大的页面大小，减少请求次数
          }),
          productService.getProductList({
            page: 1,
            pageSize: 50, // 使用更大的页面大小，减少请求次数
            auditStatus: 'approved',
          }),
        ]);

        console.log('初始化数据请求完成');
        console.log('拍卖商品响应:', JSON.stringify(itemsResponse));
        console.log('拍卖会响应:', JSON.stringify(auctionsResponse));
        console.log('商品响应:', JSON.stringify(productsResponse));

        // 处理拍卖会数据
        if (auctionsResponse.success && auctionsResponse.data && auctionsResponse.data.list) {
          const mappedAuctions = auctionsResponse.data.list.map((auction: any) => ({
            ...auction,
            title: auction.name || auction.title,
          }));
          setAuctions(mappedAuctions);
          console.log('处理后的拍卖会数据:', mappedAuctions);
        }

        // 处理商品数据
        if (productsResponse.success && productsResponse.data && productsResponse.data.list) {
          setProducts(productsResponse.data.list);
          console.log('处理后的商品数据:', productsResponse.data.list);
        }

        // 处理拍卖商品数据
        if (itemsResponse.success && itemsResponse.data) {
          console.log('拍卖商品数据结构:', Object.keys(itemsResponse.data));
          
          // 确保list字段存在且是数组
          const itemsList = itemsResponse.data.list || [];
          if (!Array.isArray(itemsList)) {
            console.error('列表数据不是数组:', itemsList);
            setAuctionItems([]);
            setTotal(0);
            return;
          }
          
          console.log('拍卖商品原始数据:', itemsList);
          
          const mappedItems = itemsList.map((item: any) => {
            // 从product中获取商品信息
            const product = item.product || {};
            console.log('处理商品项:', item.id, '商品信息:', product);
            
            // 状态字段现在是数字，可以直接使用
            const status = typeof item.status === 'number' ? item.status : 
                          item.status === 'PENDING' ? AuctionItemStatus.PENDING :
                          item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :
                          item.status === 'SOLD' ? AuctionItemStatus.SOLD :
                          item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :
                          item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : 
                          AuctionItemStatus.PENDING;
            
            return {
              id: item.id,
              auctionId: item.auctionId,
              productId: item.productId,
              productName: product.name || item.productName || '未知商品',
              productCode: product.code || item.productCode || `商品${item.productId}`,
              quantity: product.quantity || item.quantity || 1,
              unit: product.unit || item.unit || '件',
              images: product.images || item.images || [],
              // 价格字段映射
              startingPrice: item.startPrice || item.startingPrice || 0,
              currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,
              reservePrice: item.reservePrice || 0,
              bidIncrement: item.stepPrice || item.bidIncrement || 10,
              bidCount: item.totalBids || item.bidCount || 0,
              highestBidder: item.winnerName || item.highestBidder || '',
              status: status,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
              category: product.category || item.category || '',
              quality: product.qualityLevel || item.quality || '',
            };
          });

          console.log('映射后的拍卖商品数据:', mappedItems);
          setAuctionItems(mappedItems);
          setTotal(itemsResponse.data.total || 0);
          
          // 更新统计信息
          const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;
          const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;
          const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;
          const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);
          
          setStatistics({
            totalItems: mappedItems.length,
            pendingItems,
            ongoingItems,
            soldItems,
            totalValue,
          });
        } else {
          console.warn('拍卖商品数据格式异常:', itemsResponse);
          setAuctionItems([]);
          setTotal(0);
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
        message.error('获取数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    // 首次加载或重置查询参数时，使用并行请求
    if (
      queryParams.page === 1 && 
      !queryParams.auctionId && 
      !queryParams.productName && 
      !queryParams.status
    ) {
      initData();
    } else {
      // 搜索或翻页时，只请求拍卖商品列表
      fetchAuctionItems();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增拍卖商品
  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    clearAllMessages();
    setIsModalVisible(true);
  };

  // 编辑拍卖商品
  const handleEdit = (item: AuctionItem) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    clearAllMessages();
    setIsModalVisible(true);
  };

  // 删除拍卖商品
  const handleDelete = async (id: number) => {
    try {
      // 调用删除API
      message.success('删除成功');
      fetchAuctionItems();
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存拍卖商品
  const handleSave = async (values: any) => {
    setSaving(true);
    clearAllMessages();

    try {
      let response;
      if (editingItem) {
        // 更新拍卖商品 - 目前暂不支持更新，显示提示信息
        setFormError('暂不支持更新拍卖商品，请删除后重新添加');
        setSaving(false);
        return;
      } else {
        // 添加拍卖商品
        response = await auctionService.addAuctionItem(values.auctionId, {
          productId: values.productId,
          startingPrice: values.startingPrice,
          reservePrice: values.reservePrice,
          bidIncrement: values.bidIncrement,
          startTime: values.startTime ? values.startTime.toISOString() : undefined,
        });
      }

      const successMsg = '拍卖商品添加成功！';

      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {
        // 成功：延迟关闭模态框
        setTimeout(() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingItem(null);
          clearAllMessages();
          fetchAuctionItems();
          fetchStatistics();
        }, 1500);
      }
    } catch (error: any) {
      handleApiError(error, setFormError);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>拍卖商品管理</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总商品数"
              value={statistics.totalItems}
              prefix={<AuditOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待拍卖"
              value={statistics.pendingItems}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="拍卖中"
              value={statistics.ongoingItems}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已成交"
              value={statistics.soldItems}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small" style={{ marginBottom: 16 }}>
        <Form
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="productName" label="商品名称">
                <Input placeholder="请输入商品名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="拍卖状态">
                <Select placeholder="请选择拍卖状态" allowClear>
                  <Option value={AuctionItemStatus.PENDING}>待拍卖</Option>
                  <Option value={AuctionItemStatus.ONGOING}>拍卖中</Option>
                  <Option value={AuctionItemStatus.SOLD}>已成交</Option>
                  <Option value={AuctionItemStatus.UNSOLD}>流拍</Option>
                  <Option value={AuctionItemStatus.WITHDRAWN}>撤回</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="auctionId" label="拍卖会">
                <Select placeholder="请选择拍卖会" allowClear>
                  {auctions.map(auction => (
                    <Option key={auction.id} value={auction.id}>
                      {auction.title}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加拍卖商品
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAuctionItems}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 拍卖商品列表表格 */}
      <Card>
        {/* 添加调试信息，显示当前数据状态 */}
        <div style={{ marginBottom: 16 }}>
          <p>当前数据状态: {loading ? '加载中...' : `共 ${auctionItems.length} 条记录`}</p>
          {auctionItems.length === 0 && !loading && (
            <p style={{ color: 'red' }}>未找到拍卖商品数据，请检查数据映射或网络请求</p>
          )}
          {/* 添加调试按钮 */}
          <Button 
            type="primary" 
            onClick={() => {
              console.log('当前拍卖商品数据:', auctionItems);
              message.info(`当前数据条数: ${auctionItems.length}`);
            }}
            style={{ marginRight: 8 }}
          >
            调试数据
          </Button>
          <Button 
            type="primary" 
            onClick={checkApiConnection}
            style={{ marginRight: 8 }}
          >
            测试API连接
          </Button>
          <Button 
            onClick={fetchAuctionItems}
            style={{ marginRight: 8 }}
          >
            重新获取数据
          </Button>
        </div>
        
        <Table
          columns={[
            {
              title: 'ID',
              dataIndex: 'id',
              key: 'id',
              width: 80,
            },
            {
              title: '商品信息',
              key: 'product',
              width: 250,
              render: (_, record: AuctionItem) => (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {record.images && record.images.length > 0 && (
                    <Image
                      width={60}
                      height={60}
                      src={record.images[0]}
                      style={{ marginRight: 12, borderRadius: 4 }}
                      fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                    />
                  )}
                  <div>
                    <div style={{ fontWeight: 500 }}>{record.productName}</div>
                    <div style={{ fontSize: 12, color: '#999' }}>
                      编号: {record.productCode}
                    </div>
                    <div style={{ fontSize: 12, color: '#999' }}>
                      {record.quantity} {record.unit}
                    </div>
                  </div>
                </div>
              ),
            },
            {
              title: '拍卖状态',
              dataIndex: 'status',
              key: 'status',
              width: 100,
              render: (status: AuctionItemStatus) => {
                console.log('渲染状态:', status, typeof status);
                // 确保状态是数字类型
                const numStatus = typeof status === 'number' ? status : 
                                 status === 'PENDING' ? AuctionItemStatus.PENDING :
                                 status === 'ONGOING' ? AuctionItemStatus.ONGOING :
                                 status === 'SOLD' ? AuctionItemStatus.SOLD :
                                 status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :
                                 status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : 
                                 AuctionItemStatus.PENDING;
                
                const statusInfo = itemStatusMap[numStatus];
                console.log('状态信息:', statusInfo);
                
                return (
                  <Badge
                    status={
                      numStatus === AuctionItemStatus.ONGOING ? 'processing' :
                      numStatus === AuctionItemStatus.SOLD ? 'success' :
                      numStatus === AuctionItemStatus.UNSOLD ? 'warning' :
                      numStatus === AuctionItemStatus.WITHDRAWN ? 'error' : 'default'
                    }
                    text={
                      <Tag color={statusInfo?.color || 'default'}>
                        {statusInfo?.label || `未知(${numStatus})`}
                      </Tag>
                    }
                  />
                );
              },
            },
            {
              title: '价格信息',
              key: 'price',
              width: 150,
              render: (_, record: AuctionItem) => (
                <div>
                  <div>起拍: ¥{record.startingPrice.toFixed(2)}</div>
                  <div style={{ color: '#f50', fontWeight: 500 }}>
                    当前: ¥{record.currentPrice.toFixed(2)}
                  </div>
                  {record.reservePrice && (
                    <div style={{ fontSize: 12, color: '#999' }}>
                      保留: ¥{record.reservePrice.toFixed(2)}
                    </div>
                  )}
                </div>
              ),
            },
            {
              title: '竞价信息',
              key: 'bid',
              width: 120,
              render: (_, record: AuctionItem) => (
                <div>
                  <div>出价次数: {record.bidCount}</div>
                  <div>加价幅度: ¥{record.bidIncrement.toFixed(2)}</div>
                  {record.highestBidder && (
                    <div style={{ fontSize: 12, color: '#999' }}>
                      最高出价人: {record.highestBidder}
                    </div>
                  )}
                </div>
              ),
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              width: 160,
              render: (text: string) => new Date(text).toLocaleString(),
            },
            {
              title: '操作',
              key: 'action',
              width: 200,
              fixed: 'right',
              render: (_, record: AuctionItem) => (
                <Space size="small">
                  <Tooltip title="查看详情">
                    <Button
                      type="link"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => {/* 查看详情 */}}
                    />
                  </Tooltip>
                  <Tooltip title="编辑">
                    <Button
                      type="link"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEdit(record)}
                      disabled={record.status === AuctionItemStatus.ONGOING}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="确定要删除这个拍卖商品吗？"
                    onConfirm={() => handleDelete(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Tooltip title="删除">
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        disabled={record.status === AuctionItemStatus.ONGOING}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={auctionItems}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 拍卖商品编辑模态框 */}
      <Modal
        title={editingItem ? '编辑拍卖商品' : '添加拍卖商品'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Form.Item
            name="auctionId"
            label="拍卖会"
            rules={[{ required: true, message: '请选择拍卖会' }]}
          >
            <Select placeholder="请选择拍卖会">
              {auctions.map(auction => (
                <Option key={auction.id} value={auction.id}>
                  {auction.title}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="productId"
            label="商品"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <Select placeholder="请选择商品" showSearch optionFilterProp="children">
              {products.map(product => (
                <Option key={product.id} value={product.id}>
                  {product.name} - {product.code}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startingPrice"
                label="起拍价"
                rules={[
                  { required: true, message: '请输入起拍价' },
                  { type: 'number', min: 0.01, message: '起拍价必须大于0' },
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入起拍价"
                  precision={2}
                  min={0.01}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reservePrice"
                label="保留价（可选）"
                rules={[
                  { type: 'number', min: 0.01, message: '保留价必须大于0' },
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入保留价"
                  precision={2}
                  min={0.01}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="bidIncrement"
            label="加价幅度"
            rules={[
              { required: true, message: '请输入加价幅度' },
              { type: 'number', min: 0.01, message: '加价幅度必须大于0' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入加价幅度"
              precision={2}
              min={0.01}
              addonBefore="¥"
            />
          </Form.Item>

          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[
              { required: true, message: '请选择开始时间' },
            ]}
          >
            <DatePicker
              style={{ width: '100%' }}
              showTime
              placeholder="请选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          {/* 错误和成功消息显示 */}
          <FormMessage type="error" message={formError} visible={!!formError} />
          <FormMessage type="success" message={formSuccess} visible={!!formSuccess} />

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                  setEditingItem(null);
                  clearAllMessages();
                }}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                disabled={saving}
              >
                {saving ? '保存中...' : (editingItem ? '更新' : '添加')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AuctionItems;
