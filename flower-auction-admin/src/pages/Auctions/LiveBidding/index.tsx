import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Row,
  Col,
  Button,

  message,
  Typography,
  Space,
  Tag,
  Avatar,
  List,
  Statistic,
  Progress,
  Badge,
  Modal,
  Form,
  InputNumber,
  Alert,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  AuditOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { auctionService } from '../../../services/auctionService';

const { Title, Text } = Typography;

// 竞价记录接口
interface BidRecord {
  id: number;
  userId: number;
  username: string;
  avatar?: string;
  bidAmount: number;
  bidTime: string;
  isWinning: boolean;
}

// 拍卖商品接口
interface LiveAuctionItem {
  id: number;
  productName: string;
  productCode: string;
  images: string[];
  startingPrice: number;
  currentPrice: number;
  reservePrice?: number;
  bidIncrement: number;
  bidCount: number;
  timeRemaining: number; // 剩余时间（秒）
  status: 'pending' | 'ongoing' | 'ended';
  highestBidder?: string;
  description: string;
}

const LiveBidding: React.FC = () => {
  const [currentAuctionId, setCurrentAuctionId] = useState<number | null>(null);
  const [currentItem, setCurrentItem] = useState<LiveAuctionItem | null>(null);
  const [bidRecords, setBidRecords] = useState<BidRecord[]>([]);
  const [isAuctionActive, setIsAuctionActive] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [bidAmount, setBidAmount] = useState<number>(0);
  const [isBidModalVisible, setIsBidModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(0);
  const [form] = Form.useForm();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // 模拟拍卖商品数据
  const mockAuctionItem: LiveAuctionItem = {
    id: 1,
    productName: '荷兰郁金香 - 红色经典',
    productCode: 'TLP-001',
    images: [
      'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400',
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
    ],
    startingPrice: 100.00,
    currentPrice: 350.00,
    reservePrice: 500.00,
    bidIncrement: 10.00,
    bidCount: 15,
    timeRemaining: 300, // 5分钟
    status: 'ongoing',
    highestBidder: 'user123',
    description: '来自荷兰的优质郁金香，颜色鲜艳，品质上乘。适合园艺爱好者和花卉收藏家。',
  };

  // 模拟竞价记录
  const mockBidRecords: BidRecord[] = [
    {
      id: 1,
      userId: 1,
      username: 'user123',
      bidAmount: 350.00,
      bidTime: new Date().toISOString(),
      isWinning: true,
    },
    {
      id: 2,
      userId: 2,
      username: 'flower_lover',
      bidAmount: 340.00,
      bidTime: new Date(Date.now() - 30000).toISOString(),
      isWinning: false,
    },
    {
      id: 3,
      userId: 3,
      username: 'garden_master',
      bidAmount: 330.00,
      bidTime: new Date(Date.now() - 60000).toISOString(),
      isWinning: false,
    },
  ];

  // 获取当前进行中的拍卖
  const fetchCurrentAuction = async () => {
    try {
      // 获取拍卖会列表
      const auctionResponse = await auctionService.getAuctionList({
        page: 1,
        pageSize: 10,
      });

      if (auctionResponse.success && auctionResponse.data.list.length > 0) {
        const auction = auctionResponse.data.list[0];
        console.log('获取到拍卖会:', auction);

        // 设置当前拍卖会ID
        setCurrentAuctionId(auction.id);

        // 使用模拟的拍卖商品数据，但显示真实拍卖会信息
        const auctionName = (auction as any).name || auction.title;
        const liveItem: LiveAuctionItem = {
          ...mockAuctionItem,
          id: auction.id,
          productName: `${auctionName} - 拍卖商品`,
          description: auction.description || `拍卖会：${auctionName}`,
        };

        setCurrentItem(liveItem);
        setTimeRemaining(liveItem.timeRemaining);
        setBidAmount(liveItem.currentPrice + liveItem.bidIncrement);

        // 获取竞价记录（如果有的话）
        fetchBidRecords(auction.id);
      } else {
        // 如果没有拍卖会，使用模拟数据
        console.log('没有拍卖会数据，使用模拟数据');
        setCurrentItem(mockAuctionItem);
        setBidRecords(mockBidRecords);
        setTimeRemaining(mockAuctionItem.timeRemaining);
        setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);
      }

      setOnlineUsers(Math.floor(Math.random() * 50) + 10);
    } catch (error) {
      console.error('获取拍卖数据失败:', error);
      // 出错时使用模拟数据
      setCurrentItem(mockAuctionItem);
      setBidRecords(mockBidRecords);
      setTimeRemaining(mockAuctionItem.timeRemaining);
      setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);
      setOnlineUsers(Math.floor(Math.random() * 50) + 10);
    }
  };

  // 获取竞价记录
  const fetchBidRecords = async (auctionId: number) => {
    try {
      console.log('获取拍卖会竞价记录:', auctionId);

      // 尝试获取真实的竞价记录
      const response = await auctionService.getBidRecords(auctionId, undefined, {
        page: 1,
        pageSize: 50
      });

      if (response.success && response.data && response.data.list) {
        // 转换后端数据格式为前端格式
        const bidRecords: BidRecord[] = response.data.list.map((record: any) => ({
          id: record.id,
          userId: record.userId || record.bidderId,
          username: record.bidderName || record.username || `用户${record.userId}`,
          bidAmount: record.bidAmount || record.amount,
          bidTime: record.bidTime || record.createdAt,
          isWinning: record.isWinning || false,
        }));

        setBidRecords(bidRecords);
        console.log('获取到真实竞价记录:', bidRecords);
      } else {
        // 如果没有真实数据，使用模拟数据
        console.log('没有竞价记录，使用模拟数据');
        setBidRecords(mockBidRecords);
      }
    } catch (error) {
      console.error('获取竞价记录失败:', error);
      // 出错时使用模拟数据
      setBidRecords(mockBidRecords);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchCurrentAuction();
  }, []);

  // 倒计时器
  useEffect(() => {
    if (isAuctionActive && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsAuctionActive(false);
            message.info('拍卖时间结束');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isAuctionActive, timeRemaining]);

  // WebSocket连接（模拟）
  useEffect(() => {
    // 这里应该建立WebSocket连接来接收实时竞价信息
    // wsRef.current = new WebSocket('ws://localhost:8081/ws/auction');

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 开始拍卖
  const handleStartAuction = async () => {
    if (!currentAuctionId) {
      message.error('没有选中的拍卖会');
      return;
    }

    try {
      setLoading(true);
      const response = await auctionService.startAuction(currentAuctionId);
      if (response.success) {
        setIsAuctionActive(true);
        message.success('拍卖已开始');
      } else {
        throw new Error(response.message || '开始拍卖失败');
      }
    } catch (error: any) {
      console.error('开始拍卖失败:', error);
      message.error(error.message || '开始拍卖失败');
    } finally {
      setLoading(false);
    }
  };

  // 暂停拍卖
  const handlePauseAuction = async () => {
    if (!currentAuctionId) {
      message.error('没有选中的拍卖会');
      return;
    }

    try {
      setLoading(true);
      const response = await auctionService.pauseAuction(currentAuctionId);
      if (response.success) {
        setIsAuctionActive(false);
        message.info('拍卖已暂停');
      } else {
        throw new Error(response.message || '暂停拍卖失败');
      }
    } catch (error: any) {
      console.error('暂停拍卖失败:', error);
      message.error(error.message || '暂停拍卖失败');
    } finally {
      setLoading(false);
    }
  };

  // 结束拍卖
  const handleEndAuction = async () => {
    if (!currentAuctionId) {
      message.error('没有选中的拍卖会');
      return;
    }

    try {
      setLoading(true);
      const response = await auctionService.endAuction(currentAuctionId);
      if (response.success) {
        setIsAuctionActive(false);
        setTimeRemaining(0);
        message.success('拍卖已结束');
      } else {
        throw new Error(response.message || '结束拍卖失败');
      }
    } catch (error: any) {
      console.error('结束拍卖失败:', error);
      message.error(error.message || '结束拍卖失败');
    } finally {
      setLoading(false);
    }
  };

  // 出价
  const handleBid = () => {
    setIsBidModalVisible(true);
    form.setFieldsValue({ bidAmount });
  };

  // 确认出价
  const handleConfirmBid = async (values: any) => {
    setLoading(true);
    try {
      if (!currentItem) {
        throw new Error('当前没有选中的拍卖商品');
      }

      // 调用后端API提交出价
      const response = await auctionService.placeBid({
        itemId: currentItem.id,
        userId: 1, // 这里应该从用户上下文获取真实的用户ID
        price: values.bidAmount,
      });

      if (response.success) {
        const newBid: BidRecord = {
          id: Date.now(),
          userId: 1,
          username: 'admin', // 这里应该从用户上下文获取真实的用户名
          bidAmount: values.bidAmount,
          bidTime: new Date().toISOString(),
          isWinning: true,
        };

        // 更新竞价记录
        setBidRecords(prev => {
          const updated = prev.map(record => ({ ...record, isWinning: false }));
          return [newBid, ...updated];
        });

        // 更新当前价格
        setCurrentItem(prev => prev ? {
          ...prev,
          currentPrice: values.bidAmount,
          bidCount: prev.bidCount + 1,
          highestBidder: 'admin',
        } : null);

        // 设置下一次出价金额
        setBidAmount(values.bidAmount + (currentItem?.bidIncrement || 10));

        message.success('出价成功');
        setIsBidModalVisible(false);
      } else {
        throw new Error(response.message || '出价失败');
      }
    } catch (error: any) {
      console.error('出价失败:', error);
      message.error(error.message || '出价失败');
    } finally {
      setLoading(false);
    }
  };

  if (!currentItem) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Title level={3}>暂无进行中的拍卖</Title>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>实时竞价</Title>

      <Row gutter={24}>
        {/* 左侧：商品信息和控制面板 */}
        <Col xs={24} lg={16}>
          {/* 商品信息卡片 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col xs={24} md={8}>
                <img
                  src={currentItem.images[0]}
                  alt={currentItem.productName}
                  style={{
                    width: '100%',
                    height: 200,
                    objectFit: 'cover',
                    borderRadius: 8,
                  }}
                />
              </Col>
              <Col xs={24} md={16}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Title level={3} style={{ margin: 0 }}>
                    {currentItem.productName}
                  </Title>
                  <Text type="secondary">商品编号: {currentItem.productCode}</Text>
                  <Text>{currentItem.description}</Text>

                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="起拍价"
                        value={currentItem.startingPrice}
                        precision={2}
                        prefix="¥"
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="当前价格"
                        value={currentItem.currentPrice}
                        precision={2}
                        prefix="¥"
                        valueStyle={{ color: '#f50', fontSize: 24, fontWeight: 'bold' }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="出价次数"
                        value={currentItem.bidCount}
                        suffix="次"
                      />
                    </Col>
                  </Row>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 拍卖控制面板 */}
          <Card title="拍卖控制" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={8}>
                <Space>
                  <Badge
                    status={isAuctionActive ? 'processing' : 'default'}
                    text={
                      <Text strong>
                        状态: {isAuctionActive ? '进行中' : '已暂停'}
                      </Text>
                    }
                  />
                </Space>
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="剩余时间"
                  value={formatTime(timeRemaining)}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{
                    color: timeRemaining < 60 ? '#f50' : '#1890ff',
                    fontSize: 20,
                    fontWeight: 'bold',
                  }}
                />
                <Progress
                  percent={Math.max(0, (timeRemaining / 300) * 100)}
                  showInfo={false}
                  strokeColor={timeRemaining < 60 ? '#f50' : '#1890ff'}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Space>
                  {!isAuctionActive ? (
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={handleStartAuction}
                      disabled={timeRemaining === 0}
                      loading={loading}
                    >
                      开始拍卖
                    </Button>
                  ) : (
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={handlePauseAuction}
                      loading={loading}
                    >
                      暂停拍卖
                    </Button>
                  )}
                  <Button
                    danger
                    icon={<StopOutlined />}
                    onClick={handleEndAuction}
                    loading={loading}
                  >
                    结束拍卖
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 出价面板 */}
          <Card title="快速出价">
            <Row gutter={16} align="middle">
              <Col xs={24} sm={12}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text>建议出价: ¥{bidAmount.toFixed(2)}</Text>
                  <Text type="secondary">
                    加价幅度: ¥{currentItem.bidIncrement.toFixed(2)}
                  </Text>
                  {currentItem.reservePrice && (
                    <Text type="warning">
                      保留价: ¥{currentItem.reservePrice.toFixed(2)}
                    </Text>
                  )}
                </Space>
              </Col>
              <Col xs={24} sm={12}>
                <Space>
                  <Button
                    type="primary"
                    size="large"
                    icon={<AuditOutlined />}
                    onClick={handleBid}
                    disabled={!isAuctionActive || timeRemaining === 0}
                  >
                    出价 ¥{bidAmount.toFixed(2)}
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 右侧：竞价记录和在线用户 */}
        <Col xs={24} lg={8}>
          {/* 在线统计 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="在线用户"
                  value={onlineUsers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="最高出价人"
                  value={currentItem.highestBidder || '暂无'}
                  prefix={<TrophyOutlined />}
                />
              </Col>
            </Row>
          </Card>

          {/* 竞价记录 */}
          <Card title="竞价记录" size="small">
            <List
              dataSource={bidRecords}
              renderItem={(record) => (
                <List.Item
                  style={{
                    padding: '8px 0',
                    backgroundColor: record.isWinning ? '#f6ffed' : 'transparent',
                    borderRadius: record.isWinning ? 4 : 0,
                    paddingLeft: record.isWinning ? 8 : 0,
                  }}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        src={record.avatar}
                        icon={<UserOutlined />}
                        style={{
                          backgroundColor: record.isWinning ? '#52c41a' : '#1890ff',
                        }}
                      />
                    }
                    title={
                      <Space>
                        <Text strong={record.isWinning}>
                          {record.username}
                        </Text>
                        {record.isWinning && (
                          <Tag color="green">
                            最高价
                          </Tag>
                        )}
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size={0}>
                        <Text strong style={{ color: '#f50' }}>
                          ¥{record.bidAmount.toFixed(2)}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {new Date(record.bidTime).toLocaleTimeString()}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
              style={{ maxHeight: 400, overflowY: 'auto' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 出价确认模态框 */}
      <Modal
        title="确认出价"
        open={isBidModalVisible}
        onCancel={() => setIsBidModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfirmBid}
          autoComplete="off"
        >
          <Alert
            message="出价提醒"
            description={`当前最高价: ¥${currentItem.currentPrice.toFixed(2)}，最小加价幅度: ¥${currentItem.bidIncrement.toFixed(2)}`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            name="bidAmount"
            label="出价金额"
            rules={[
              { required: true, message: '请输入出价金额' },
              {
                type: 'number',
                min: currentItem.currentPrice + currentItem.bidIncrement,
                message: `出价必须高于当前价格 ¥${(currentItem.currentPrice + currentItem.bidIncrement).toFixed(2)}`,
              },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              precision={2}
              min={currentItem.currentPrice + currentItem.bidIncrement}
              step={currentItem.bidIncrement}
              addonBefore="¥"
              placeholder="请输入出价金额"
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setIsBidModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                确认出价
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LiveBidding;
