import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Tag,
  Space,
  Button,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Modal,
  message,
  Tooltip,
  Badge,
  Statistic,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  BugOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { confirm } = Modal;

// 日志级别枚举
enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

// 日志类型枚举
enum LogType {
  SYSTEM = 'system',
  SECURITY = 'security',
  OPERATION = 'operation',
  API = 'api',
  DATABASE = 'database',
}

// 日志记录接口
interface LogRecord {
  id: number;
  timestamp: string;
  level: LogLevel;
  type: LogType;
  module: string;
  message: string;
  details?: string;
  userId?: number;
  username?: string;
  ip?: string;
  userAgent?: string;
  requestId?: string;
  duration?: number;
}

// 日志统计接口
interface LogStatistics {
  totalLogs: number;
  errorLogs: number;
  warnLogs: number;
  infoLogs: number;
  debugLogs: number;
  todayLogs: number;
  recentErrors: number;
}

const SystemLogs: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<LogRecord[]>([]);
  const [statistics, setStatistics] = useState<LogStatistics>({
    totalLogs: 0,
    errorLogs: 0,
    warnLogs: 0,
    infoLogs: 0,
    debugLogs: 0,
    todayLogs: 0,
    recentErrors: 0,
  });

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    level: '',
    type: '',
    dateRange: null as any,
    module: '',
  });

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 获取日志数据
  const fetchLogs = async () => {
    setLoading(true);
    try {
      // 这里应该调用后端API获取日志
      // const response = await logService.getLogs({
      //   ...searchParams,
      //   page: pagination.current,
      //   pageSize: pagination.pageSize,
      // });
      
      // 模拟日志数据
      const mockLogs: LogRecord[] = [
        {
          id: 1,
          timestamp: '2023-12-15 10:30:15',
          level: LogLevel.ERROR,
          type: LogType.SYSTEM,
          module: 'auction-service',
          message: '拍卖服务连接数据库失败',
          details: 'Connection timeout after 30 seconds',
          userId: 1,
          username: 'admin',
          ip: '*************',
          requestId: 'req-123456',
        },
        {
          id: 2,
          timestamp: '2023-12-15 10:25:30',
          level: LogLevel.WARN,
          type: LogType.SECURITY,
          module: 'auth-service',
          message: '用户登录失败次数过多',
          details: 'User attempted to login 5 times with wrong password',
          userId: 2,
          username: 'user001',
          ip: '*************',
          requestId: 'req-123455',
        },
        {
          id: 3,
          timestamp: '2023-12-15 10:20:45',
          level: LogLevel.INFO,
          type: LogType.OPERATION,
          module: 'user-service',
          message: '用户创建成功',
          details: 'New user registered with email: <EMAIL>',
          userId: 1,
          username: 'admin',
          ip: '*************',
          requestId: 'req-123454',
        },
        {
          id: 4,
          timestamp: '2023-12-15 10:15:20',
          level: LogLevel.DEBUG,
          type: LogType.API,
          module: 'api-gateway',
          message: 'API请求处理完成',
          details: 'GET /api/v1/auctions processed successfully',
          duration: 150,
          requestId: 'req-123453',
        },
      ];

      setLogs(mockLogs);
      setPagination(prev => ({ ...prev, total: 100 }));

      // 模拟统计数据
      setStatistics({
        totalLogs: 1250,
        errorLogs: 45,
        warnLogs: 120,
        infoLogs: 980,
        debugLogs: 105,
        todayLogs: 156,
        recentErrors: 8,
      });

    } catch (error: any) {
      console.error('获取日志失败:', error);
      message.error('获取日志失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [pagination.current, pagination.pageSize, searchParams]);

  // 搜索日志
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchLogs();
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      keyword: '',
      level: '',
      type: '',
      dateRange: null,
      module: '',
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 导出日志
  const handleExport = () => {
    message.success('日志导出功能开发中...');
  };

  // 清理日志
  const handleClearLogs = () => {
    confirm({
      title: '确认清理日志',
      content: '确定要清理所有日志吗？此操作不可恢复。',
      icon: <ExclamationCircleOutlined />,
      onOk() {
        message.success('日志清理成功');
        fetchLogs();
      },
    });
  };

  // 查看日志详情
  const handleViewDetails = (record: LogRecord) => {
    Modal.info({
      title: '日志详情',
      width: 800,
      content: (
        <div>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Text strong>时间:</Text> {record.timestamp}
            </Col>
            <Col span={12}>
              <Text strong>级别:</Text> {getLevelTag(record.level)}
            </Col>
          </Row>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Text strong>类型:</Text> {getTypeTag(record.type)}
            </Col>
            <Col span={12}>
              <Text strong>模块:</Text> {record.module}
            </Col>
          </Row>
          {record.userId && (
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Text strong>用户:</Text> {record.username} (ID: {record.userId})
              </Col>
              <Col span={12}>
                <Text strong>IP地址:</Text> {record.ip}
              </Col>
            </Row>
          )}
          {record.requestId && (
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Text strong>请求ID:</Text> {record.requestId}
              </Col>
              {record.duration && (
                <Col span={12}>
                  <Text strong>耗时:</Text> {record.duration}ms
                </Col>
              )}
            </Row>
          )}
          <div style={{ marginBottom: 16 }}>
            <Text strong>消息:</Text>
            <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
              {record.message}
            </div>
          </div>
          {record.details && (
            <div>
              <Text strong>详细信息:</Text>
              <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>{record.details}</pre>
              </div>
            </div>
          )}
        </div>
      ),
    });
  };

  // 获取级别标签
  const getLevelTag = (level: LogLevel) => {
    const levelMap = {
      [LogLevel.DEBUG]: { color: 'default', text: 'DEBUG' },
      [LogLevel.INFO]: { color: 'blue', text: 'INFO' },
      [LogLevel.WARN]: { color: 'orange', text: 'WARN' },
      [LogLevel.ERROR]: { color: 'red', text: 'ERROR' },
      [LogLevel.FATAL]: { color: 'red', text: 'FATAL' },
    };
    const config = levelMap[level];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取类型标签
  const getTypeTag = (type: LogType) => {
    const typeMap = {
      [LogType.SYSTEM]: { color: 'purple', text: '系统' },
      [LogType.SECURITY]: { color: 'red', text: '安全' },
      [LogType.OPERATION]: { color: 'green', text: '操作' },
      [LogType.API]: { color: 'blue', text: 'API' },
      [LogType.DATABASE]: { color: 'orange', text: '数据库' },
    };
    const config = typeMap[type];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      sorter: true,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: LogLevel) => getLevelTag(level),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: LogType) => getTypeTag(type),
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 120,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: {
        showTitle: false,
      },
      render: (message: string) => (
        <Tooltip placement="topLeft" title={message}>
          {message}
        </Tooltip>
      ),
    },
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
      width: 100,
      render: (username: string, record: LogRecord) => 
        username ? `${username} (${record.userId})` : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: LogRecord) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <FileTextOutlined /> 系统日志
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总日志数"
              value={statistics.totalLogs}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="错误日志"
              value={statistics.errorLogs}
              valueStyle={{ color: '#cf1322' }}
              prefix={<BugOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="警告日志"
              value={statistics.warnLogs}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="信息日志"
              value={statistics.infoLogs}
              valueStyle={{ color: '#1890ff' }}
              prefix={<InfoCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="今日日志"
              value={statistics.todayLogs}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="近期错误"
              value={statistics.recentErrors}
              valueStyle={{ color: '#cf1322' }}
              prefix={<ExclamationCircleOutlined />}
              suffix={
                <Badge
                  count={statistics.recentErrors > 0 ? statistics.recentErrors : 0}
                  style={{ backgroundColor: '#f50' }}
                />
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Input
              placeholder="搜索关键词"
              value={searchParams.keyword}
              onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="日志级别"
              value={searchParams.level}
              onChange={(value) => setSearchParams(prev => ({ ...prev, level: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value={LogLevel.DEBUG}>DEBUG</Option>
              <Option value={LogLevel.INFO}>INFO</Option>
              <Option value={LogLevel.WARN}>WARN</Option>
              <Option value={LogLevel.ERROR}>ERROR</Option>
              <Option value={LogLevel.FATAL}>FATAL</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="日志类型"
              value={searchParams.type}
              onChange={(value) => setSearchParams(prev => ({ ...prev, type: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value={LogType.SYSTEM}>系统</Option>
              <Option value={LogType.SECURITY}>安全</Option>
              <Option value={LogType.OPERATION}>操作</Option>
              <Option value={LogType.API}>API</Option>
              <Option value={LogType.DATABASE}>数据库</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              value={searchParams.dateRange}
              onChange={(dates) => setSearchParams(prev => ({ ...prev, dateRange: dates }))}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 日志表格 */}
      <Card
        title="日志列表"
        extra={
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchLogs}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleClearLogs}
            >
              清理日志
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 20 }));
            },
          }}
          size="small"
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default SystemLogs;
