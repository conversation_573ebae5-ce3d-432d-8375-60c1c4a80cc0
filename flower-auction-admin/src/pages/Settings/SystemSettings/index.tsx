import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  message,
  Tabs,
  Row,
  Col,
  Select,
  Upload,
  Image,
  Divider,
  Space,
  Alert,
  TimePicker,
  Tooltip,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  BellOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
// import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// 系统配置接口
interface SystemConfig {
  // 基础配置
  siteName: string;
  siteDescription: string;
  siteLogo: string;
  siteIcon: string;
  contactEmail: string;
  contactPhone: string;

  // 拍卖配置
  defaultAuctionDuration: number; // 默认拍卖时长（分钟）
  bidIncrementPercentage: number; // 默认加价幅度百分比
  maxBidIncrement: number; // 最大加价金额
  minBidIncrement: number; // 最小加价金额
  auctionExtensionTime: number; // 拍卖延时时间（秒）

  // 用户配置
  enableUserRegistration: boolean; // 允许用户注册
  requireEmailVerification: boolean; // 需要邮箱验证
  defaultUserRole: string; // 默认用户角色
  maxLoginAttempts: number; // 最大登录尝试次数

  // 支付配置
  enableAlipay: boolean; // 启用支付宝
  enableWechatPay: boolean; // 启用微信支付
  enableBankTransfer: boolean; // 启用银行转账
  paymentTimeout: number; // 支付超时时间（分钟）

  // 系统维护
  maintenanceMode: boolean; // 维护模式
  maintenanceMessage: string; // 维护提示信息
  maintenanceStartTime?: string; // 维护开始时间
  maintenanceEndTime?: string; // 维护结束时间

  // 文件上传
  maxFileSize: number; // 最大文件大小（MB）
  allowedFileTypes: string[]; // 允许的文件类型
  maxImagesPerProduct: number; // 每个商品最大图片数
}

const SystemSettings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    // 默认配置
    siteName: '昆明花卉拍卖系统',
    siteDescription: '专业的花卉拍卖交易平台',
    siteLogo: '',
    siteIcon: '',
    contactEmail: '<EMAIL>',
    contactPhone: '************',

    defaultAuctionDuration: 60,
    bidIncrementPercentage: 5,
    maxBidIncrement: 10000,
    minBidIncrement: 10,
    auctionExtensionTime: 30,

    enableUserRegistration: true,
    requireEmailVerification: false,
    defaultUserRole: 'buyer',
    maxLoginAttempts: 5,

    enableAlipay: true,
    enableWechatPay: true,
    enableBankTransfer: true,
    paymentTimeout: 30,

    maintenanceMode: false,
    maintenanceMessage: '系统正在维护中，请稍后再试',

    maxFileSize: 10,
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif'],
    maxImagesPerProduct: 10,
  });

  const [basicForm] = Form.useForm();
  const [auctionForm] = Form.useForm();
  const [userForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [maintenanceForm] = Form.useForm();
  const [uploadForm] = Form.useForm();

  // 获取系统配置
  const fetchConfig = async () => {
    setLoading(true);
    try {
      // 这里应该调用后端API获取配置
      // const response = await systemService.getConfig();
      // if (response.success) {
      //   setConfig(response.data);
      //   updateForms(response.data);
      // }

      // 暂时使用模拟数据
      updateForms(config);
      message.success('配置加载成功');
    } catch (error: any) {
      console.error('获取系统配置失败:', error);
      message.error('获取系统配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新表单数据
  const updateForms = (configData: SystemConfig) => {
    basicForm.setFieldsValue({
      siteName: configData.siteName,
      siteDescription: configData.siteDescription,
      contactEmail: configData.contactEmail,
      contactPhone: configData.contactPhone,
    });

    auctionForm.setFieldsValue({
      defaultAuctionDuration: configData.defaultAuctionDuration,
      bidIncrementPercentage: configData.bidIncrementPercentage,
      maxBidIncrement: configData.maxBidIncrement,
      minBidIncrement: configData.minBidIncrement,
      auctionExtensionTime: configData.auctionExtensionTime,
    });

    userForm.setFieldsValue({
      enableUserRegistration: configData.enableUserRegistration,
      requireEmailVerification: configData.requireEmailVerification,
      defaultUserRole: configData.defaultUserRole,
      maxLoginAttempts: configData.maxLoginAttempts,
    });

    paymentForm.setFieldsValue({
      enableAlipay: configData.enableAlipay,
      enableWechatPay: configData.enableWechatPay,
      enableBankTransfer: configData.enableBankTransfer,
      paymentTimeout: configData.paymentTimeout,
    });

    maintenanceForm.setFieldsValue({
      maintenanceMode: configData.maintenanceMode,
      maintenanceMessage: configData.maintenanceMessage,
      maintenanceStartTime: configData.maintenanceStartTime ? configData.maintenanceStartTime : null,
      maintenanceEndTime: configData.maintenanceEndTime ? configData.maintenanceEndTime : null,
    });

    uploadForm.setFieldsValue({
      maxFileSize: configData.maxFileSize,
      allowedFileTypes: configData.allowedFileTypes,
      maxImagesPerProduct: configData.maxImagesPerProduct,
    });
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  // 保存配置
  const handleSave = async (formData: any, configType: string) => {
    setSaving(true);
    try {
      const updatedConfig = { ...config, ...formData };

      // 这里应该调用后端API保存配置
      // const response = await systemService.updateConfig(updatedConfig);
      // if (response.success) {
      //   setConfig(updatedConfig);
      //   message.success('配置保存成功');
      // }

      // 暂时使用模拟保存
      setConfig(updatedConfig);
      message.success(`${configType}配置保存成功`);
    } catch (error: any) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <SettingOutlined /> 系统配置
      </Title>

      <Tabs defaultActiveKey="basic" type="card">
        {/* 基础配置 */}
        <TabPane tab={<span><InfoCircleOutlined />基础配置</span>} key="basic">
          <Card>
            <Form
              form={basicForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '基础')}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="siteName"
                    label="网站名称"
                    rules={[{ required: true, message: '请输入网站名称' }]}
                  >
                    <Input placeholder="请输入网站名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="contactEmail"
                    label="联系邮箱"
                    rules={[
                      { required: true, message: '请输入联系邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="请输入联系邮箱" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="contactPhone"
                    label="联系电话"
                    rules={[{ required: true, message: '请输入联系电话' }]}
                  >
                    <Input placeholder="请输入联系电话" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="siteDescription"
                label="网站描述"
              >
                <TextArea
                  rows={3}
                  placeholder="请输入网站描述"
                  maxLength={200}
                  showCount
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 拍卖配置 */}
        <TabPane tab={<span><DatabaseOutlined />拍卖配置</span>} key="auction">
          <Card>
            <Form
              form={auctionForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '拍卖')}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="defaultAuctionDuration"
                    label={
                      <span>
                        默认拍卖时长（分钟）
                        <Tooltip title="新创建拍卖会的默认持续时间">
                          <InfoCircleOutlined style={{ marginLeft: 4 }} />
                        </Tooltip>
                      </span>
                    }
                    rules={[{ required: true, message: '请输入默认拍卖时长' }]}
                  >
                    <InputNumber
                      min={1}
                      max={1440}
                      style={{ width: '100%' }}
                      placeholder="请输入默认拍卖时长"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="bidIncrementPercentage"
                    label={
                      <span>
                        默认加价幅度（%）
                        <Tooltip title="相对于当前价格的加价百分比">
                          <InfoCircleOutlined style={{ marginLeft: 4 }} />
                        </Tooltip>
                      </span>
                    }
                    rules={[{ required: true, message: '请输入默认加价幅度' }]}
                  >
                    <InputNumber
                      min={1}
                      max={100}
                      style={{ width: '100%' }}
                      placeholder="请输入默认加价幅度"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="minBidIncrement"
                    label="最小加价金额（元）"
                    rules={[{ required: true, message: '请输入最小加价金额' }]}
                  >
                    <InputNumber
                      min={1}
                      style={{ width: '100%' }}
                      placeholder="请输入最小加价金额"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maxBidIncrement"
                    label="最大加价金额（元）"
                    rules={[{ required: true, message: '请输入最大加价金额' }]}
                  >
                    <InputNumber
                      min={1}
                      style={{ width: '100%' }}
                      placeholder="请输入最大加价金额"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="auctionExtensionTime"
                label={
                  <span>
                    拍卖延时时间（秒）
                    <Tooltip title="在拍卖即将结束时，如果有新的出价，自动延长的时间">
                      <InfoCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: '请输入拍卖延时时间' }]}
              >
                <InputNumber
                  min={0}
                  max={300}
                  style={{ width: '100%' }}
                  placeholder="请输入拍卖延时时间"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 用户配置 */}
        <TabPane tab={<span><SecurityScanOutlined />用户配置</span>} key="user">
          <Card>
            <Form
              form={userForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '用户')}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="enableUserRegistration"
                    label="允许用户注册"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="requireEmailVerification"
                    label="需要邮箱验证"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="defaultUserRole"
                    label="默认用户角色"
                    rules={[{ required: true, message: '请选择默认用户角色' }]}
                  >
                    <Select placeholder="请选择默认用户角色">
                      <Option value="buyer">买家</Option>
                      <Option value="seller">卖家</Option>
                      <Option value="both">买家+卖家</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maxLoginAttempts"
                    label="最大登录尝试次数"
                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
                  >
                    <InputNumber
                      min={1}
                      max={10}
                      style={{ width: '100%' }}
                      placeholder="请输入最大登录尝试次数"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 支付配置 */}
        <TabPane tab={<span><BellOutlined />支付配置</span>} key="payment">
          <Card>
            <Form
              form={paymentForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '支付')}
            >
              <Alert
                message="支付配置"
                description="配置系统支持的支付方式和相关参数"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="enableAlipay"
                    label="启用支付宝"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="enableWechatPay"
                    label="启用微信支付"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="enableBankTransfer"
                    label="启用银行转账"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="paymentTimeout"
                label={
                  <span>
                    支付超时时间（分钟）
                    <Tooltip title="用户完成支付的最大时间限制">
                      <InfoCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: '请输入支付超时时间' }]}
              >
                <InputNumber
                  min={5}
                  max={1440}
                  style={{ width: '100%' }}
                  placeholder="请输入支付超时时间"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 系统维护 */}
        <TabPane tab={<span><SettingOutlined />系统维护</span>} key="maintenance">
          <Card>
            <Form
              form={maintenanceForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '维护')}
            >
              <Alert
                message="维护模式"
                description="启用维护模式后，普通用户将无法访问系统，只有管理员可以正常使用"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Form.Item
                name="maintenanceMode"
                label="启用维护模式"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="maintenanceMessage"
                label="维护提示信息"
                rules={[{ required: true, message: '请输入维护提示信息' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="请输入维护提示信息"
                  maxLength={500}
                  showCount
                />
              </Form.Item>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="maintenanceStartTime"
                    label="维护开始时间"
                  >
                    <TimePicker
                      style={{ width: '100%' }}
                      format="HH:mm"
                      placeholder="请选择维护开始时间"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maintenanceEndTime"
                    label="维护结束时间"
                  >
                    <TimePicker
                      style={{ width: '100%' }}
                      format="HH:mm"
                      placeholder="请选择维护结束时间"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 文件上传配置 */}
        <TabPane tab={<span><UploadOutlined />文件上传</span>} key="upload">
          <Card>
            <Form
              form={uploadForm}
              layout="vertical"
              onFinish={(values) => handleSave(values, '文件上传')}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="maxFileSize"
                    label="最大文件大小（MB）"
                    rules={[{ required: true, message: '请输入最大文件大小' }]}
                  >
                    <InputNumber
                      min={1}
                      max={100}
                      style={{ width: '100%' }}
                      placeholder="请输入最大文件大小"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maxImagesPerProduct"
                    label="每个商品最大图片数"
                    rules={[{ required: true, message: '请输入每个商品最大图片数' }]}
                  >
                    <InputNumber
                      min={1}
                      max={20}
                      style={{ width: '100%' }}
                      placeholder="请输入每个商品最大图片数"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="allowedFileTypes"
                label="允许的文件类型"
                rules={[{ required: true, message: '请选择允许的文件类型' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择允许的文件类型"
                  style={{ width: '100%' }}
                >
                  <Option value="jpg">JPG</Option>
                  <Option value="jpeg">JPEG</Option>
                  <Option value="png">PNG</Option>
                  <Option value="gif">GIF</Option>
                  <Option value="webp">WebP</Option>
                  <Option value="pdf">PDF</Option>
                  <Option value="doc">DOC</Option>
                  <Option value="docx">DOCX</Option>
                  <Option value="xls">XLS</Option>
                  <Option value="xlsx">XLSX</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => fetchConfig()}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SystemSettings;
