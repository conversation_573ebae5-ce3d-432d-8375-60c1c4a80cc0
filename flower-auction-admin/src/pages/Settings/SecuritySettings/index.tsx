import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  message,
  Row,
  Col,
  Select,
  Alert,
  Table,
  Tag,
  Space,
  Modal,
  Tooltip,
  Progress,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  SecurityScanOutlined,
  LockOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ShieldOutlined,
  KeyOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 安全配置接口
interface SecurityConfig {
  // 密码策略
  minPasswordLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  passwordExpireDays: number;
  
  // 登录安全
  enableTwoFactor: boolean;
  maxLoginAttempts: number;
  lockoutDuration: number; // 锁定时长（分钟）
  enableCaptcha: boolean;
  
  // 会话管理
  sessionTimeout: number; // 会话超时（分钟）
  maxConcurrentSessions: number;
  enableSessionMonitoring: boolean;
  
  // IP白名单
  enableIpWhitelist: boolean;
  allowedIps: string[];
  
  // 安全日志
  enableSecurityLog: boolean;
  logRetentionDays: number;
}

// 登录记录接口
interface LoginRecord {
  id: number;
  userId: number;
  username: string;
  ip: string;
  userAgent: string;
  loginTime: string;
  status: 'success' | 'failed' | 'blocked';
  location?: string;
}

const SecuritySettings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<SecurityConfig>({
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    passwordExpireDays: 90,
    
    enableTwoFactor: false,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    enableCaptcha: true,
    
    sessionTimeout: 120,
    maxConcurrentSessions: 3,
    enableSessionMonitoring: true,
    
    enableIpWhitelist: false,
    allowedIps: [],
    
    enableSecurityLog: true,
    logRetentionDays: 30,
  });

  const [loginRecords, setLoginRecords] = useState<LoginRecord[]>([]);
  const [ipModalVisible, setIpModalVisible] = useState(false);
  const [newIp, setNewIp] = useState('');

  const [form] = Form.useForm();

  // 获取安全配置
  const fetchConfig = async () => {
    setLoading(true);
    try {
      // 这里应该调用后端API获取配置
      // const response = await securityService.getConfig();
      // if (response.success) {
      //   setConfig(response.data);
      //   form.setFieldsValue(response.data);
      // }
      
      // 暂时使用模拟数据
      form.setFieldsValue(config);
      message.success('安全配置加载成功');
    } catch (error: any) {
      console.error('获取安全配置失败:', error);
      message.error('获取安全配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取登录记录
  const fetchLoginRecords = async () => {
    try {
      // 模拟登录记录数据
      const mockRecords: LoginRecord[] = [
        {
          id: 1,
          userId: 1,
          username: 'admin',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          loginTime: '2023-12-15 09:30:00',
          status: 'success',
          location: '北京市',
        },
        {
          id: 2,
          userId: 2,
          username: 'user001',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          loginTime: '2023-12-15 08:45:00',
          status: 'failed',
          location: '上海市',
        },
      ];
      setLoginRecords(mockRecords);
    } catch (error: any) {
      console.error('获取登录记录失败:', error);
    }
  };

  useEffect(() => {
    fetchConfig();
    fetchLoginRecords();
  }, []);

  // 保存配置
  const handleSave = async (values: any) => {
    setSaving(true);
    try {
      const updatedConfig = { ...config, ...values };
      
      // 这里应该调用后端API保存配置
      // const response = await securityService.updateConfig(updatedConfig);
      // if (response.success) {
      //   setConfig(updatedConfig);
      //   message.success('安全配置保存成功');
      // }
      
      // 暂时使用模拟保存
      setConfig(updatedConfig);
      message.success('安全配置保存成功');
    } catch (error: any) {
      console.error('保存安全配置失败:', error);
      message.error('保存安全配置失败');
    } finally {
      setSaving(false);
    }
  };

  // 添加IP白名单
  const handleAddIp = () => {
    if (!newIp) {
      message.error('请输入IP地址');
      return;
    }
    
    // 简单的IP格式验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(newIp)) {
      message.error('请输入有效的IP地址');
      return;
    }
    
    if (config.allowedIps.includes(newIp)) {
      message.error('该IP地址已存在');
      return;
    }
    
    const updatedConfig = {
      ...config,
      allowedIps: [...config.allowedIps, newIp],
    };
    setConfig(updatedConfig);
    setNewIp('');
    setIpModalVisible(false);
    message.success('IP地址添加成功');
  };

  // 删除IP白名单
  const handleRemoveIp = (ip: string) => {
    confirm({
      title: '确认删除',
      content: `确定要删除IP地址 ${ip} 吗？`,
      onOk() {
        const updatedConfig = {
          ...config,
          allowedIps: config.allowedIps.filter(item => item !== ip),
        };
        setConfig(updatedConfig);
        message.success('IP地址删除成功');
      },
    });
  };

  // 计算密码强度
  const calculatePasswordStrength = () => {
    let strength = 0;
    if (config.minPasswordLength >= 8) strength += 20;
    if (config.requireUppercase) strength += 20;
    if (config.requireLowercase) strength += 20;
    if (config.requireNumbers) strength += 20;
    if (config.requireSpecialChars) strength += 20;
    return strength;
  };

  // 登录记录表格列
  const loginColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '登录时间',
      dataIndex: 'loginTime',
      key: 'loginTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          success: { color: 'green', text: '成功' },
          failed: { color: 'red', text: '失败' },
          blocked: { color: 'orange', text: '被阻止' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: LoginRecord) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              Modal.info({
                title: '登录详情',
                content: (
                  <div>
                    <p><strong>用户名:</strong> {record.username}</p>
                    <p><strong>IP地址:</strong> {record.ip}</p>
                    <p><strong>用户代理:</strong> {record.userAgent}</p>
                    <p><strong>登录时间:</strong> {record.loginTime}</p>
                    <p><strong>位置:</strong> {record.location}</p>
                  </div>
                ),
              });
            }}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <SecurityScanOutlined /> 安全设置
      </Title>

      <Row gutter={24}>
        <Col span={16}>
          <Card title={<span><LockOutlined /> 安全配置</span>}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={config}
            >
              {/* 密码策略 */}
              <Alert
                message="密码策略"
                description="配置用户密码的安全要求"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="minPasswordLength"
                    label="最小密码长度"
                    rules={[{ required: true, message: '请输入最小密码长度' }]}
                  >
                    <InputNumber
                      min={6}
                      max={20}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="passwordExpireDays"
                    label="密码过期天数"
                    rules={[{ required: true, message: '请输入密码过期天数' }]}
                  >
                    <InputNumber
                      min={30}
                      max={365}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    name="requireUppercase"
                    label="需要大写字母"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="requireLowercase"
                    label="需要小写字母"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="requireNumbers"
                    label="需要数字"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="requireSpecialChars"
                    label="需要特殊字符"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              {/* 密码强度指示器 */}
              <div style={{ marginBottom: 24 }}>
                <Text strong>密码强度: </Text>
                <Progress
                  percent={calculatePasswordStrength()}
                  size="small"
                  status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}
                  style={{ width: 200, display: 'inline-block', marginLeft: 8 }}
                />
              </div>

              {/* 登录安全 */}
              <Alert
                message="登录安全"
                description="配置登录相关的安全策略"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="maxLoginAttempts"
                    label="最大登录尝试次数"
                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
                  >
                    <InputNumber
                      min={3}
                      max={10}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="lockoutDuration"
                    label="锁定时长（分钟）"
                    rules={[{ required: true, message: '请输入锁定时长' }]}
                  >
                    <InputNumber
                      min={5}
                      max={1440}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="sessionTimeout"
                    label="会话超时（分钟）"
                    rules={[{ required: true, message: '请输入会话超时时间' }]}
                  >
                    <InputNumber
                      min={30}
                      max={480}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="enableTwoFactor"
                    label="启用双因子认证"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="enableCaptcha"
                    label="启用验证码"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="enableSessionMonitoring"
                    label="启用会话监控"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={fetchConfig}
                    loading={loading}
                  >
                    重新加载
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col span={8}>
          {/* IP白名单管理 */}
          <Card 
            title={<span><ShieldOutlined /> IP白名单</span>}
            extra={
              <Button
                type="primary"
                size="small"
                onClick={() => setIpModalVisible(true)}
              >
                添加IP
              </Button>
            }
            style={{ marginBottom: 16 }}
          >
            <Form.Item
              label="启用IP白名单"
              style={{ marginBottom: 16 }}
            >
              <Switch
                checked={config.enableIpWhitelist}
                onChange={(checked) => setConfig({ ...config, enableIpWhitelist: checked })}
              />
            </Form.Item>
            
            {config.allowedIps.length > 0 ? (
              <div>
                {config.allowedIps.map((ip, index) => (
                  <div key={index} style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text code>{ip}</Text>
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveIp(ip)}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <Text type="secondary">暂无IP白名单</Text>
            )}
          </Card>

          {/* 安全状态 */}
          <Card title={<span><KeyOutlined /> 安全状态</span>}>
            <div style={{ marginBottom: 16 }}>
              <Text strong>密码强度: </Text>
              <Progress
                percent={calculatePasswordStrength()}
                size="small"
                status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}
              />
            </div>
            
            <div style={{ marginBottom: 8 }}>
              <Text>双因子认证: </Text>
              <Tag color={config.enableTwoFactor ? 'green' : 'red'}>
                {config.enableTwoFactor ? '已启用' : '未启用'}
              </Tag>
            </div>
            
            <div style={{ marginBottom: 8 }}>
              <Text>IP白名单: </Text>
              <Tag color={config.enableIpWhitelist ? 'green' : 'red'}>
                {config.enableIpWhitelist ? '已启用' : '未启用'}
              </Tag>
            </div>
            
            <div>
              <Text>会话监控: </Text>
              <Tag color={config.enableSessionMonitoring ? 'green' : 'red'}>
                {config.enableSessionMonitoring ? '已启用' : '未启用'}
              </Tag>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 登录记录 */}
      <Card 
        title="最近登录记录" 
        style={{ marginTop: 24 }}
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchLoginRecords}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={loginColumns}
          dataSource={loginRecords}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Card>

      {/* 添加IP模态框 */}
      <Modal
        title="添加IP白名单"
        open={ipModalVisible}
        onOk={handleAddIp}
        onCancel={() => {
          setIpModalVisible(false);
          setNewIp('');
        }}
      >
        <Input
          placeholder="请输入IP地址，例如：*************"
          value={newIp}
          onChange={(e) => setNewIp(e.target.value)}
        />
      </Modal>
    </div>
  );
};

export default SecuritySettings;
