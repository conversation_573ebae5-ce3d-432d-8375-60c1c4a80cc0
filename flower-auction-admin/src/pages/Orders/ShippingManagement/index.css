.action-card {
  border: 1px solid #f0f0f0;
  background: #fafafa;
}

.action-card .ant-card-body {
  padding: 12px 16px;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 时间轴样式优化 */
.ant-timeline-item-content {
  margin-left: 8px;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 600;
  color: #333;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .ant-statistic-content {
    font-size: 16px;
  }
}
