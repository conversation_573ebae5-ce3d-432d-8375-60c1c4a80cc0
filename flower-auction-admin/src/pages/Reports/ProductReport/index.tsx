import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Table,
  Space,
  message,
  Spin,
  Empty,
  Tag,
  Progress,
} from 'antd';
import {
  ShoppingOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  DownloadOutlined,
  ReloadOutlined,
  StarOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 商品统计数据接口
interface ProductStatistics {
  totalProducts: number;
  activeProducts: number;
  soldProducts: number;
  avgPrice: number;
  totalViews: number;
  totalSales: number;
  conversionRate: number;
  returnRate: number;
}

// 分类销售数据接口
interface CategorySalesData {
  category: string;
  sales: number;
  count: number;
  revenue: number;
}

// 商品性能排行接口
interface ProductPerformanceRank {
  id: number;
  productName: string;
  category: string;
  price: number;
  views: number;
  sales: number;
  revenue: number;
  conversionRate: number;
  rating: number;
  status: 'active' | 'sold' | 'expired';
}

// 价格分布数据接口
interface PriceDistribution {
  priceRange: string;
  count: number;
  percentage: number;
}

const ProductReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [category, setCategory] = useState<string>('all');
  
  const [statistics, setStatistics] = useState<ProductStatistics>({
    totalProducts: 0,
    activeProducts: 0,
    soldProducts: 0,
    avgPrice: 0,
    totalViews: 0,
    totalSales: 0,
    conversionRate: 0,
    returnRate: 0,
  });

  const [categorySalesData, setCategorySalesData] = useState<CategorySalesData[]>([]);
  const [productRanks, setProductRanks] = useState<ProductPerformanceRank[]>([]);
  const [priceDistribution, setPriceDistribution] = useState<PriceDistribution[]>([]);

  // 获取商品报表数据
  const fetchProductData = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      // 模拟数据
      const mockStatistics: ProductStatistics = {
        totalProducts: 2456,
        activeProducts: 1234,
        soldProducts: 987,
        avgPrice: 156.8,
        totalViews: 125600,
        totalSales: 3456,
        conversionRate: 2.75,
        returnRate: 1.2,
      };

      const mockCategorySalesData: CategorySalesData[] = [
        { category: '玫瑰', sales: 1200, count: 450, revenue: 180000 },
        { category: '康乃馨', sales: 800, count: 320, revenue: 96000 },
        { category: '百合', sales: 600, count: 240, revenue: 84000 },
        { category: '郁金香', sales: 400, count: 160, revenue: 64000 },
        { category: '其他', sales: 456, count: 186, revenue: 68400 },
      ];

      const mockProductRanks: ProductPerformanceRank[] = [
        {
          id: 1,
          productName: '精品红玫瑰',
          category: '玫瑰',
          price: 25.0,
          views: 2560,
          sales: 156,
          revenue: 3900,
          conversionRate: 6.09,
          rating: 4.8,
          status: 'active',
        },
        {
          id: 2,
          productName: '白玫瑰花束',
          category: '玫瑰',
          price: 30.0,
          views: 2100,
          sales: 98,
          revenue: 2940,
          conversionRate: 4.67,
          rating: 4.6,
          status: 'sold',
        },
        {
          id: 3,
          productName: '粉色康乃馨',
          category: '康乃馨',
          price: 18.0,
          views: 1800,
          sales: 87,
          revenue: 1566,
          conversionRate: 4.83,
          rating: 4.5,
          status: 'active',
        },
      ];

      const mockPriceDistribution: PriceDistribution[] = [
        { priceRange: '0-50元', count: 1200, percentage: 48.8 },
        { priceRange: '50-100元', count: 800, percentage: 32.6 },
        { priceRange: '100-200元', count: 300, percentage: 12.2 },
        { priceRange: '200-500元', count: 120, percentage: 4.9 },
        { priceRange: '500元以上', count: 36, percentage: 1.5 },
      ];

      setStatistics(mockStatistics);
      setCategorySalesData(mockCategorySalesData);
      setProductRanks(mockProductRanks);
      setPriceDistribution(mockPriceDistribution);

    } catch (error: any) {
      console.error('获取商品报表数据失败:', error);
      message.error('获取商品报表数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchProductData();
    }, 300);

    return () => clearTimeout(timer);
  }, [dateRange, category, fetchProductData]);

  const handleRefresh = () => {
    fetchProductData();
  };

  const handleExport = () => {
    message.success('商品报表导出功能开发中...');
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '在售' },
      sold: { color: 'default', text: '已售' },
      expired: { color: 'error', text: '过期' },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 商品性能表格列
  const productColumns = [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price.toFixed(2)}`,
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.price - b.price,
    },
    {
      title: '浏览量',
      dataIndex: 'views',
      key: 'views',
      render: (views: number) => views.toLocaleString(),
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.views - b.views,
    },
    {
      title: '销量',
      dataIndex: 'sales',
      key: 'sales',
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.sales - b.sales,
    },
    {
      title: '收入',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue: number) => `¥${revenue.toLocaleString()}`,
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.revenue - b.revenue,
    },
    {
      title: '转化率',
      dataIndex: 'conversionRate',
      key: 'conversionRate',
      render: (rate: number) => (
        <div>
          <Progress
            percent={rate}
            size="small"
            format={(percent) => `${percent}%`}
            strokeColor={rate > 5 ? '#52c41a' : rate > 3 ? '#faad14' : '#f5222d'}
          />
        </div>
      ),
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.conversionRate - b.conversionRate,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating: number) => (
        <span>
          <StarOutlined style={{ color: '#faad14', marginRight: 4 }} />
          {rating.toFixed(1)}
        </span>
      ),
      sorter: (a: ProductPerformanceRank, b: ProductPerformanceRank) => a.rating - b.rating,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
  ];

  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <ShoppingOutlined /> 商品报表
      </Title>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <span style={{ marginRight: 8 }}>时间范围:</span>
            <RangePicker
              value={dateRange}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0], dates[1]]);
                }
              }}
              style={{ marginRight: 16 }}
            />
          </Col>
          <Col>
            <span style={{ marginRight: 8 }}>商品分类:</span>
            <Select
              value={category}
              onChange={setCategory}
              style={{ width: 120, marginRight: 16 }}
            >
              <Option value="all">全部</Option>
              <Option value="rose">玫瑰</Option>
              <Option value="carnation">康乃馨</Option>
              <Option value="lily">百合</Option>
              <Option value="tulip">郁金香</Option>
            </Select>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="商品总数"
                value={statistics.totalProducts}
                valueStyle={{ color: '#1890ff' }}
                prefix={<ShoppingOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                在售商品 {statistics.activeProducts} 个
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总浏览量"
                value={statistics.totalViews}
                valueStyle={{ color: '#52c41a' }}
                prefix={<EyeOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                平均转化率 {statistics.conversionRate}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总销量"
                value={statistics.totalSales}
                valueStyle={{ color: '#722ed1' }}
                prefix={<TrophyOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                已售商品 {statistics.soldProducts} 个
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均价格"
                value={statistics.avgPrice}
                precision={2}
                valueStyle={{ color: '#fa8c16' }}
                prefix="¥"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                退货率 {statistics.returnRate}%
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {/* 分类销售统计 */}
          <Col span={12}>
            <Card title="分类销售统计">
              {categorySalesData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={categorySalesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="sales" fill="#1890ff" name="销量" />
                    <Bar dataKey="revenue" fill="#52c41a" name="收入" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 价格分布 */}
          <Col span={12}>
            <Card title="价格分布">
              {priceDistribution.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={priceDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ priceRange, percentage }: any) => `${priceRange} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {priceDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [value.toLocaleString(), '商品数量']} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 商品性能排行 */}
        <Card title="商品性能排行">
          <Table
            columns={productColumns}
            dataSource={productRanks}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            size="small"
          />
        </Card>
      </Spin>
    </div>
  );
};

export default ProductReport;
