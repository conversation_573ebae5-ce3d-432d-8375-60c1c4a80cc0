import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Table,
  Space,
  message,
  Spin,
  Empty,
} from 'antd';
import {
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DownloadOutlined,
  ReloadOutlined,
  TrophyOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 销售统计数据接口
interface SalesStatistics {
  totalSales: number;
  totalOrders: number;
  totalCustomers: number;
  avgOrderValue: number;
  salesGrowth: number;
  orderGrowth: number;
  customerGrowth: number;
  conversionRate: number;
}

// 销售趋势数据接口
interface SalesTrendData {
  date: string;
  sales: number;
  orders: number;
  customers: number;
}

// 商品销售排行接口
interface ProductSalesRank {
  id: number;
  productName: string;
  category: string;
  salesAmount: number;
  salesCount: number;
  revenue: number;
  rank: number;
}

// 销售渠道分布接口
interface ChannelDistribution {
  channel: string;
  value: number;
  percentage: number;
}

const SalesReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [timeGranularity, setTimeGranularity] = useState<'day' | 'week' | 'month'>('day');
  
  const [statistics, setStatistics] = useState<SalesStatistics>({
    totalSales: 0,
    totalOrders: 0,
    totalCustomers: 0,
    avgOrderValue: 0,
    salesGrowth: 0,
    orderGrowth: 0,
    customerGrowth: 0,
    conversionRate: 0,
  });

  const [trendData, setTrendData] = useState<SalesTrendData[]>([]);
  const [productRanks, setProductRanks] = useState<ProductSalesRank[]>([]);
  const [channelData, setChannelData] = useState<ChannelDistribution[]>([]);

  // 获取销售报表数据
  const fetchSalesData = useCallback(async () => {
    if (loading) return; // 防止重复调用
    
    setLoading(true);
    try {
      // 这里应该调用后端API获取数据
      // const response = await reportService.getSalesReport({
      //   startDate: dateRange[0].format('YYYY-MM-DD'),
      //   endDate: dateRange[1].format('YYYY-MM-DD'),
      //   granularity: timeGranularity,
      // });

      // 模拟数据
      const mockStatistics: SalesStatistics = {
        totalSales: 1250000,
        totalOrders: 3456,
        totalCustomers: 1234,
        avgOrderValue: 361.5,
        salesGrowth: 15.6,
        orderGrowth: 12.3,
        customerGrowth: 8.9,
        conversionRate: 3.2,
      };

      const mockTrendData: SalesTrendData[] = Array.from({ length: 30 }, (_, i) => ({
        date: dayjs().subtract(29 - i, 'day').format('MM-DD'),
        sales: Math.floor(Math.random() * 50000) + 20000,
        orders: Math.floor(Math.random() * 150) + 50,
        customers: Math.floor(Math.random() * 80) + 20,
      }));

      const mockProductRanks: ProductSalesRank[] = [
        { id: 1, productName: '红玫瑰', category: '玫瑰', salesAmount: 1500, salesCount: 300, revenue: 45000, rank: 1 },
        { id: 2, productName: '白玫瑰', category: '玫瑰', salesAmount: 1200, salesCount: 250, revenue: 36000, rank: 2 },
        { id: 3, productName: '康乃馨', category: '康乃馨', salesAmount: 1000, salesCount: 200, revenue: 30000, rank: 3 },
        { id: 4, productName: '百合花', category: '百合', salesAmount: 800, salesCount: 160, revenue: 24000, rank: 4 },
        { id: 5, productName: '郁金香', category: '郁金香', salesAmount: 600, salesCount: 120, revenue: 18000, rank: 5 },
      ];

      const mockChannelData: ChannelDistribution[] = [
        { channel: '线上拍卖', value: 650000, percentage: 52 },
        { channel: '线下拍卖', value: 400000, percentage: 32 },
        { channel: '直销', value: 200000, percentage: 16 },
      ];

      setStatistics(mockStatistics);
      setTrendData(mockTrendData);
      setProductRanks(mockProductRanks);
      setChannelData(mockChannelData);

    } catch (error: any) {
      console.error('获取销售报表数据失败:', error);
      message.error('获取销售报表数据失败');
    } finally {
      setLoading(false);
    }
  }, []); // 移除所有依赖，防止循环调用

  // 使用useEffect，但添加防抖机制
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchSalesData();
    }, 300); // 300ms防抖

    return () => clearTimeout(timer);
  }, [dateRange, timeGranularity, fetchSalesData]); // 添加fetchSalesData依赖

  // 手动刷新数据
  const handleRefresh = () => {
    fetchSalesData();
  };

  // 导出报表
  const handleExport = () => {
    message.success('报表导出功能开发中...');
  };

  // 图表颜色配置
  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];

  // 商品排行表格列
  const productColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => (
        <span style={{ fontWeight: 'bold', color: rank <= 3 ? '#f5222d' : '#666' }}>
          {rank}
        </span>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '销售数量',
      dataIndex: 'salesCount',
      key: 'salesCount',
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '销售金额',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue: number) => `¥${revenue.toLocaleString()}`,
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <BarChartOutlined /> 销售报表
      </Title>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <span style={{ marginRight: 8 }}>时间范围:</span>
            <RangePicker
              value={dateRange}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0], dates[1]]);
                }
              }}
              style={{ marginRight: 16 }}
            />
          </Col>
          <Col>
            <span style={{ marginRight: 8 }}>时间粒度:</span>
            <Select
              value={timeGranularity}
              onChange={setTimeGranularity}
              style={{ width: 100, marginRight: 16 }}
            >
              <Option value="day">按天</Option>
              <Option value="week">按周</Option>
              <Option value="month">按月</Option>
            </Select>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总销售额"
                value={statistics.totalSales}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                较上期增长 {statistics.salesGrowth}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总订单数"
                value={statistics.totalOrders}
                valueStyle={{ color: '#1890ff' }}
                prefix={<ShoppingCartOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                较上期增长 {statistics.orderGrowth}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="客户数量"
                value={statistics.totalCustomers}
                valueStyle={{ color: '#722ed1' }}
                prefix={<UserOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                较上期增长 {statistics.customerGrowth}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均订单价值"
                value={statistics.avgOrderValue}
                precision={2}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<TrophyOutlined />}
                suffix="元"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                转化率 {statistics.conversionRate}%
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {/* 销售趋势图 */}
          <Col span={16}>
            <Card title={<span><LineChartOutlined /> 销售趋势</span>}>
              {trendData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="sales"
                      stroke="#1890ff"
                      name="销售额"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="orders"
                      stroke="#52c41a"
                      name="订单数"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 销售渠道分布 */}
          <Col span={8}>
            <Card title={<span><PieChartOutlined /> 销售渠道分布</span>}>
              {channelData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={channelData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ channel, percentage }: any) => `${channel} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {channelData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [`¥${value.toLocaleString()}`, '销售额']} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 商品销售排行 */}
        <Card title="商品销售排行">
          <Table
            columns={productColumns}
            dataSource={productRanks}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      </Spin>
    </div>
  );
};

export default SalesReport;
