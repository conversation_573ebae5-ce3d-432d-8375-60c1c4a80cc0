import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Table,
  Space,
  message,
  Spin,
  Empty,
  Tag,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  RiseOutlined,
  FallOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CrownOutlined,
  HeartOutlined,
} from '@ant-design/icons';
import {
  Line<PERSON>hart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import dayjs from 'dayjs';
import { userReportAPI } from '../../../services/reportService';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 用户统计数据接口
interface UserStatistics {
  totalUsers: number;
  newUsers: number;
  activeUsers: number;
  retentionRate: number;
  userGrowth: number;
  activeGrowth: number;
  avgSessionDuration: number;
  bounceRate: number;
}

// 用户增长趋势接口
interface UserGrowthData {
  date: string;
  newUsers: number;
  activeUsers: number;
  totalUsers: number;
}

// 用户分布数据接口
interface UserDistribution {
  category: string;
  value: number;
  percentage: number;
}

// 用户活跃度排行接口
interface UserActivityRank {
  id: number;
  username: string;
  email: string;
  loginCount: number;
  lastLoginTime: string;
  totalSpent: number;
  orderCount: number;
  userType: 'buyer' | 'seller' | 'both';
  status: 'active' | 'inactive';
}

const UserReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [userType, setUserType] = useState<'all' | 'buyer' | 'seller'>('all');
  
  const [statistics, setStatistics] = useState<UserStatistics>({
    totalUsers: 0,
    newUsers: 0,
    activeUsers: 0,
    retentionRate: 0,
    userGrowth: 0,
    activeGrowth: 0,
    avgSessionDuration: 0,
    bounceRate: 0,
  });

  const [growthData, setGrowthData] = useState<UserGrowthData[]>([]);
  const [distributionData, setDistributionData] = useState<UserDistribution[]>([]);
  const [activityRanks, setActivityRanks] = useState<UserActivityRank[]>([]);

  // 获取用户报表数据
  const fetchUserData = useCallback(async () => {
    if (loading) return;

    setLoading(true);
    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      // 并行获取所有数据
      const [
        statisticsResponse,
        growthResponse,
        distributionResponse,
        activityResponse
      ] = await Promise.all([
        userReportAPI.getUserReport({
          start_date: startDate,
          end_date: endDate,
          user_type: userType,
        }),
        userReportAPI.getUserGrowthTrend({
          start_date: startDate,
          end_date: endDate,
        }),
        userReportAPI.getUserDistribution(),
        userReportAPI.getUserActivityRank({
          user_type: userType,
          limit: 20,
        })
      ]);

      // 设置统计数据
      if (statisticsResponse.data?.success) {
        setStatistics(statisticsResponse.data.data);
      }

      // 设置增长趋势数据
      if (growthResponse.data?.success) {
        setGrowthData(growthResponse.data.data);
      }

      // 设置分布数据
      if (distributionResponse.data?.success) {
        setDistributionData(distributionResponse.data.data);
      }

      // 设置活跃度排行数据
      if (activityResponse.data?.success) {
        setActivityRanks(activityResponse.data.data);
      }

    } catch (error: any) {
      console.error('获取用户报表数据失败:', error);
      message.error('获取用户报表数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchUserData();
    }, 300);

    return () => clearTimeout(timer);
  }, [dateRange, userType, fetchUserData]);

  const handleRefresh = () => {
    fetchUserData();
  };

  const handleExport = () => {
    message.success('用户报表导出功能开发中...');
  };

  // 获取用户类型标签
  const getUserTypeTag = (type: string) => {
    const typeMap = {
      buyer: { color: 'blue', text: '买家' },
      seller: { color: 'green', text: '卖家' },
      both: { color: 'purple', text: '买家+卖家' },
    };
    const config = typeMap[type as keyof typeof typeMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '活跃' },
      inactive: { color: 'default', text: '不活跃' },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 用户活跃度表格列
  const activityColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '用户类型',
      dataIndex: 'userType',
      key: 'userType',
      render: (type: string) => getUserTypeTag(type),
    },
    {
      title: '登录次数',
      dataIndex: 'loginCount',
      key: 'loginCount',
      sorter: (a: UserActivityRank, b: UserActivityRank) => a.loginCount - b.loginCount,
    },
    {
      title: '消费金额',
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      render: (amount: number) => `¥${amount.toLocaleString()}`,
      sorter: (a: UserActivityRank, b: UserActivityRank) => a.totalSpent - b.totalSpent,
    },
    {
      title: '订单数',
      dataIndex: 'orderCount',
      key: 'orderCount',
      sorter: (a: UserActivityRank, b: UserActivityRank) => a.orderCount - b.orderCount,
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginTime',
      key: 'lastLoginTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
  ];

  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <TeamOutlined /> 用户报表
      </Title>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <span style={{ marginRight: 8 }}>时间范围:</span>
            <RangePicker
              value={dateRange}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0], dates[1]]);
                }
              }}
              style={{ marginRight: 16 }}
            />
          </Col>
          <Col>
            <span style={{ marginRight: 8 }}>用户类型:</span>
            <Select
              value={userType}
              onChange={setUserType}
              style={{ width: 120, marginRight: 16 }}
            >
              <Option value="all">全部</Option>
              <Option value="buyer">买家</Option>
              <Option value="seller">卖家</Option>
            </Select>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总用户数"
                value={statistics.totalUsers}
                valueStyle={{ color: '#1890ff' }}
                prefix={<UserOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                较上期增长 {statistics.userGrowth}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="新增用户"
                value={statistics.newUsers}
                valueStyle={{ color: '#52c41a' }}
                prefix={<RiseOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                活跃用户增长 {statistics.activeGrowth}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={statistics.activeUsers}
                valueStyle={{ color: '#722ed1' }}
                prefix={<HeartOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                留存率 {statistics.retentionRate}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均会话时长"
                value={statistics.avgSessionDuration}
                precision={1}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<CrownOutlined />}
                suffix="分钟"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                跳出率 {statistics.bounceRate}%
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {/* 用户增长趋势 */}
          <Col span={16}>
            <Card title="用户增长趋势">
              {growthData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={growthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="newUsers"
                      stroke="#52c41a"
                      name="新增用户"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="activeUsers"
                      stroke="#1890ff"
                      name="活跃用户"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 用户类型分布 */}
          <Col span={8}>
            <Card title="用户类型分布">
              {distributionData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={distributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }: any) => `${category} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {distributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [value.toLocaleString(), '用户数']} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 用户活跃度排行 */}
        <Card title="用户活跃度排行">
          <Table
            columns={activityColumns}
            dataSource={activityRanks}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            size="small"
          />
        </Card>
      </Spin>
    </div>
  );
};

export default UserReport;
