import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Tag,
  message,
  Statistic,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 交易类型枚举
enum TransactionType {
  AUCTION_PAYMENT = 'auction_payment',
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  COMMISSION = 'commission',
  REFUND = 'refund',
}

// 交易状态枚举
enum TransactionStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// 交易记录接口
interface TransactionRecord {
  id: number;
  transactionNo: string;
  userId: number;
  userName: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  description: string;
  createdAt: string;
  updatedAt: string;
  relatedOrderNo?: string;
  paymentMethod?: string;
}

// 查询参数接口
interface QueryParams {
  transactionNo?: string;
  userName?: string;
  type?: TransactionType;
  status?: TransactionStatus;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

// 统计数据接口
interface TransactionStatistics {
  totalAmount: number;
  totalCount: number;
  todayAmount: number;
  todayCount: number;
  typeDistribution: Record<TransactionType, number>;
  statusDistribution: Record<TransactionStatus, number>;
}

const TransactionRecords: React.FC = () => {
  const [records, setRecords] = useState<TransactionRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [statistics, setStatistics] = useState<TransactionStatistics>({
    totalAmount: 0,
    totalCount: 0,
    todayAmount: 0,
    todayCount: 0,
    typeDistribution: {} as Record<TransactionType, number>,
    statusDistribution: {} as Record<TransactionStatus, number>,
  });
  const [searchForm] = Form.useForm();

  // 获取交易记录列表
  const fetchRecords = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockData = {
        success: true,
        data: {
          list: [
            {
              id: 1,
              transactionNo: 'TXN202312010001',
              userId: 1001,
              userName: '张三',
              type: TransactionType.AUCTION_PAYMENT,
              amount: 15000.00,
              status: TransactionStatus.SUCCESS,
              description: '拍卖支付 - 玫瑰花批次#001',
              createdAt: '2023-12-01 10:30:00',
              updatedAt: '2023-12-01 10:30:05',
              relatedOrderNo: 'ORD202312010001',
              paymentMethod: '支付宝',
            },
            {
              id: 2,
              transactionNo: 'TXN202312010002',
              userId: 1002,
              userName: '李四',
              type: TransactionType.DEPOSIT,
              amount: 50000.00,
              status: TransactionStatus.SUCCESS,
              description: '账户充值',
              createdAt: '2023-12-01 09:15:00',
              updatedAt: '2023-12-01 09:15:10',
              paymentMethod: '银行转账',
            },
          ],
          total: 2,
        },
      };

      if (mockData.success) {
        setRecords(mockData.data.list);
        setTotal(mockData.data.total);
      } else {
        message.error('获取交易记录失败');
      }
    } catch (error: any) {
      console.error('获取交易记录失败:', error);
      message.error('获取交易记录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      // 模拟API调用
      const mockStats = {
        success: true,
        data: {
          totalAmount: 1250000.00,
          totalCount: 156,
          todayAmount: 85000.00,
          todayCount: 12,
          typeDistribution: {
            [TransactionType.AUCTION_PAYMENT]: 45,
            [TransactionType.DEPOSIT]: 30,
            [TransactionType.WITHDRAWAL]: 15,
            [TransactionType.COMMISSION]: 40,
            [TransactionType.REFUND]: 8,
          },
          statusDistribution: {
            [TransactionStatus.SUCCESS]: 140,
            [TransactionStatus.PENDING]: 8,
            [TransactionStatus.FAILED]: 5,
            [TransactionStatus.CANCELLED]: 3,
          },
        },
      };

      if (mockStats.success) {
        setStatistics(mockStats.data);
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchRecords();
    fetchStatistics();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    const { dateRange, ...otherValues } = values;
    setQueryParams({
      ...queryParams,
      ...otherValues,
      dateRange: dateRange ? [
        dateRange[0].format('YYYY-MM-DD'),
        dateRange[1].format('YYYY-MM-DD'),
      ] : undefined,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 导出数据
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 查看详情
  const handleViewDetail = (record: TransactionRecord) => {
    message.info(`查看交易详情: ${record.transactionNo}`);
  };

  // 交易类型映射
  const typeMap = {
    [TransactionType.AUCTION_PAYMENT]: { text: '拍卖支付', color: 'blue' },
    [TransactionType.DEPOSIT]: { text: '充值', color: 'green' },
    [TransactionType.WITHDRAWAL]: { text: '提现', color: 'orange' },
    [TransactionType.COMMISSION]: { text: '佣金', color: 'purple' },
    [TransactionType.REFUND]: { text: '退款', color: 'red' },
  };

  // 状态映射
  const statusMap = {
    [TransactionStatus.PENDING]: { text: '处理中', color: 'processing' },
    [TransactionStatus.SUCCESS]: { text: '成功', color: 'success' },
    [TransactionStatus.FAILED]: { text: '失败', color: 'error' },
    [TransactionStatus.CANCELLED]: { text: '已取消', color: 'default' },
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>交易记录</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总交易金额"
              value={statistics.totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总交易笔数"
              value={statistics.totalCount}
              suffix="笔"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日交易金额"
              value={statistics.todayAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日交易笔数"
              value={statistics.todayCount}
              suffix="笔"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="transactionNo" label="交易单号">
                <Input placeholder="请输入交易单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="userName" label="用户名称">
                <Input placeholder="请输入用户名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="type" label="交易类型">
                <Select placeholder="请选择交易类型" allowClear>
                  {Object.entries(typeMap).map(([key, value]) => (
                    <Option key={key} value={key}>
                      {value.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="交易状态">
                <Select placeholder="请选择交易状态" allowClear>
                  {Object.entries(statusMap).map(([key, value]) => (
                    <Option key={key} value={key}>
                      {value.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="交易时间">
                <RangePicker
                  style={{ width: '100%' }}
                  placeholder={['开始日期', '结束日期']}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出数据
              </Button>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRecords}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 交易记录表格 */}
      <Card>
        <Table
          columns={[
            {
              title: '交易单号',
              dataIndex: 'transactionNo',
              key: 'transactionNo',
              width: 150,
              render: (text: string) => (
                <Tooltip title={text}>
                  <span style={{ fontFamily: 'monospace' }}>{text}</span>
                </Tooltip>
              ),
            },
            {
              title: '用户名称',
              dataIndex: 'userName',
              key: 'userName',
              width: 120,
            },
            {
              title: '交易类型',
              dataIndex: 'type',
              key: 'type',
              width: 100,
              render: (type: TransactionType) => {
                const typeInfo = typeMap[type];
                return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
              },
            },
            {
              title: '交易金额',
              dataIndex: 'amount',
              key: 'amount',
              width: 120,
              render: (amount: number) => (
                <span style={{ color: '#3f8600', fontWeight: 'bold' }}>
                  ¥{amount.toLocaleString()}
                </span>
              ),
            },
            {
              title: '交易状态',
              dataIndex: 'status',
              key: 'status',
              width: 100,
              render: (status: TransactionStatus) => {
                const statusInfo = statusMap[status];
                return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
              },
            },
            {
              title: '交易描述',
              dataIndex: 'description',
              key: 'description',
              width: 200,
              ellipsis: {
                showTitle: false,
              },
              render: (text: string) => (
                <Tooltip title={text}>
                  {text}
                </Tooltip>
              ),
            },
            {
              title: '支付方式',
              dataIndex: 'paymentMethod',
              key: 'paymentMethod',
              width: 100,
              render: (text: string) => text || '-',
            },
            {
              title: '交易时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              width: 160,
              render: (text: string) => new Date(text).toLocaleString(),
            },
            {
              title: '操作',
              key: 'action',
              width: 100,
              fixed: 'right',
              render: (_, record: TransactionRecord) => (
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleViewDetail(record)}
                >
                  详情
                </Button>
              ),
            },
          ]}
          dataSource={records}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>
    </div>
  );
};

export default TransactionRecords;
