<!DOCTYPE html>
<html>
<head>
    <title>Toast测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-button { margin: 10px; padding: 10px 20px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #40a9ff; }
    </style>
</head>
<body>
    <h1>Toast功能测试</h1>
    <p>这个页面用来测试前端应用的Toast功能是否正常工作</p>
    
    <button class="test-button" onclick="testErrorToast()">测试错误Toast</button>
    <button class="test-button" onclick="testSuccessToast()">测试成功Toast</button>
    <button class="test-button" onclick="testWarningToast()">测试警告Toast</button>
    
    <div id="result" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px;"></div>

    <script>
        // 在前端应用的控制台中运行这些函数
        function testErrorToast() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <h3>测试错误Toast</h3>
                <p>请在前端应用的控制台中运行以下代码：</p>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 测试错误Toast
import { message } from 'antd';
message.error({
    content: '这是一个测试错误消息',
    duration: 5,
    style: {
        marginTop: '20vh',
    },
});
                </pre>
                <p>或者直接运行：</p>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 如果antd已经加载
window.antd?.message?.error('测试错误消息');
                </pre>
            `;
        }

        function testSuccessToast() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <h3>测试成功Toast</h3>
                <p>请在前端应用的控制台中运行以下代码：</p>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 测试成功Toast
import { message } from 'antd';
message.success({
    content: '这是一个测试成功消息',
    duration: 3,
    style: {
        marginTop: '20vh',
    },
});
                </pre>
            `;
        }

        function testWarningToast() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <h3>测试警告Toast</h3>
                <p>请在前端应用的控制台中运行以下代码：</p>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 测试警告Toast
import { message } from 'antd';
message.warning({
    content: '这是一个测试警告消息',
    duration: 4,
    style: {
        marginTop: '20vh',
    },
});
                </pre>
            `;
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <h3>使用说明</h3>
                <p>1. 打开前端应用 <a href="http://localhost:3002" target="_blank">http://localhost:3002</a></p>
                <p>2. 打开浏览器开发者工具的控制台</p>
                <p>3. 点击上面的按钮获取测试代码</p>
                <p>4. 在前端应用的控制台中运行测试代码</p>
                <p>5. 观察是否有Toast消息弹出</p>
                
                <h4>快速测试代码：</h4>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 直接在前端应用控制台运行
if (window.antd && window.antd.message) {
    window.antd.message.error('测试错误消息');
} else {
    console.log('antd未找到，尝试其他方式...');
    // 尝试通过React DevTools或其他方式
}
                </pre>
            `;
        });
    </script>
</body>
</html>
