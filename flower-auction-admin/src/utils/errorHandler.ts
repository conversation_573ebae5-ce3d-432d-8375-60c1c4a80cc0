/**
 * 错误处理工具函数
 * 统一处理前端错误信息的显示
 */

import { message } from 'antd';

export interface ErrorResponse {
  response?: {
    status: number;
    data: {
      error?: string;
      message?: string;
    };
  };
  message?: string;
}

export interface ApiResponse {
  success: boolean;
  code?: string;
  message?: string;
  data?: any;
}

/**
 * 显示错误Toast消息
 * @param errorMessage 错误信息
 * @param duration 显示时长（秒）
 */
export const showErrorToast = (errorMessage: string, duration: number = 5): void => {
  console.log('🔴 显示错误Toast:', errorMessage); // 调试日志
  console.log('🔴 Toast参数:', { content: errorMessage, duration, style: { marginTop: '20vh' } }); // 调试日志

  try {
    // 尝试使用静态方法
    message.error({
      content: errorMessage,
      duration,
      style: {
        marginTop: '20vh',
      },
    });
    console.log('🔴 Toast已调用message.error'); // 调试日志
  } catch (error) {
    console.error('🔴 Toast调用失败:', error);
    // 备用方案：直接在控制台显示错误
    console.error('❌ 错误信息:', errorMessage);
    // 尝试使用原生alert作为最后的备用方案
    if (typeof window !== 'undefined') {
      window.alert(`错误: ${errorMessage}`);
    }
  }
};

/**
 * 显示成功Toast消息
 * @param successMessage 成功信息
 * @param duration 显示时长（秒）
 */
export const showSuccessToast = (successMessage: string, duration: number = 3): void => {
  console.log('🟢 显示成功Toast:', successMessage); // 调试日志

  try {
    message.success({
      content: successMessage,
      duration,
      style: {
        marginTop: '20vh',
      },
    });
    console.log('🟢 Toast已调用message.success'); // 调试日志
  } catch (error) {
    console.error('🟢 成功Toast调用失败:', error);
    console.log('✅ 成功信息:', successMessage);
  }
};

/**
 * 显示警告Toast消息
 * @param warningMessage 警告信息
 * @param duration 显示时长（秒）
 */
export const showWarningToast = (warningMessage: string, duration: number = 4): void => {
  message.warning({
    content: warningMessage,
    duration,
    style: {
      marginTop: '20vh',
    },
  });
};

/**
 * 处理API响应错误信息
 * @param response API响应对象
 * @param defaultMessage 默认错误信息
 * @returns 用户友好的错误信息
 */
export const handleApiResponseError = (response: ApiResponse, defaultMessage: string = '操作失败'): string => {
  if (!response.message) {
    return defaultMessage;
  }

  // 直接返回后端的错误信息，后端应该返回用户友好的中文错误信息
  return response.message;
};

/**
 * 处理网络请求异常错误
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 * @returns 用户友好的错误信息
 */
export const handleNetworkError = (error: ErrorResponse, defaultMessage: string = '请求失败，请稍后重试'): string => {
  if (error.response) {
    const { status, data } = error.response;

    // 优先使用后端返回的错误信息
    if (data.error) {
      return data.error;
    }

    if (data.message) {
      return data.message;
    }

    // 根据HTTP状态码返回通用错误信息
    switch (status) {
      case 400:
        return '请求参数错误，请检查输入信息';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 409:
        return '数据冲突，请检查是否重复提交';
      case 422:
        return '数据验证失败，请检查输入信息';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误，请联系管理员';
      case 502:
        return '网关错误，请稍后重试';
      case 503:
        return '服务暂时不可用，请稍后重试';
      case 504:
        return '请求超时，请稍后重试';
      default:
        return `请求失败 (${status})，请稍后重试`;
    }
  } else if (error.message) {
    // 处理网络连接错误等
    if (error.message.includes('Network Error')) {
      return '网络连接失败，请检查网络后重试';
    }
    if (error.message.includes('timeout')) {
      return '请求超时，请稍后重试';
    }
    return error.message;
  }

  return defaultMessage;
};

/**
 * 角色管理相关的错误处理
 */
export const handleRoleError = (error: ErrorResponse | ApiResponse): string => {
  // 如果是API响应错误
  if ('success' in error) {
    return handleApiResponseError(error, '角色操作失败');
  }

  // 如果是网络错误
  const errorMsg = handleNetworkError(error, '角色操作失败，请稍后重试');

  // 针对角色管理的特殊错误处理
  if (errorMsg.includes('角色编码已存在')) {
    return '角色编码已存在，请选择其他编码';
  }
  if (errorMsg.includes('角色名称已存在')) {
    return '角色名称已存在，请使用其他名称';
  }

  return errorMsg;
};

/**
 * 用户管理相关的错误处理
 */
export const handleUserError = (error: ErrorResponse | ApiResponse): string => {
  // 如果是API响应错误
  if ('success' in error) {
    return handleApiResponseError(error, '用户操作失败');
  }

  // 如果是网络错误
  const errorMsg = handleNetworkError(error, '用户操作失败，请稍后重试');

  // 针对用户管理的特殊错误处理
  if (errorMsg.includes('用户名已存在')) {
    return '用户名已存在，请使用其他用户名';
  }
  if (errorMsg.includes('手机号已存在')) {
    return '手机号已被使用，请使用其他手机号';
  }
  if (errorMsg.includes('邮箱已存在')) {
    return '邮箱已被使用，请使用其他邮箱';
  }

  return errorMsg;
};

/**
 * 商品管理相关的错误处理
 */
export const handleProductError = (error: ErrorResponse | ApiResponse): string => {
  // 如果是API响应错误
  if ('success' in error) {
    return handleApiResponseError(error, '商品操作失败');
  }

  // 如果是网络错误
  const errorMsg = handleNetworkError(error, '商品操作失败，请稍后重试');

  // 针对商品管理的特殊错误处理
  if (errorMsg.includes('商品名称已存在')) {
    return '商品名称已存在，请使用其他名称';
  }
  if (errorMsg.includes('分类不存在')) {
    return '商品分类不存在，请重新选择';
  }

  return errorMsg;
};

/**
 * 拍卖管理相关的错误处理
 */
export const handleAuctionError = (error: ErrorResponse | ApiResponse): string => {
  // 如果是API响应错误
  if ('success' in error) {
    return handleApiResponseError(error, '拍卖操作失败');
  }

  // 如果是网络错误
  const errorMsg = handleNetworkError(error, '拍卖操作失败，请稍后重试');

  // 针对拍卖管理的特殊错误处理
  if (errorMsg.includes('拍卖已开始')) {
    return '拍卖已开始，无法修改';
  }
  if (errorMsg.includes('拍卖已结束')) {
    return '拍卖已结束，无法操作';
  }

  return errorMsg;
};

/**
 * 通用错误处理函数
 * 根据模块类型自动选择合适的错误处理方式，并显示Toast消息
 */
export const handleError = (
  error: ErrorResponse | ApiResponse,
  module: 'role' | 'user' | 'product' | 'auction' | 'general' = 'general',
  showToast: boolean = true
): string => {
  console.log('🚨 handleError 被调用:', { error, module, showToast }); // 调试日志

  let errorMessage: string;

  switch (module) {
    case 'role':
      errorMessage = handleRoleError(error);
      break;
    case 'user':
      errorMessage = handleUserError(error);
      break;
    case 'product':
      errorMessage = handleProductError(error);
      break;
    case 'auction':
      errorMessage = handleAuctionError(error);
      break;
    default:
      if ('success' in error) {
        errorMessage = handleApiResponseError(error);
      } else {
        errorMessage = handleNetworkError(error);
      }
  }

  console.log('🔍 处理后的错误信息:', errorMessage); // 调试日志

  // 自动显示Toast消息
  if (showToast) {
    console.log('🍞 准备显示Toast:', errorMessage); // 调试日志
    showErrorToast(errorMessage);
  }

  return errorMessage;
};

/**
 * 处理成功响应并显示Toast消息
 */
export const handleSuccess = (
  message: string,
  showToast: boolean = true
): void => {
  if (showToast) {
    showSuccessToast(message);
  }
};
