import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setUser, clearUser, setLoading } from '../../store/slices/authSlice';
import { authService } from '../../services/authService';
import { RootState } from '../../store';

// JWT解析工具函数
const parseJWT = (token: string) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to parse JWT:', error);
    return null;
  }
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading, user } = useSelector((state: RootState) => state.auth);
  const [isInitializing, setIsInitializing] = useState(true);
  const initializationRef = useRef(false);

  useEffect(() => {
    // 防止重复初始化
    if (initializationRef.current) {
      return;
    }
    initializationRef.current = true;

    const initializeAuth = async () => {
      const token = localStorage.getItem('token');

      // 如果没有token，直接清除状态
      if (!token) {
        dispatch(clearUser());
        setIsInitializing(false);
        return;
      }

      // 如果已经有用户信息且token存在，跳过API调用
      if (user && isAuthenticated) {
        setIsInitializing(false);
        return;
      }

      try {
        dispatch(setLoading(true));

        // 检查token是否即将过期（提前5分钟刷新）
        const tokenPayload = parseJWT(token);
        if (tokenPayload && tokenPayload.exp) {
          const expirationTime = tokenPayload.exp * 1000; // 转换为毫秒
          const currentTime = Date.now();
          const timeUntilExpiry = expirationTime - currentTime;

          // 如果token在5分钟内过期，尝试刷新
          if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
            const refreshToken = localStorage.getItem('refreshToken');
            if (refreshToken) {
              try {
                const refreshResponse = await authService.refreshToken(refreshToken);
                if (refreshResponse.success && refreshResponse.data) {
                  localStorage.setItem('token', refreshResponse.data.token);
                  localStorage.setItem('refreshToken', refreshResponse.data.refreshToken);
                }
              } catch (refreshError) {
                console.warn('Token refresh failed during initialization:', refreshError);
              }
            }
          }
        }

        // 获取用户信息
        const response = await authService.getUserInfo();
        if (response.success && response.data) {
          dispatch(setUser(response.data));
        } else {
          // token无效，清除认证信息
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          dispatch(clearUser());
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        dispatch(clearUser());
      } finally {
        dispatch(setLoading(false));
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, []); // 空依赖数组，确保只在组件挂载时执行一次

  // 如果正在初始化，显示加载状态
  if (isInitializing || loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div>正在初始化...</div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
