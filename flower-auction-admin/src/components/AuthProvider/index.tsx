import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setUser, clearUser, setLoading } from '../../store/slices/authSlice';
import { authService } from '../../services/authService';
import { RootState } from '../../store';

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading } = useSelector((state: RootState) => state.auth);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        dispatch(clearUser());
        setIsInitializing(false);
        return;
      }

      try {
        dispatch(setLoading(true));
        const response = await authService.getUserInfo();
        if (response.success && response.data) {
          dispatch(setUser(response.data));
        } else {
          // token无效，清除认证信息
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          dispatch(clearUser());
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        dispatch(clearUser());
      } finally {
        dispatch(setLoading(false));
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, []); // 空依赖数组，确保只在组件挂载时执行一次

  // 如果正在初始化，显示加载状态
  if (isInitializing || loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div>正在初始化...</div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
