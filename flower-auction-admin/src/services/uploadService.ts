import { apiClient } from './apiClient';

export interface UploadResponse {
  success: boolean;
  data?: {
    url: string;
    filename: string;
    size: number;
  };
  message?: string;
}

class UploadService {
  // 上传单张图片
  async uploadImage(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image', file);

    try {
      const response = await apiClient.post('/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('图片上传失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '图片上传失败',
      };
    }
  }

  // 批量上传图片
  async uploadImages(files: File[]): Promise<UploadResponse> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`images`, file);
    });

    try {
      const response = await apiClient.post('/upload/images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('批量图片上传失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '批量图片上传失败',
      };
    }
  }

  // 上传文档
  async uploadDocument(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('document', file);

    try {
      const response = await apiClient.post('/upload/document', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('文档上传失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '文档上传失败',
      };
    }
  }

  // 删除文件
  async deleteFile(fileUrl: string): Promise<UploadResponse> {
    try {
      const response = await apiClient.delete('/upload/file', {
        data: { url: fileUrl },
      });
      return response.data;
    } catch (error: any) {
      console.error('文件删除失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '文件删除失败',
      };
    }
  }

  // 获取文件信息
  async getFileInfo(fileUrl: string): Promise<UploadResponse> {
    try {
      const response = await apiClient.get('/upload/file/info', {
        params: { url: fileUrl },
      });
      return response.data;
    } catch (error: any) {
      console.error('获取文件信息失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '获取文件信息失败',
      };
    }
  }

  // 上传商品图片（专用接口）
  async uploadProductImages(productId: number, files: File[]): Promise<UploadResponse> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('images', file);
    });

    try {
      const response = await apiClient.post(`/products/${productId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('商品图片上传失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '商品图片上传失败',
      };
    }
  }

  // 获取商品图片列表
  async getProductImages(productId: number): Promise<UploadResponse> {
    try {
      const response = await apiClient.get(`/products/${productId}/images`);
      return response.data;
    } catch (error: any) {
      console.error('获取商品图片失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '获取商品图片失败',
      };
    }
  }

  // 删除商品图片
  async deleteProductImage(imageId: number): Promise<UploadResponse> {
    try {
      const response = await apiClient.delete(`/products/images/${imageId}`);
      return response.data;
    } catch (error: any) {
      console.error('删除商品图片失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '删除商品图片失败',
      };
    }
  }

  // 更新图片排序
  async updateImageOrder(imageId: number, order: number): Promise<UploadResponse> {
    try {
      const response = await apiClient.put(`/products/images/${imageId}/order`, {
        order,
      });
      return response.data;
    } catch (error: any) {
      console.error('更新图片排序失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '更新图片排序失败',
      };
    }
  }
}

export const uploadService = new UploadService();
