// WebSocket服务类
export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private listeners: Map<string, Set<Function>> = new Map();
  private isConnecting = false;

  constructor(url: string) {
    this.url = url;
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve();
        return;
      }

      this.isConnecting = true;

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connected');
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          // 自动重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.stopHeartbeat();
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }
  }

  // 发送消息
  send(type: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: Date.now(),
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  // 处理接收到的消息
  private handleMessage(message: any) {
    const { type, data } = message;
    this.emit(type, data);
  }

  // 添加事件监听器
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  // 移除事件监听器
  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  // 触发事件
  private emit(event: string, data?: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('事件回调执行失败:', error);
        }
      });
    }
  }

  // 开始心跳
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.send('ping', {});
    }, 30000); // 30秒心跳
  }

  // 停止心跳
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 计划重连
  private scheduleReconnect() {
    this.reconnectAttempts++;
    console.log(`计划第${this.reconnectAttempts}次重连...`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error);
      });
    }, this.reconnectInterval);
  }

  // 获取连接状态
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }
}

// 拍卖WebSocket服务
export class AuctionWebSocketService extends WebSocketService {
  constructor() {
    // 根据环境变量或配置决定WebSocket地址
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8080/ws/auction';
    super(wsUrl);
  }

  // 加入拍卖房间
  joinAuction(auctionId: string) {
    this.send('join_auction', { auctionId });
  }

  // 离开拍卖房间
  leaveAuction(auctionId: string) {
    this.send('leave_auction', { auctionId });
  }

  // 提交出价
  placeBid(auctionId: string, bidAmount: number) {
    this.send('place_bid', { auctionId, bidAmount });
  }

  // 开始拍卖
  startAuction(auctionId: string) {
    this.send('start_auction', { auctionId });
  }

  // 结束拍卖
  endAuction(auctionId: string) {
    this.send('end_auction', { auctionId });
  }

  // 暂停拍卖
  pauseAuction(auctionId: string) {
    this.send('pause_auction', { auctionId });
  }

  // 恢复拍卖
  resumeAuction(auctionId: string) {
    this.send('resume_auction', { auctionId });
  }
}

// 创建全局实例
export const auctionWebSocket = new AuctionWebSocketService();

// 拍卖事件类型
export enum AuctionEventType {
  AUCTION_STARTED = 'auction_started',
  AUCTION_ENDED = 'auction_ended',
  AUCTION_PAUSED = 'auction_paused',
  AUCTION_RESUMED = 'auction_resumed',
  BID_PLACED = 'bid_placed',
  BID_REJECTED = 'bid_rejected',
  PRICE_UPDATED = 'price_updated',
  TIME_UPDATED = 'time_updated',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  ERROR = 'error',
}

// 拍卖状态接口
export interface AuctionStatus {
  id: string;
  status: 'waiting' | 'active' | 'paused' | 'ended';
  currentPrice: number;
  startPrice: number;
  reservePrice?: number;
  timeRemaining: number;
  bidCount: number;
  participantCount: number;
  lastBidder?: {
    id: string;
    name: string;
    bidAmount: number;
    bidTime: string;
  };
}

// 出价信息接口
export interface BidInfo {
  id: string;
  auctionId: string;
  bidderId: string;
  bidderName: string;
  bidAmount: number;
  bidTime: string;
  isWinning: boolean;
}

export default WebSocketService;
