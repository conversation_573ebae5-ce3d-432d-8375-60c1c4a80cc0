import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import Login from '../pages/Login';
import Dashboard from '../pages/Dashboard';
import UserList from '../pages/Users/<USER>';
import RoleManagement from '../pages/Users/<USER>';
import ProductList from '../pages/Products/ProductList';
import CategoryManagement from '../pages/Products/CategoryManagement';
import ProductAudit from '../pages/Products/ProductAudit';
import AuctionList from '../pages/Auctions/AuctionList';
import AuctionItems from '../pages/Auctions/AuctionItems';
import BidRecords from '../pages/Auctions/BidRecords';
import LiveBidding from '../pages/Auctions/LiveBidding';
import OrderList from '../pages/Orders/OrderList';
import ShippingManagement from '../pages/Orders/ShippingManagement';
import AccountManagement from '../pages/Finance/AccountManagement';
import TransactionRecords from '../pages/Finance/TransactionRecords';
import FinanceReports from '../pages/Finance/FinanceReports';
import SystemSettings from '../pages/Settings/SystemSettings';
import SecuritySettings from '../pages/Settings/SecuritySettings';
import SystemLogs from '../pages/Settings/SystemLogs';
import BackupRestore from '../pages/Settings/BackupRestore';
import OperationLogs from '../pages/Settings/OperationLogs';

// 创建路由配置
export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
      },
      // 用户管理
      {
        path: 'users',
        children: [
          {
            path: 'list',
            element: <UserList />,
          },
          {
            path: 'roles',
            element: <RoleManagement />,
          },
        ],
      },
      // 商品管理
      {
        path: 'products',
        children: [
          {
            path: 'list',
            element: <ProductList />,
          },
          {
            path: 'categories',
            element: <CategoryManagement />,
          },
          {
            path: 'audit',
            element: <ProductAudit />,
          },
        ],
      },
      // 拍卖管理
      {
        path: 'auctions',
        children: [
          {
            path: 'list',
            element: <AuctionList />,
          },
          {
            path: 'items',
            element: <AuctionItems />,
          },
          {
            path: 'live',
            element: <LiveBidding />,
          },
          {
            path: 'bids',
            element: <BidRecords />,
          },
        ],
      },
      // 订单管理
      {
        path: 'orders',
        children: [
          {
            path: 'list',
            element: <OrderList />,
          },
          {
            path: 'shipping',
            element: <ShippingManagement />,
          },
        ],
      },
      // 财务管理
      {
        path: 'finance',
        children: [
          {
            path: 'accounts',
            element: <AccountManagement />,
          },
          {
            path: 'transactions',
            element: <TransactionRecords />,
          },
          {
            path: 'reports',
            element: <FinanceReports />,
          },
        ],
      },
      // 系统设置
      {
        path: 'settings',
        children: [
          {
            path: 'system',
            element: <SystemSettings />,
          },
          {
            path: 'security',
            element: <SecuritySettings />,
          },
          {
            path: 'system-logs',
            element: <SystemLogs />,
          },
          {
            path: 'backup-restore',
            element: <BackupRestore />,
          },
          {
            path: 'logs',
            element: <OperationLogs />,
          },
        ],
      },
    ],
  },
  // 404页面
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
]);
