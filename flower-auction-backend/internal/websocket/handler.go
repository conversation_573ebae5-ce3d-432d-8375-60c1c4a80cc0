package websocket

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Handler WebSocket处理器
type Handler struct {
	hub *Hub
}

// NewHandler 创建新的WebSocket处理器
func NewHandler() *Handler {
	hub := NewHub()
	go hub.Run()
	
	return &Handler{
		hub: hub,
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *Handler) HandleWebSocket(c *gin.Context) {
	ServeWS(h.hub, c.Writer, c.Request)
}

// GetAuctionStats 获取拍卖统计信息
func (h *Handler) GetAuctionStats(c *gin.Context) {
	auctionID := c.Param("auctionId")
	if auctionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "拍卖ID不能为空",
		})
		return
	}

	participantCount := h.hub.GetAuctionParticipantCount(auctionID)
	
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"auctionId":       auctionID,
			"participantCount": participantCount,
			"isActive":        participantCount > 0,
		},
	})
}

// BroadcastMessage 广播消息到拍卖房间
func (h *Handler) BroadcastMessage(c *gin.Context) {
	var req struct {
		AuctionID string      `json:"auctionId" binding:"required"`
		Type      string      `json:"type" binding:"required"`
		Data      interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数无效",
			"error":   err.Error(),
		})
		return
	}

	h.hub.BroadcastToAuction(req.AuctionID, req.Type, req.Data)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已广播",
	})
}

// GetHub 获取Hub实例
func (h *Handler) GetHub() *Hub {
	return h.hub
}
