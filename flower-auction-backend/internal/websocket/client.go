package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
	"github.com/google/uuid"
)

const (
	// 写入等待时间
	writeWait = 10 * time.Second

	// 读取等待时间
	pongWait = 60 * time.Second

	// ping周期，必须小于pongWait
	pingPeriod = (pongWait * 9) / 10

	// 最大消息大小
	maxMessageSize = 512
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源，生产环境应该限制
		return true
	},
}

// Client WebSocket客户端
type Client struct {
	// WebSocket连接
	conn *websocket.Conn

	// Hub引用
	hub *Hub

	// 客户端ID
	ID string

	// 当前所在的拍卖房间ID
	AuctionID string

	// 发送消息的通道
	send chan []byte

	// 用户信息
	UserID   string
	UserName string
	UserRole string
}

// ClientMessage 客户端消息
type ClientMessage struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// NewClient 创建新客户端
func NewClient(conn *websocket.Conn, hub *Hub) *Client {
	return &Client{
		conn: conn,
		hub:  hub,
		ID:   uuid.New().String(),
		send: make(chan []byte, 256),
	}
}

// readPump 读取消息
func (c *Client) readPump() {
	defer func() {
		c.hub.UnregisterClient(c)
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, messageBytes, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		var message ClientMessage
		if err := json.Unmarshal(messageBytes, &message); err != nil {
			log.Printf("解析消息失败: %v", err)
			continue
		}

		c.handleMessage(message)
	}
}

// writePump 写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 添加队列中的其他消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
func (c *Client) handleMessage(message ClientMessage) {
	switch message.Type {
	case "ping":
		c.sendMessage("pong", map[string]interface{}{
			"timestamp": time.Now().Unix(),
		})

	case "join_auction":
		if data, ok := message.Data.(map[string]interface{}); ok {
			if auctionID, exists := data["auctionId"].(string); exists {
				c.hub.JoinAuction(c, auctionID)
			}
		}

	case "leave_auction":
		if data, ok := message.Data.(map[string]interface{}); ok {
			if auctionID, exists := data["auctionId"].(string); exists {
				c.hub.LeaveAuction(c, auctionID)
			}
		}

	case "place_bid":
		c.handlePlaceBid(message.Data)

	case "start_auction":
		c.handleStartAuction(message.Data)

	case "end_auction":
		c.handleEndAuction(message.Data)

	case "pause_auction":
		c.handlePauseAuction(message.Data)

	case "resume_auction":
		c.handleResumeAuction(message.Data)

	default:
		log.Printf("未知消息类型: %s", message.Type)
	}
}

// handlePlaceBid 处理出价
func (c *Client) handlePlaceBid(data interface{}) {
	bidData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("无效的出价数据")
		return
	}

	auctionID, _ := bidData["auctionId"].(string)
	bidAmount, _ := bidData["bidAmount"].(float64)

	if auctionID == "" || bidAmount <= 0 {
		c.sendError("出价参数无效")
		return
	}

	// 这里应该验证出价逻辑，检查拍卖状态等
	// 暂时直接广播出价成功

	bidInfo := map[string]interface{}{
		"id":         uuid.New().String(),
		"auctionId":  auctionID,
		"bidderId":   c.ID,
		"bidderName": c.UserName,
		"bidAmount":  bidAmount,
		"bidTime":    time.Now().Format(time.RFC3339),
		"isWinning":  true,
	}

	// 广播新出价到拍卖房间
	c.hub.BroadcastToAuction(auctionID, "bid_placed", bidInfo)

	// 更新拍卖价格
	c.hub.BroadcastToAuction(auctionID, "price_updated", map[string]interface{}{
		"auctionId":    auctionID,
		"currentPrice": bidAmount,
		"lastBidder":   bidInfo,
	})
}

// handleStartAuction 处理开始拍卖
func (c *Client) handleStartAuction(data interface{}) {
	auctionData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("无效的拍卖数据")
		return
	}

	auctionID, _ := auctionData["auctionId"].(string)
	if auctionID == "" {
		c.sendError("拍卖ID不能为空")
		return
	}

	// 广播拍卖开始
	c.hub.BroadcastToAuction(auctionID, "auction_started", map[string]interface{}{
		"auctionId": auctionID,
		"startTime": time.Now().Format(time.RFC3339),
		"status":    "active",
	})
}

// handleEndAuction 处理结束拍卖
func (c *Client) handleEndAuction(data interface{}) {
	auctionData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("无效的拍卖数据")
		return
	}

	auctionID, _ := auctionData["auctionId"].(string)
	if auctionID == "" {
		c.sendError("拍卖ID不能为空")
		return
	}

	// 广播拍卖结束
	c.hub.BroadcastToAuction(auctionID, "auction_ended", map[string]interface{}{
		"auctionId": auctionID,
		"endTime":   time.Now().Format(time.RFC3339),
		"status":    "ended",
	})
}

// handlePauseAuction 处理暂停拍卖
func (c *Client) handlePauseAuction(data interface{}) {
	auctionData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("无效的拍卖数据")
		return
	}

	auctionID, _ := auctionData["auctionId"].(string)
	if auctionID == "" {
		c.sendError("拍卖ID不能为空")
		return
	}

	// 广播拍卖暂停
	c.hub.BroadcastToAuction(auctionID, "auction_paused", map[string]interface{}{
		"auctionId": auctionID,
		"pauseTime": time.Now().Format(time.RFC3339),
		"status":    "paused",
	})
}

// handleResumeAuction 处理恢复拍卖
func (c *Client) handleResumeAuction(data interface{}) {
	auctionData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("无效的拍卖数据")
		return
	}

	auctionID, _ := auctionData["auctionId"].(string)
	if auctionID == "" {
		c.sendError("拍卖ID不能为空")
		return
	}

	// 广播拍卖恢复
	c.hub.BroadcastToAuction(auctionID, "auction_resumed", map[string]interface{}{
		"auctionId":  auctionID,
		"resumeTime": time.Now().Format(time.RFC3339),
		"status":     "active",
	})
}

// sendMessage 发送消息给客户端
func (c *Client) sendMessage(msgType string, data interface{}) {
	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		close(c.send)
	}
}

// sendError 发送错误消息
func (c *Client) sendError(errorMsg string) {
	c.sendMessage("error", map[string]interface{}{
		"message": errorMsg,
	})
}

// ServeWS 处理WebSocket连接
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	client := NewClient(conn, hub)
	
	// 从查询参数获取用户信息
	client.UserID = r.URL.Query().Get("userId")
	client.UserName = r.URL.Query().Get("userName")
	client.UserRole = r.URL.Query().Get("userRole")

	client.hub.RegisterClient(client)

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}
