#!/usr/bin/env bash

# 投屏端启动脚本
# 使用环境变量配置的端口，默认3003

set -e

# 检查bash版本
if [ -z "$BASH_VERSION" ]; then
    echo "错误: 此脚本需要bash环境运行"
    echo "请使用: bash $0 或 ./$(basename $0)"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认端口配置
DEFAULT_PORT=3003
PORT=${PORT:-$DEFAULT_PORT}

# 应用信息
APP_NAME="花卉拍卖系统 - 投屏端"
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

log_info "🌸 启动 $APP_NAME"
log_info "📍 应用目录: $APP_DIR"
log_info "🚀 目标端口: $PORT"

# 检查Node.js环境
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或不在PATH中"
        log_error "请安装 Node.js 18+ 版本"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装或不在PATH中"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_success "Node.js 环境正常: $node_version"
}

# 检查端口占用
check_port() {
    if command -v lsof &> /dev/null; then
        local pid=$(lsof -ti:$PORT 2>/dev/null)
        if [ ! -z "$pid" ]; then
            log_warning "端口 $PORT 被进程 $pid 占用"
            read -p "是否终止占用进程并继续? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                log_info "正在终止进程 $pid..."
                kill -9 $pid 2>/dev/null || true
                sleep 2
                log_success "进程已终止"
            else
                log_error "启动取消"
                exit 1
            fi
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log_info "📦 检查并安装依赖..."
    
    if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
        log_info "首次安装依赖，这可能需要几分钟..."
        npm install
    else
        # 检查package.json是否有更新
        if [ "package.json" -nt "node_modules" ]; then
            log_info "检测到依赖更新，重新安装..."
            npm install
        else
            log_info "依赖已是最新版本"
        fi
    fi
    
    log_success "依赖安装完成"
}

# 检查环境配置
check_env() {
    log_info "🔧 检查环境配置..."
    
    # 检查.env文件
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，将使用默认配置"
    else
        log_success "找到 .env 配置文件"
        # 显示关键配置
        if grep -q "VITE_API_BASE_URL" .env; then
            local api_url=$(grep "VITE_API_BASE_URL" .env | cut -d'=' -f2)
            log_info "API地址: $api_url"
        fi
        if grep -q "VITE_WS_URL" .env; then
            local ws_url=$(grep "VITE_WS_URL" .env | cut -d'=' -f2)
            log_info "WebSocket地址: $ws_url"
        fi
    fi
}

# 构建项目（生产模式）
build_project() {
    if [ "$1" = "build" ]; then
        log_info "🔨 构建生产版本..."
        npm run build
        log_success "构建完成，输出目录: dist/"
        return 0
    fi
    return 1
}

# 启动开发服务器
start_dev_server() {
    log_info "🎯 启动开发服务器..."
    log_info "🌐 访问地址: http://localhost:$PORT"
    log_info "📝 按 Ctrl+C 停止服务"
    log_info "💡 投屏端建议使用全屏模式浏览"
    echo
    
    # 设置端口环境变量并启动
    export PORT=$PORT
    npm run dev
}

# 显示帮助信息
show_help() {
    echo "投屏端启动脚本"
    echo
    echo "用法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  dev     启动开发服务器 (默认)"
    echo "  build   构建生产版本"
    echo "  help    显示帮助信息"
    echo
    echo "环境变量:"
    echo "  PORT    指定端口号 (默认: 3003)"
    echo
    echo "示例:"
    echo "  $0              # 启动开发服务器"
    echo "  $0 dev          # 启动开发服务器"
    echo "  $0 build        # 构建生产版本"
    echo "  PORT=3007 $0    # 在端口3007启动"
    echo
    echo "注意:"
    echo "  投屏端适用于大屏幕显示，建议使用全屏模式"
}

# 清理函数
cleanup() {
    log_info "正在清理..."
    # 这里可以添加清理逻辑
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    local command=${1:-dev}
    
    case $command in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "build")
            check_node
            install_dependencies
            check_env
            build_project build
            ;;
        "dev"|"")
            check_node
            check_port
            install_dependencies
            check_env
            start_dev_server
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
