/* 拍卖概览样式 */

.auction-overview {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.panel-title {
  margin: 0 0 12px 0 !important;
  color: #1890ff;
  font-size: 14px !important;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 会话信息 */
.session-info .session-card {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(82, 196, 26, 0.1));
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.session-progress {
  margin-bottom: 12px;
}

.session-progress .ant-progress {
  margin: 4px 0 0 0;
}

.session-stats {
  font-size: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

/* 活跃拍卖 */
.active-auctions .ant-list-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.active-auctions .ant-list-item:last-child {
  border-bottom: none;
}

.active-item .item-content {
  width: 100%;
}

.active-item .item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.active-item .item-price {
  margin-bottom: 4px;
}

.active-item .item-stats {
  font-size: 11px;
}

/* 热门商品 */
.popular-items .ant-list-item,
.high-value-items .ant-list-item {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.popular-items .ant-list-item:last-child,
.high-value-items .ant-list-item:last-child {
  border-bottom: none;
}

.popular-item .item-content,
.high-value-item .item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.popular-item .item-rank,
.high-value-item .item-rank {
  flex: 0 0 auto;
}

.popular-item .item-info,
.high-value-item .item-info {
  flex: 1;
  min-width: 0;
}

.popular-item .item-meta,
.high-value-item .item-meta {
  font-size: 11px;
  margin-top: 2px;
}

/* 空状态 */
.auction-overview .ant-empty {
  margin: 12px 0;
}

.auction-overview .ant-empty-description {
  font-size: 12px;
  color: #999;
}

/* 动画效果 */
.active-item {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(82, 196, 26, 0.05);
  }
}

.popular-item:hover,
.high-value-item:hover {
  background-color: rgba(24, 144, 255, 0.05);
  transition: background-color 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .auction-overview {
    padding: 4px;
  }
  
  .panel-title {
    font-size: 13px !important;
    margin-bottom: 8px !important;
  }
  
  .session-header {
    margin-bottom: 8px;
  }
  
  .session-progress {
    margin-bottom: 8px;
  }
}

/* 暗色主题适配 */
[data-theme='dark'] .session-info .session-card {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(82, 196, 26, 0.2));
  border: 1px solid rgba(24, 144, 255, 0.3);
}

[data-theme='dark'] .active-auctions .ant-list-item,
[data-theme='dark'] .popular-items .ant-list-item,
[data-theme='dark'] .high-value-items .ant-list-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .active-item {
  animation: pulseDark 2s infinite;
}

@keyframes pulseDark {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(82, 196, 26, 0.1);
  }
}

[data-theme='dark'] .popular-item:hover,
[data-theme='dark'] .high-value-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 滚动条样式 */
.auction-overview::-webkit-scrollbar {
  width: 4px;
}

.auction-overview::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.auction-overview::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.3);
  border-radius: 2px;
}

.auction-overview::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.5);
}
