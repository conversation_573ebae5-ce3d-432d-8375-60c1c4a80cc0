/* 投屏端布局样式 */

.display-layout {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.display-layout.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.display-header {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.display-title {
  color: #fff;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-center span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

.header-right .ant-btn {
  color: rgba(255, 255, 255, 0.8);
  border: none;
}

.header-right .ant-btn:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.display-content {
  padding: 16px;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.statistics-column,
.clocks-column,
.overview-column {
  height: 100%;
}

.statistics-card,
.clocks-card,
.overview-card {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.statistics-card .ant-card-head,
.clocks-card .ant-card-head,
.overview-card .ant-card-head {
  background: rgba(24, 144, 255, 0.1);
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
}

.statistics-card .ant-card-head-title,
.clocks-card .ant-card-head-title,
.overview-card .ant-card-head-title {
  color: #1890ff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .display-header {
    padding: 0 16px;
  }
  
  .display-title {
    font-size: 20px;
  }
  
  .header-center {
    display: none;
  }
}

@media (max-width: 768px) {
  .display-content {
    padding: 8px;
  }
  
  .display-title {
    font-size: 18px;
  }
  
  .header-left {
    gap: 16px;
  }
}

/* 暗色主题适配 */
.ant-layout.display-layout {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme='dark'] .statistics-card,
[data-theme='dark'] .clocks-card,
[data-theme='dark'] .overview-card {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .statistics-card .ant-card-head,
[data-theme='dark'] .clocks-card .ant-card-head,
[data-theme='dark'] .overview-card .ant-card-head {
  background: rgba(24, 144, 255, 0.2);
  border-bottom: 1px solid rgba(24, 144, 255, 0.3);
}

/* 动画效果 */
.statistics-card,
.clocks-card,
.overview-card {
  transition: all 0.3s ease;
}

.statistics-card:hover,
.clocks-card:hover,
.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 滚动条样式 */
.display-content ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.display-content ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.display-content ::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5);
  border-radius: 3px;
}

.display-content ::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.7);
}
