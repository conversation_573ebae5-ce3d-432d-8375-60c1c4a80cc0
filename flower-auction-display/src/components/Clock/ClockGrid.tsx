import React from 'react';
import { Row, Col, Empty, Spin } from 'antd';
import { useAppSelector } from '../../hooks/redux';
import {
  selectAllClocks,
  selectClocksLoading,
  selectClocksError,
} from '../../store/slices/clocksSlice';
import {
  selectCurrentItems,
} from '../../store/slices/auctionSlice';
import ClockCard from './ClockCard';
import './ClockGrid.css';

interface ClockGridProps {
  layout: 'grid' | 'list';
}

const ClockGrid: React.FC<ClockGridProps> = ({ layout }) => {
  const clocks = useAppSelector(selectAllClocks);
  const currentItems = useAppSelector(selectCurrentItems);
  const loading = useAppSelector(selectClocksLoading);
  const error = useAppSelector(selectClocksError);

  if (loading) {
    return (
      <div className="clock-grid-loading">
        <Spin size="large" tip="加载钟号数据..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="clock-grid-error">
        <Empty
          description={`加载失败: ${error}`}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  if (clocks.length === 0) {
    return (
      <div className="clock-grid-empty">
        <Empty
          description="暂无钟号数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  // 按钟号排序
  const sortedClocks = [...clocks].sort((a, b) => a.clockNumber - b.clockNumber);

  // 网格布局配置
  const getColSpan = () => {
    if (layout === 'list') return 24;
    
    const clockCount = clocks.length;
    if (clockCount <= 4) return 12; // 2列
    if (clockCount <= 9) return 8;  // 3列
    return 6; // 4列
  };

  return (
    <div className={`clock-grid clock-grid-${layout}`}>
      <Row gutter={[12, 12]} className="clock-grid-row">
        {sortedClocks.map((clock) => {
          const currentItem = currentItems[clock.clockNumber];
          
          return (
            <Col
              key={clock.clockNumber}
              span={getColSpan()}
              className="clock-grid-col"
            >
              <ClockCard
                clock={clock}
                currentItem={currentItem}
                layout={layout}
              />
            </Col>
          );
        })}
      </Row>
    </div>
  );
};

export default ClockGrid;
