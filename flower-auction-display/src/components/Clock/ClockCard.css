/* 钟号卡片样式 */

.clock-card {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.clock-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #d9d9d9;
  transition: all 0.3s ease;
}

/* 状态指示条 */
.clock-status-active::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
  animation: pulse 2s infinite;
}

.clock-status-paused::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.clock-status-error::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
  animation: blink 1s infinite;
}

.clock-status-idle::before {
  background: #d9d9d9;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 卡片标题 */
.clock-card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clock-number {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.clock-number-text {
  font-size: 14px;
}

/* 卡片内容 */
.clock-card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.clock-card-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #999;
}

/* 商品信息 */
.item-info {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.item-name {
  margin: 0 !important;
  color: #262626;
  font-size: 16px;
  line-height: 1.4;
}

.batch-number {
  font-size: 12px;
  color: #8c8c8c;
}

/* 价格信息 */
.price-info {
  flex: 1;
}

.price-row {
  text-align: center;
  margin-bottom: 8px;
}

.price-details {
  display: flex;
  justify-content: center;
  font-size: 12px;
}

/* 拍卖统计 */
.auction-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.auction-stats .anticon {
  color: #1890ff;
}

/* 进度条 */
.auction-progress {
  margin: 8px 0;
}

/* 商品详情 */
.item-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 列表布局样式 */
.clock-card-list {
  height: auto !important;
}

.clock-card-list .clock-card-content {
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.clock-card-list .item-info {
  flex: 1;
  border-bottom: none;
  padding-bottom: 0;
}

.clock-card-list .price-info {
  flex: 0 0 150px;
}

.clock-card-list .auction-stats {
  flex: 0 0 120px;
  border: none;
  padding: 0;
}

.clock-card-list .item-details {
  flex: 0 0 100px;
}

.clock-card-list .auction-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
}

/* 悬停效果 */
.clock-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.clock-status-active:hover {
  box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);
}

.clock-status-paused:hover {
  box-shadow: 0 8px 24px rgba(250, 173, 20, 0.3);
}

.clock-status-error:hover {
  box-shadow: 0 8px 24px rgba(255, 77, 79, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .clock-card-content {
    gap: 8px;
  }
  
  .item-name {
    font-size: 14px !important;
  }
  
  .price-row .ant-statistic-content-value {
    font-size: 16px !important;
  }
  
  .auction-stats {
    flex-direction: column;
    gap: 4px;
  }
}

/* 暗色主题适配 */
[data-theme='dark'] .clock-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
}

[data-theme='dark'] .item-info {
  border-bottom-color: rgba(255, 255, 255, 0.12);
}

[data-theme='dark'] .auction-stats {
  border-color: rgba(255, 255, 255, 0.12);
}

[data-theme='dark'] .item-name {
  color: rgba(255, 255, 255, 0.85);
}
