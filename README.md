# 🌸 昆明花卉拍卖系统

一个基于现代技术栈的专业花卉拍卖平台，支持实时竞价、多钟号并行拍卖、大屏展示等完整功能。

## 🎯 系统概述

本系统是为昆明花卉拍卖市场设计的完整解决方案，包含拍卖师端、购买商端、投屏端三个前端应用和统一的后端服务，实现了专业的花卉拍卖业务流程。

### 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   拍卖师端      │    │   购买商端      │    │   投屏端        │
│  (蓝色主题)     │    │  (粉色主题)     │    │  (大屏展示)     │
│                 │    │                 │    │                 │
│ • 钟号管理      │    │ • 批次浏览      │    │ • 实时监控      │
│ • 拍卖控制      │    │ • 竞价出价      │    │ • 数据统计      │
│ • 价格调整      │    │ • 关注管理      │    │ • 状态展示      │
│ • 状态监控      │    │ • 账户管理      │    │ • 趋势分析      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │      后端服务           │
                    │                         │
                    │ • RESTful API          │
                    │ • WebSocket 实时通信   │
                    │ • 微服务架构           │
                    │ • 数据库管理           │
                    │ • 权限控制             │
                    └─────────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │      数据存储           │
                    │                         │
                    │ • MySQL (主数据库)     │
                    │ • Redis (缓存/会话)    │
                    │ • 文件存储             │
                    └─────────────────────────┘
```

## 🚀 技术栈

### 后端技术
- **Go 1.21+** - 高性能后端语言
- **Gin** - 轻量级Web框架
- **GORM** - 强大的ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存和会话存储
- **WebSocket** - 实时通信
- **JWT** - 身份认证

### 前端技术
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全
- **Ant Design** - 企业级UI组件库
- **Redux Toolkit** - 状态管理
- **Vite** - 快速构建工具
- **ECharts** - 数据可视化

## 📁 项目结构

```
km/
├── flower-auction/                 # 后端服务
│   ├── cmd/                       # 应用程序入口
│   ├── internal/                  # 内部包
│   │   ├── api/                  # API处理器
│   │   ├── model/                # 数据模型
│   │   ├── service/              # 业务逻辑
│   │   └── dao/                  # 数据访问层
│   ├── pkg/                      # 公共包
│   ├── configs/                  # 配置文件
│   ├── docs/                     # 文档
│   └── migrations/               # 数据库迁移
├── flower-auction-auctioneer/     # 拍卖师端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── pages/                # 页面
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
├── flower-auction-buyer/          # 购买商端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── pages/                # 页面
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
├── flower-auction-display/        # 投屏端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
└── docs/                          # 项目文档
    ├── database/                 # 数据库文档
    ├── api/                      # API文档
    └── deployment/               # 部署文档
```

## ⚡ 快速开始

### 环境要求
- **Go 1.21+**
- **Node.js 18+**
- **MySQL 8.0+**
- **Redis 6.0+**

### 环境配置
系统使用环境变量进行配置管理，支持开发和生产环境：

#### 后端配置
- 配置文件：`flower-auction/config/config.yaml`
- 环境变量：`DB_HOST`, `DB_PASSWORD`, `JWT_SECRET`, `DOMAIN`

#### 前端配置
每个前端项目都有独立的环境配置文件：
- **开发环境**：`.env` (默认使用localhost:8080)
- **生产环境**：`.env.production` (使用真实域名)

主要环境变量：
- `VITE_API_BASE_URL`: API服务地址
- `VITE_WS_URL`: WebSocket服务地址
- `PORT`: 前端服务端口号

### 1. 克隆项目
```bash
git clone <repository-url>
cd km
```

### 2. 后端服务启动
```bash
cd flower-auction

# 安装依赖
go mod download

# 配置数据库
./scripts/migrate.sh dev init

# 启动服务
./run.sh
# 或者直接运行: go run main.go
```

### 3. 前端应用启动

#### 拍卖师端
```bash
cd flower-auction-auctioneer
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3001
```

#### 购买商端
```bash
cd flower-auction-buyer
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3002
```

#### 投屏端
```bash
cd flower-auction-display
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3003
```

### 4. 一键启动脚本

#### 全局启动（推荐）
```bash
# 一键启动所有服务
./start-all.sh

# 查看服务状态
./start-all.sh status

# 停止所有服务
./start-all.sh stop

# 重启所有服务
./start-all.sh restart

# 查看服务日志
./start-all.sh logs
```

#### 单独启动
```bash
# 后端服务
cd flower-auction && ./run.sh

# 前端应用（在不同终端窗口中运行）
cd flower-auction-auctioneer && ./run.sh  # 拍卖师端
cd flower-auction-buyer && ./run.sh       # 购买商端
cd flower-auction-display && ./run.sh     # 投屏端
```

## 🎨 系统特色

### 拍卖师端 (蓝色主题)
- 🎯 **钟号管理** - 12个钟号并行控制
- 🎮 **拍卖控制** - 开始/暂停/停止/调价
- 📊 **实时监控** - 竞价状态、用户活动
- 📈 **数据统计** - 成交统计、收益分析

### 购买商端 (粉色主题)
- 🛍️ **批次浏览** - 智能筛选、搜索功能
- 💰 **实时竞价** - 一键出价、自动出价
- ⭐ **关注管理** - 收藏商品、价格提醒
- 💳 **账户管理** - 余额查询、交易记录

### 投屏端 (大屏展示)
- 📺 **大屏适配** - 全屏模式、响应式布局
- 🔄 **实时同步** - 毫秒级数据更新
- 📊 **数据可视化** - 图表展示、趋势分析
- 🎨 **视觉效果** - 动画效果、状态指示

## 🔧 核心功能

### 拍卖功能
- ✅ 多钟号并行拍卖
- ✅ 实时竞价系统
- ✅ 价格调整机制
- ✅ 流拍处理
- ✅ 埋单功能

### 用户管理
- ✅ 角色权限控制
- ✅ 实名认证
- ✅ 企业认证
- ✅ 信用等级管理

### 资金管理
- ✅ 账户余额管理
- ✅ 资金冻结/解冻
- ✅ 交易记录
- ✅ 保证金管理

### 数据统计
- ✅ 实时交易统计
- ✅ 市场行情分析
- ✅ 用户行为分析
- ✅ 财务报表

## 📚 文档链接

- [后端API文档](./flower-auction/README.md)
- [拍卖师端文档](./flower-auction-auctioneer/README.md)
- [购买商端文档](./flower-auction-buyer/README.md)
- [投屏端文档](./flower-auction-display/README.md)
- [数据库设计](./docs/database/README.md)
- [部署指南](./docs/deployment/README.md)

## 🔗 访问地址

| 服务 | 开发环境 | 生产环境 |
|------|----------|----------|
| 后端API | http://localhost:8080 | https://api.flower-auction.com |
| 拍卖师端 | http://localhost:3001 | https://auctioneer.flower-auction.com |
| 购买商端 | http://localhost:3002 | https://buyer.flower-auction.com |
| 投屏端 | http://localhost:3003 | https://display.flower-auction.com |

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 开发团队

- **后端开发** - Go微服务架构
- **前端开发** - React生态系统
- **UI/UX设计** - 专业拍卖界面设计
- **数据库设计** - 高性能数据架构

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 技术支持: [Support Email]

---

🌸 **让花卉拍卖更加智能、高效、透明！**