import { io, Socket } from 'socket.io-client';
import { WebSocketMessage, AuctionRealTimeData } from '../types';
import authService from './authService';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private listeners: Map<string, Function[]> = new Map();

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected || this.isConnecting) {
        resolve();
        return;
      }

      this.isConnecting = true;
      const wsUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8081';
      const token = authService.getStoredToken();

      if (!token) {
        this.isConnecting = false;
        reject(new Error('No authentication token found'));
        return;
      }

      this.socket = io(wsUrl, {
        auth: {
          token: token
        },
        transports: ['websocket'],
        timeout: 10000,
      });

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.emit('connected', true);
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.emit('connected', false);
        
        // 自动重连
        if (reason === 'io server disconnect') {
          // 服务器主动断开，不重连
          return;
        }
        
        this.scheduleReconnect();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnecting = false;
        this.emit('connected', false);
        
        if (this.reconnectAttempts === 0) {
          reject(error);
        }
        
        this.scheduleReconnect();
      });

      // 监听拍卖相关事件
      this.socket.on('auction:start', (data) => {
        this.emit('auction:start', data);
      });

      this.socket.on('auction:end', (data) => {
        this.emit('auction:end', data);
      });

      this.socket.on('auction:bid', (data) => {
        this.emit('auction:bid', data);
      });

      this.socket.on('auction:price_update', (data) => {
        this.emit('auction:price_update', data);
      });

      this.socket.on('auction:status_update', (data) => {
        this.emit('auction:status_update', data);
      });

      // 监听通知事件
      this.socket.on('notification', (data) => {
        this.emit('notification', data);
      });

      // 监听实时数据更新
      this.socket.on('realtime:update', (data) => {
        this.emit('realtime:update', data);
      });
    });
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.listeners.clear();
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * 发送消息
   */
  send(event: string, data: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, message not sent:', event, data);
    }
  }

  /**
   * 加入拍卖房间
   */
  joinAuction(batchId: number): void {
    this.send('auction:join', { batchId });
  }

  /**
   * 离开拍卖房间
   */
  leaveAuction(batchId: number): void {
    this.send('auction:leave', { batchId });
  }

  /**
   * 发送出价
   */
  sendBid(batchId: number, price: number, quantity: number): void {
    this.send('auction:bid', { batchId, price, quantity });
  }

  /**
   * 订阅钟号
   */
  subscribeClock(clockNumber: number): void {
    this.send('clock:subscribe', { clockNumber });
  }

  /**
   * 取消订阅钟号
   */
  unsubscribeClock(clockNumber: number): void {
    this.send('clock:unsubscribe', { clockNumber });
  }

  /**
   * 添加事件监听器
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback?: Function): void {
    if (!this.listeners.has(event)) {
      return;
    }

    if (callback) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(event);
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebSocket event callback:', error);
        }
      });
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  } {
    return {
      connected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
    };
  }
}

export const websocketService = new WebSocketService();
export default websocketService;
