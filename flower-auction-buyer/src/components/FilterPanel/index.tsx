import React, { useState, useEffect } from 'react';
import { Drawer, Form, Select, Slider, DatePicker, Button, Space, Typography, Divider, Checkbox } from 'antd';
import { FilterOutlined, ClearOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { BatchQueryParams } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface FilterPanelProps {
  visible: boolean;
  onClose: () => void;
  onFilter: (filters: Partial<BatchQueryParams>) => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({ visible, onClose, onFilter }) => {
  const [form] = Form.useForm();
  const { categories } = useSelector((state: RootState) => state.batch);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);

  useEffect(() => {
    if (visible) {
      // 重置表单
      form.resetFields();
      setPriceRange([0, 10000]);
    }
  }, [visible, form]);

  const handleFilter = () => {
    const values = form.getFieldsValue();
    const filters: Partial<BatchQueryParams> = {};

    // 分类筛选
    if (values.categoryId) {
      filters.categoryId = values.categoryId;
    }

    // 质量等级筛选
    if (values.qualityLevel) {
      filters.qualityLevel = values.qualityLevel;
    }

    // 价格范围筛选
    if (priceRange[0] > 0 || priceRange[1] < 10000) {
      filters.priceMin = priceRange[0];
      filters.priceMax = priceRange[1];
    }

    // 时间范围筛选
    if (values.dateRange && values.dateRange.length === 2) {
      filters.startDate = values.dateRange[0].format('YYYY-MM-DD');
      filters.endDate = values.dateRange[1].format('YYYY-MM-DD');
    }

    // 钟号筛选
    if (values.clockNumber) {
      filters.clockNumber = values.clockNumber;
    }

    // 只看关注的
    if (values.onlyWatched) {
      filters.onlyWatched = true;
    }

    onFilter(filters);
  };

  const handleClear = () => {
    form.resetFields();
    setPriceRange([0, 10000]);
    onFilter({});
  };

  const qualityLevels = [
    { label: '特级', value: 1 },
    { label: '一级', value: 2 },
    { label: '二级', value: 3 },
    { label: '三级', value: 4 },
  ];

  const clockNumbers = Array.from({ length: 10 }, (_, i) => i + 1);

  return (
    <Drawer
      title={
        <Space>
          <FilterOutlined />
          <span>高级筛选</span>
        </Space>
      }
      placement="right"
      width={320}
      open={visible}
      onClose={onClose}
      footer={
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button icon={<ClearOutlined />} onClick={handleClear}>
            清空
          </Button>
          <Button type="primary" onClick={handleFilter}>
            应用筛选
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical">
        <Form.Item label="商品分类" name="categoryId">
          <Select placeholder="请选择分类" allowClear>
            {categories.map(category => (
              <Option key={category.id} value={category.id}>
                {category.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="质量等级" name="qualityLevel">
          <Select placeholder="请选择等级" allowClear>
            {qualityLevels.map(level => (
              <Option key={level.value} value={level.value}>
                {level.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="钟号" name="clockNumber">
          <Select placeholder="请选择钟号" allowClear>
            {clockNumbers.map(num => (
              <Option key={num} value={num}>
                钟号 {num}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Divider />

        <Form.Item label="价格范围">
          <div style={{ padding: '0 8px' }}>
            <Slider
              range
              min={0}
              max={10000}
              step={100}
              value={priceRange}
              onChange={(value) => setPriceRange(value as [number, number])}
              tooltip={{
                formatter: (value) => `¥${value}`
              }}
            />
            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
              <Text type="secondary">¥{priceRange[0]}</Text>
              <Text type="secondary">¥{priceRange[1]}</Text>
            </div>
          </div>
        </Form.Item>

        <Divider />

        <Form.Item label="拍卖时间" name="dateRange">
          <RangePicker
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
            format="YYYY-MM-DD"
            disabledDate={(current) => {
              // 不能选择今天之前的日期
              return current && current < dayjs().startOf('day');
            }}
          />
        </Form.Item>

        <Divider />

        <Form.Item name="onlyWatched" valuePropName="checked">
          <Checkbox>只看我关注的</Checkbox>
        </Form.Item>

        <div style={{ marginTop: 24 }}>
          <Title level={5}>筛选说明</Title>
          <ul style={{ paddingLeft: 16, margin: 0, color: '#8c8c8c', fontSize: 12 }}>
            <li>可以组合多个条件进行筛选</li>
            <li>价格范围基于起拍价筛选</li>
            <li>时间范围基于拍卖开始时间</li>
            <li>清空按钮会重置所有筛选条件</li>
          </ul>
        </div>
      </Form>
    </Drawer>
  );
};

export default FilterPanel;
