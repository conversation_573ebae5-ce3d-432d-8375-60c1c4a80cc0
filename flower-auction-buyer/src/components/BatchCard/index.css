.batch-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.batch-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #ff6b9d;
}

.batch-card.bidding {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
}

.batch-card.bidding:hover {
  box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);
}

.card-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-cover .ant-image {
  width: 100%;
  height: 100%;
}

.card-cover .ant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.batch-card:hover .card-cover .ant-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.3) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.batch-card:hover .card-overlay {
  opacity: 1;
}

.overlay-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.overlay-bottom {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.status-tag {
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hot-tag {
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.watch-btn {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  transition: all 0.3s ease;
}

.watch-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #ff6b9d;
  transform: scale(1.1);
}

.watch-btn.watched {
  color: #ff6b9d;
  background: rgba(255, 107, 157, 0.1);
}

.card-title {
  margin-bottom: 8px;
}

.product-name {
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 4px;
  display: block;
}

.batch-number {
  font-size: 12px;
  color: #8c8c8c;
}

.card-description {
  padding: 0;
}

.supplier-info {
  margin-bottom: 8px;
}

.quantity-info {
  margin-bottom: 12px;
}

.price-info {
  margin-bottom: 12px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.start-price {
  color: #8c8c8c;
  font-size: 14px;
}

.current-price {
  color: #f5222d;
  font-weight: 600;
  font-size: 16px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background: #f6ffed;
  border-radius: 4px;
  border: 1px solid #b7eb8f;
}

.time-info.urgent {
  background: #fff2e8;
  border-color: #ffbb96;
}

.time-info.urgent .time-icon {
  color: #fa8c16;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.time-icon {
  color: #52c41a;
  font-size: 12px;
}

.time-text {
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.time-info.urgent .time-text {
  color: #fa8c16;
}

.batch-card .ant-card-actions {
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.batch-card .ant-card-actions > li {
  margin: 8px 0;
}

.batch-card .ant-card-actions > li > span {
  color: #8c8c8c;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.batch-card .ant-card-actions > li:hover > span {
  color: #ff6b9d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-cover {
    height: 160px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .current-price {
    font-size: 14px;
  }
  
  .batch-card .ant-card-actions > li {
    margin: 4px 0;
  }
  
  .batch-card .ant-card-actions > li > span {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .card-cover {
    height: 140px;
  }
  
  .card-overlay {
    padding: 8px;
  }
  
  .watch-btn {
    width: 32px;
    height: 32px;
  }
  
  .status-tag,
  .hot-tag {
    font-size: 11px;
    padding: 2px 6px;
  }
}
