.layout-container {
  min-height: 100vh;
}

.layout-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(180deg, #ff6b9d 0%, #ff4d7d 100%);
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.logo-text {
  overflow: hidden;
}

.logo-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

.logo-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1;
}

.layout-menu {
  flex: 1;
  border-right: none;
  background: transparent;
}

.layout-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  color: rgba(255, 255, 255, 0.8);
}

.layout-menu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.layout-menu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.layout-menu .ant-menu-item .anticon {
  color: inherit;
}

.layout-main {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.layout-main.collapsed {
  margin-left: 80px;
}

.layout-header {
  background: white;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-button {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s;
}

.collapse-button:hover {
  background: #f0f0f0;
}

.header-right {
  display: flex;
  align-items: center;
}

.balance-info {
  padding: 8px 16px;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border-radius: 20px;
  color: white;
}

.balance-amount {
  color: white !important;
  font-size: 16px;
  margin-left: 4px;
}

.notification-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.notification-button:hover {
  background: #f0f0f0;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.user-avatar:hover {
  background: #f0f0f0;
}

.avatar {
  margin-right: 12px;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.2;
}

.user-type {
  color: #8c8c8c;
  font-size: 12px;
  line-height: 1.2;
  margin-top: 2px;
}

.layout-content {
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sider.open {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-header {
    padding: 0 16px;
  }
  
  .balance-info {
    display: none;
  }
  
  .user-info {
    display: none;
  }
}

@media (max-width: 576px) {
  .layout-header {
    padding: 0 12px;
  }
  
  .user-avatar {
    padding: 8px;
  }
  
  .logo {
    padding: 0 12px;
  }
  
  .layout-menu .ant-menu-item {
    margin: 4px;
  }
}

/* 动画效果 */
.layout-sider {
  transition: all 0.2s;
}

.layout-main {
  transition: all 0.2s;
}

/* 当侧边栏收起时的样式调整 */
.ant-layout-sider-collapsed .logo-text {
  display: none;
}

.ant-layout-sider-collapsed .logo {
  justify-content: center;
}

.ant-layout-sider-collapsed .layout-menu .ant-menu-item {
  margin: 4px;
  text-align: center;
}

/* 菜单项图标样式 */
.layout-menu .ant-menu-item .anticon {
  font-size: 16px;
}

/* 用户下拉菜单样式 */
.ant-dropdown-menu-item {
  display: flex;
  align-items: center;
}

.ant-dropdown-menu-item .anticon {
  margin-right: 8px;
  font-size: 14px;
}

/* 通知徽章样式 */
.ant-badge-count {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

/* 滚动条样式 */
.layout-sider .ant-layout-sider-children::-webkit-scrollbar {
  width: 4px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 余额信息动画 */
.balance-info {
  animation: balanceGlow 3s ease-in-out infinite;
}

@keyframes balanceGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 107, 157, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 107, 157, 0.5);
  }
}
