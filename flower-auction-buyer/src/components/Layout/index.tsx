import React, { useState, useEffect } from 'react';
import { Layout as AntLayout, Menu, Dropdown, Avatar, Space, Typography, Button, Modal, message, Badge } from 'antd';
import {
  HomeOutlined,
  HeartOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  WalletOutlined,
  UserOutlined,
  LogoutOutlined,
  LockOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { logoutAsync } from '../../store/slices/authSlice';
import { getNotificationsAsync } from '../../store/slices/notificationSlice';
import './index.css';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { unreadCount } = useSelector((state: RootState) => state.notification);
  const [collapsed, setCollapsed] = useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);

  useEffect(() => {
    // 获取未读通知数量
    dispatch(getNotificationsAsync({ page: 1, size: 1, isRead: false }));
  }, [dispatch]);

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/watchlist',
      icon: <HeartOutlined />,
      label: '我的关注',
    },
    {
      key: '/orders',
      icon: <ShoppingCartOutlined />,
      label: '我的订单',
    },
    {
      key: '/bids',
      icon: <TrophyOutlined />,
      label: '我的出价',
    },
    {
      key: '/account',
      icon: <WalletOutlined />,
      label: '账户管理',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = async () => {
    try {
      await dispatch(logoutAsync()).unwrap();
      message.success('登出成功');
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      message.error('登出失败');
    } finally {
      setLogoutModalVisible(false);
    }
  };

  const handleChangePassword = () => {
    message.info('修改密码功能开发中...');
  };

  const handleNotificationClick = () => {
    navigate('/notifications');
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'change-password',
      icon: <LockOutlined />,
      label: '修改密码',
      onClick: handleChangePassword,
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      onClick: () => navigate('/help'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => setLogoutModalVisible(true),
    },
  ];

  return (
    <AntLayout className="layout-container">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="layout-sider"
        width={240}
      >
        <div className="logo">
          <div className="logo-icon">🌸</div>
          {!collapsed && (
            <div className="logo-text">
              <div className="logo-title">花卉拍卖</div>
              <div className="logo-subtitle">购买商平台</div>
            </div>
          )}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
        />
      </Sider>

      <AntLayout className="layout-main">
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="collapse-button"
            />
          </div>

          <div className="header-right">
            <Space size="large">
              <div className="balance-info">
                <Text type="secondary">可用余额：</Text>
                <Text strong className="balance-amount">
                  ¥{user?.balance?.toFixed(2) || '0.00'}
                </Text>
              </div>

              <Button
                type="text"
                icon={
                  <Badge count={unreadCount} size="small">
                    <BellOutlined style={{ fontSize: 18 }} />
                  </Badge>
                }
                onClick={handleNotificationClick}
                className="notification-button"
              />
              
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="user-avatar">
                  <Avatar 
                    icon={<UserOutlined />} 
                    className="avatar"
                  />
                  <div className="user-info">
                    <div className="username">
                      {user?.realName || user?.username}
                    </div>
                    <div className="user-type">
                      {user?.companyName || '个人用户'}
                    </div>
                  </div>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        <Content className="layout-content">
          {children}
        </Content>
      </AntLayout>

      {/* 登出确认弹窗 */}
      <Modal
        title="确认登出"
        open={logoutModalVisible}
        onOk={handleLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认"
        cancelText="取消"
        okType="danger"
      >
        <p>您确定要退出登录吗？</p>
      </Modal>
    </AntLayout>
  );
};

export default Layout;
