import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import AuthGuard from '../components/AuthGuard';
import Layout from '../components/Layout';
import Login from '../pages/Login';
import Home from '../pages/Home';

// 404页面组件
const NotFound: React.FC = () => (
  <Result
    status="404"
    title="404"
    subTitle="抱歉，您访问的页面不存在。"
    extra={
      <Button type="primary" onClick={() => window.location.href = '/'}>
        返回首页
      </Button>
    }
  />
);

// 403页面组件
const Forbidden: React.FC = () => (
  <Result
    status="403"
    title="403"
    subTitle="抱歉，您没有权限访问此页面。"
    extra={
      <Button type="primary" onClick={() => window.location.href = '/'}>
        返回首页
      </Button>
    }
  />
);

// 开发中页面组件
const UnderDevelopment: React.FC<{ title: string }> = ({ title }) => (
  <Result
    status="info"
    title={title}
    subTitle="该功能正在开发中，敬请期待..."
    extra={
      <Button type="primary" onClick={() => window.location.href = '/'}>
        返回首页
      </Button>
    }
  />
);

// 创建路由配置
export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: (
      <AuthGuard>
        <Layout>
          <Home />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/batch/:id',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="批次详情页面" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/auction/:id',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="实时竞价页面" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/watchlist',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="我的关注" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/orders',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="我的订单" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/bids',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="我的出价" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/account',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="账户管理" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/profile',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="个人资料" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/notifications',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="消息通知" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/help',
    element: (
      <AuthGuard>
        <Layout>
          <UnderDevelopment title="帮助中心" />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/403',
    element: <Forbidden />,
  },
  {
    path: '*',
    element: <NotFound />,
  },
]);

export default router;
