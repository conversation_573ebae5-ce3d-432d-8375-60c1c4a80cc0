.login-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="150" cy="150" r="80" fill="url(%23a)"/><circle cx="850" cy="200" r="120" fill="url(%23a)"/><circle cx="300" cy="800" r="100" fill="url(%23a)"/><circle cx="750" cy="750" r="90" fill="url(%23a)"/></svg>');
  background-size: cover;
  animation: float 25s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

.login-content {
  display: flex;
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  z-index: 1;
}

.login-card {
  flex: 1;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 3rem;
  color: #ff6b9d;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.login-title {
  margin-bottom: 0.5rem !important;
  color: #262626;
  font-weight: 600;
}

.login-subtitle {
  color: #8c8c8c;
  font-size: 16px;
}

.login-tabs {
  margin-bottom: 1rem;
}

.login-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.login-form,
.register-form {
  margin-top: 1rem;
}

.login-form .ant-form-item,
.register-form .ant-form-item {
  margin-bottom: 1.5rem;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  color: #ff6b9d;
}

.forgot-password:hover {
  color: #ff4d7d;
}

.login-button,
.register-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover,
.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.verification-code {
  display: flex;
  gap: 8px;
}

.verification-code .ant-input {
  flex: 1;
}

.send-code-btn {
  flex-shrink: 0;
  width: 120px;
  border-color: #ff6b9d;
  color: #ff6b9d;
}

.send-code-btn:hover {
  border-color: #ff4d7d;
  color: #ff4d7d;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.footer-text {
  display: block;
  margin-bottom: 1rem;
}

.footer-links {
  margin-top: 0.5rem;
}

.info-card {
  flex: 1;
  max-width: 300px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.feature-list li {
  padding: 0.5rem 0;
  color: #595959;
  font-size: 14px;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    padding: 1rem;
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .info-card {
    max-width: 100%;
    order: -1;
  }
  
  .login-title {
    font-size: 1.5rem !important;
  }
  
  .verification-code {
    flex-direction: column;
  }
  
  .send-code-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .login-content {
    padding: 0.5rem;
  }
  
  .login-card .ant-card-body {
    padding: 1.5rem;
  }
  
  .login-title {
    font-size: 1.25rem !important;
  }
  
  .logo-icon {
    font-size: 2.5rem;
  }
  
  .login-form .ant-form-item,
  .register-form .ant-form-item {
    margin-bottom: 1rem;
  }
}

/* 表单验证样式 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-password:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 输入框聚焦样式 */
.ant-input:focus,
.ant-input-password:focus {
  border-color: #ff6b9d;
  box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

/* 标签页样式 */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ff6b9d;
}

.ant-tabs-ink-bar {
  background: #ff6b9d;
}

/* 复选框样式 */
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #ff6b9d;
  border-color: #ff6b9d;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #ff6b9d;
}
