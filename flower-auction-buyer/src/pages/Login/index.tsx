import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Tabs, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, PhoneOutlined, MailOutlined, SafetyCertificateOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { RootState, AppDispatch } from '../../store';
import { loginAsync, registerAsync, sendVerificationCodeAsync, clearError } from '../../store/slices/authSlice';
import { LoginRequest, RegisterRequest } from '../../types';
import './index.css';

const { Title, Text, Link } = Typography;

const Login: React.FC = () => {
  const [loginForm] = Form.useForm();
  const [registerForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('login');
  const [countdown, setCountdown] = useState(0);
  
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { loading, registerLoading, codeLoading, error, isAuthenticated } = useSelector(
    (state: RootState) => state.auth
  );

  useEffect(() => {
    // 如果已经登录，直接跳转到主页
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  useEffect(() => {
    // 清除之前的错误信息
    dispatch(clearError());
  }, [dispatch, activeTab]);

  useEffect(() => {
    // 显示错误信息
    if (error) {
      message.error(error);
    }
  }, [error]);

  useEffect(() => {
    // 验证码倒计时
    let timer: number;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleLogin = async (values: LoginRequest) => {
    try {
      const result = await dispatch(loginAsync(values));
      if (loginAsync.fulfilled.match(result)) {
        message.success('登录成功');
        const from = (location.state as any)?.from?.pathname || '/';
        navigate(from, { replace: true });
      }
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const handleRegister = async (values: RegisterRequest) => {
    try {
      const result = await dispatch(registerAsync(values));
      if (registerAsync.fulfilled.match(result)) {
        message.success('注册成功，请登录');
        setActiveTab('login');
        registerForm.resetFields();
      }
    } catch (err) {
      console.error('Register failed:', err);
    }
  };

  const handleSendCode = async () => {
    try {
      const phone = registerForm.getFieldValue('phone');
      if (!phone) {
        message.error('请先输入手机号');
        return;
      }
      
      const result = await dispatch(sendVerificationCodeAsync(phone));
      if (sendVerificationCodeAsync.fulfilled.match(result)) {
        message.success('验证码发送成功');
        setCountdown(60);
      }
    } catch (err) {
      console.error('Send code failed:', err);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <SafetyCertificateOutlined className="logo-icon" />
            </div>
            <Title level={2} className="login-title">
              昆明花卉拍卖系统
            </Title>
            <Text className="login-subtitle">购买商平台</Text>
          </div>

          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            className="login-tabs"
            items={[
              {
                key: 'login',
                label: '登录',
                children: (
              <Form
                form={loginForm}
                name="login"
                className="login-form"
                onFinish={handleLogin}
                autoComplete="off"
                size="large"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名/手机号' },
                    { min: 3, message: '用户名至少3位' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="请输入用户名/手机号"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6位' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请输入密码"
                    autoComplete="current-password"
                  />
                </Form.Item>

                <Form.Item>
                  <div className="login-options">
                    <Checkbox>记住我</Checkbox>
                    <Link className="forgot-password">忘记密码？</Link>
                  </div>
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    className="login-button"
                    loading={loading}
                    block
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Form.Item>
              </Form>
                )
              },
              {
                key: 'register',
                label: '注册',
                children: (
              <Form
                form={registerForm}
                name="register"
                className="register-form"
                onFinish={handleRegister}
                autoComplete="off"
                size="large"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名至少3位' },
                    { max: 20, message: '用户名最多20位' },
                    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="请输入用户名"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="realName"
                  rules={[
                    { required: true, message: '请输入真实姓名' },
                    { min: 2, message: '姓名至少2位' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="请输入真实姓名"
                  />
                </Form.Item>

                <Form.Item
                  name="phone"
                  rules={[
                    { required: true, message: '请输入手机号' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                  ]}
                >
                  <Input
                    prefix={<PhoneOutlined />}
                    placeholder="请输入手机号"
                  />
                </Form.Item>

                <Form.Item
                  name="email"
                  rules={[
                    { type: 'email', message: '请输入正确的邮箱地址' }
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="请输入邮箱（可选）"
                  />
                </Form.Item>

                <Form.Item
                  name="companyName"
                >
                  <Input
                    placeholder="请输入公司名称（可选）"
                  />
                </Form.Item>

                <Form.Item
                  name="verificationCode"
                  rules={[
                    { required: true, message: '请输入验证码' },
                    { len: 6, message: '验证码为6位数字' }
                  ]}
                >
                  <div className="verification-code">
                    <Input
                      placeholder="请输入验证码"
                      maxLength={6}
                    />
                    <Button
                      onClick={handleSendCode}
                      loading={codeLoading}
                      disabled={countdown > 0}
                      className="send-code-btn"
                    >
                      {countdown > 0 ? `${countdown}s` : '发送验证码'}
                    </Button>
                  </div>
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6位' },
                    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请输入密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请确认密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    className="register-button"
                    loading={registerLoading}
                    block
                  >
                    {registerLoading ? '注册中...' : '注册'}
                  </Button>
                </Form.Item>
              </Form>
                )
              }
            ]}
          />

          <div className="login-footer">
            <Text type="secondary" className="footer-text">
              {activeTab === 'login' 
                ? '还没有账号？点击上方"注册"创建新账号' 
                : '已有账号？点击上方"登录"直接登录'
              }
            </Text>
            <div className="footer-links">
              <Text type="secondary">
                遇到问题？请联系客服
              </Text>
            </div>
          </div>
        </Card>

        <div className="login-info">
          <Card className="info-card" bordered={false}>
            <Title level={4}>平台优势</Title>
            <ul className="feature-list">
              <li>🌸 优质花卉资源</li>
              <li>⚡ 实时竞价系统</li>
              <li>💰 安全资金保障</li>
              <li>📱 多端同步支持</li>
              <li>🔔 智能价格提醒</li>
              <li>🚚 便捷物流配送</li>
            </ul>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
