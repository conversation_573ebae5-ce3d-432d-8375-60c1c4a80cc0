import React, { useEffect, useState } from 'react';
import { Layout, Row, Col, Card, Input, Select, Button, Space, Typography, Badge, Spin } from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  HeartOutlined,
  HeartFilled,
  EyeOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  FireOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState, AppDispatch } from '../../store';
import { getBatchesAsync, setQueryParams, addToWatchListAsync, removeFromWatchListAsync } from '../../store/slices/batchSlice';
import { BatchStatus } from '../../types';
import BatchCard from '../../components/BatchCard';
import FilterPanel from '../../components/FilterPanel';
import './index.css';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const Home: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [filterVisible, setFilterVisible] = useState(false);
  
  const { batches, total, loading, queryParams } = useSelector((state: RootState) => state.batch);
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // 初始加载批次数据
    dispatch(getBatchesAsync(queryParams));
  }, [dispatch, queryParams]);

  const handleSearch = (value: string) => {
    dispatch(setQueryParams({ ...queryParams, keyword: value, page: 1 }));
  };

  const handleStatusFilter = (status: BatchStatus | undefined) => {
    dispatch(setQueryParams({ ...queryParams, status, page: 1 }));
  };

  const handleSortChange = (value: string) => {
    const [sortField, sortOrder] = value.split('_');
    dispatch(setQueryParams({
      ...queryParams,
      page: 1
    }));
  };

  const handlePageChange = (page: number, size?: number) => {
    dispatch(setQueryParams({ ...queryParams, page, size: size || queryParams.size }));
  };

  const handleWatchToggle = async (batchId: number, isWatched: boolean) => {
    try {
      if (isWatched) {
        await dispatch(removeFromWatchListAsync(batchId)).unwrap();
      } else {
        await dispatch(addToWatchListAsync(batchId)).unwrap();
      }
    } catch (error) {
      console.error('Watch toggle failed:', error);
    }
  };

  const handleBatchClick = (batchId: number) => {
    navigate(`/batch/${batchId}`);
  };

  const getStatusColor = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return 'default';
      case BatchStatus.BIDDING:
        return 'processing';
      case BatchStatus.SOLD:
        return 'success';
      case BatchStatus.UNSOLD:
        return 'warning';
      case BatchStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return '待起拍';
      case BatchStatus.BIDDING:
        return '竞拍中';
      case BatchStatus.SOLD:
        return '已成交';
      case BatchStatus.UNSOLD:
        return '流拍';
      case BatchStatus.CANCELLED:
        return '已取消';
      default:
        return '未知';
    }
  };

  const statusOptions = [
    { label: '全部', value: undefined },
    { label: '待起拍', value: BatchStatus.PENDING },
    { label: '竞拍中', value: BatchStatus.BIDDING },
    { label: '已成交', value: BatchStatus.SOLD },
    { label: '流拍', value: BatchStatus.UNSOLD },
  ];

  const sortOptions = [
    { label: '最新发布', value: 'createdAt_desc' },
    { label: '即将开拍', value: 'auctionStartTime_asc' },
    { label: '价格从低到高', value: 'startPrice_asc' },
    { label: '价格从高到低', value: 'startPrice_desc' },
    { label: '关注度', value: 'watchCount_desc' },
  ];

  return (
    <div className="home-container">
      <div className="home-header">
        <div className="header-content">
          <div className="header-left">
            <Title level={3} className="page-title">
              花卉拍卖大厅
            </Title>
            <Text type="secondary">
              发现优质花卉，参与实时竞拍
            </Text>
          </div>
          
          <div className="header-stats">
            <Space size="large">
              <div className="stat-item">
                <div className="stat-number">{total}</div>
                <div className="stat-label">拍卖批次</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">
                  {batches.filter(b => b.status === BatchStatus.BIDDING).length}
                </div>
                <div className="stat-label">进行中</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">
                  {user?.balance?.toFixed(2) || '0.00'}
                </div>
                <div className="stat-label">可用余额</div>
              </div>
            </Space>
          </div>
        </div>
      </div>

      <Content className="home-content">
        <div className="search-bar">
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索商品名称、批次号、供货商..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                defaultValue={queryParams.keyword}
              />
            </Col>
            <Col>
              <Space>
                <Select
                  placeholder="状态筛选"
                  style={{ width: 120 }}
                  size="large"
                  value={queryParams.status}
                  onChange={handleStatusFilter}
                >
                  {statusOptions.map(option => (
                    <Option key={option.value || 'all'} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
                
                <Select
                  placeholder="排序方式"
                  style={{ width: 140 }}
                  size="large"
                  defaultValue="createdAt_desc"
                  onChange={handleSortChange}
                >
                  {sortOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
                
                <Button
                  icon={<FilterOutlined />}
                  size="large"
                  onClick={() => setFilterVisible(true)}
                >
                  高级筛选
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <div className="batch-grid">
          {loading ? (
            <div className="loading-container">
              <Spin size="large" />
            </div>
          ) : (
            <Row gutter={[16, 16]}>
              {batches.map(batch => (
                <Col key={batch.id} xs={24} sm={12} md={8} lg={6} xl={6}>
                  <BatchCard
                    batch={batch}
                    onWatch={handleWatchToggle}
                    onClick={handleBatchClick}
                  />
                </Col>
              ))}
            </Row>
          )}
        </div>

        {!loading && batches.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">🌸</div>
            <Title level={4}>暂无拍卖批次</Title>
            <Text type="secondary">
              {queryParams.keyword || queryParams.status !== undefined
                ? '没有找到符合条件的批次，请尝试调整搜索条件'
                : '当前没有可用的拍卖批次，请稍后再来看看'
              }
            </Text>
          </div>
        )}
      </Content>

      <FilterPanel
        visible={filterVisible}
        onClose={() => setFilterVisible(false)}
        onFilter={(filters) => {
          dispatch(setQueryParams({ ...queryParams, ...filters, page: 1 }));
          setFilterVisible(false);
        }}
      />
    </div>
  );
};

export default Home;
