import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import batchSlice from './slices/batchSlice';
import bidSlice from './slices/bidSlice';
import accountSlice from './slices/accountSlice';
import notificationSlice from './slices/notificationSlice';
import websocketSlice from './slices/websocketSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    batch: batchSlice,
    bid: bidSlice,
    account: accountSlice,
    notification: notificationSlice,
    websocket: websocketSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
