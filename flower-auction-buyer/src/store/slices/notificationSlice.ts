import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Notification, NotificationType } from '../../types';
import apiClient from '../../services/api';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
}

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
};

// 异步actions
export const getNotificationsAsync = createAsyncThunk(
  'notification/getNotifications',
  async (params: { page?: number; size?: number; isRead?: boolean }, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/notifications', { params });
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '获取通知失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取通知失败');
    }
  }
);

export const markAsReadAsync = createAsyncThunk(
  'notification/markAsRead',
  async (notificationId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.patch(`/notifications/${notificationId}/read`);
      if (response.data.success) {
        return notificationId;
      } else {
        return rejectWithValue(response.data.message || '标记已读失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '标记已读失败');
    }
  }
);

export const markAllAsReadAsync = createAsyncThunk(
  'notification/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.patch('/notifications/read-all');
      if (response.data.success) {
        return true;
      } else {
        return rejectWithValue(response.data.message || '全部标记已读失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '全部标记已读失败');
    }
  }
);

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.unshift(action.payload);
      if (!action.payload.isRead) {
        state.unreadCount += 1;
      }
    },
    removeNotification: (state, action: PayloadAction<number>) => {
      const index = state.notifications.findIndex(n => n.id === action.payload);
      if (index !== -1) {
        const notification = state.notifications[index];
        if (!notification.isRead) {
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
        state.notifications.splice(index, 1);
      }
    },
    updateUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount = action.payload;
    },
  },
  extraReducers: (builder) => {
    // 获取通知列表
    builder
      .addCase(getNotificationsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getNotificationsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload.items;
        state.unreadCount = action.payload.unreadCount || 0;
        state.error = null;
      })
      .addCase(getNotificationsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 标记已读
    builder
      .addCase(markAsReadAsync.fulfilled, (state, action) => {
        const notification = state.notifications.find(n => n.id === action.payload);
        if (notification && !notification.isRead) {
          notification.isRead = true;
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      })
      .addCase(markAsReadAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 全部标记已读
    builder
      .addCase(markAllAsReadAsync.fulfilled, (state) => {
        state.notifications.forEach(notification => {
          notification.isRead = true;
        });
        state.unreadCount = 0;
      })
      .addCase(markAllAsReadAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  addNotification,
  removeNotification,
  updateUnreadCount,
} = notificationSlice.actions;

export default notificationSlice.reducer;
