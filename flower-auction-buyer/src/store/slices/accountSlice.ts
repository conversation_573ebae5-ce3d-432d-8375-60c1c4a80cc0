import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Account, Transaction, TransactionType } from '../../types';
import apiClient from '../../services/api';

interface AccountState {
  account: Account | null;
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
}

const initialState: AccountState = {
  account: null,
  transactions: [],
  loading: false,
  error: null,
};

// 异步actions
export const getAccountAsync = createAsyncThunk(
  'account/getAccount',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/account');
      if (response.data.success) {
        return response.data.data as Account;
      } else {
        return rejectWithValue(response.data.message || '获取账户信息失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取账户信息失败');
    }
  }
);

export const getTransactionsAsync = createAsyncThunk(
  'account/getTransactions',
  async (params: { page?: number; size?: number; type?: TransactionType }, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/account/transactions', { params });
      if (response.data.success) {
        return response.data.data.items as Transaction[];
      } else {
        return rejectWithValue(response.data.message || '获取交易记录失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取交易记录失败');
    }
  }
);

export const rechargeAsync = createAsyncThunk(
  'account/recharge',
  async (amount: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/account/recharge', { amount });
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '充值失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '充值失败');
    }
  }
);

const accountSlice = createSlice({
  name: 'account',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateBalance: (state, action: PayloadAction<{ balance: number; frozenAmount: number }>) => {
      if (state.account) {
        state.account.balance = action.payload.balance;
        state.account.frozenAmount = action.payload.frozenAmount;
      }
    },
    addTransaction: (state, action: PayloadAction<Transaction>) => {
      state.transactions.unshift(action.payload);
    },
  },
  extraReducers: (builder) => {
    // 获取账户信息
    builder
      .addCase(getAccountAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAccountAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.account = action.payload;
        state.error = null;
      })
      .addCase(getAccountAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取交易记录
    builder
      .addCase(getTransactionsAsync.fulfilled, (state, action) => {
        state.transactions = action.payload;
      })
      .addCase(getTransactionsAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 充值
    builder
      .addCase(rechargeAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(rechargeAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (state.account) {
          state.account.balance = action.payload.balance;
        }
        state.error = null;
      })
      .addCase(rechargeAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, updateBalance, addTransaction } = accountSlice.actions;
export default accountSlice.reducer;
