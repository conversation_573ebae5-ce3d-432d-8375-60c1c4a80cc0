#!/bin/bash

# 花卉拍卖系统 - 全局启动脚本
# 一键启动后端服务和所有前端应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 服务配置
BACKEND_DIR="$PROJECT_ROOT/flower-auction"
MARGE_DIR="$PROJECT_ROOT/flower-auction-admin"
AUCTIONEER_DIR="$PROJECT_ROOT/flower-auction-auctioneer"
BUYER_DIR="$PROJECT_ROOT/flower-auction-buyer"
DISPLAY_DIR="$PROJECT_ROOT/flower-auction-display"

# 端口配置
BACKEND_PORT=8080
AUCTIONEER_PORT=3001
BUYER_PORT=3002
DISPLAY_PORT=3003

# PID文件目录
PID_DIR="$PROJECT_ROOT/.pids"
mkdir -p "$PID_DIR"

# 显示系统信息
show_banner() {
    echo -e "${CYAN}"
    echo "🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸"
    echo "🌸                                                      🌸"
    echo "🌸           昆明花卉拍卖系统 - 全局启动脚本              🌸"
    echo "🌸                                                      🌸"
    echo "🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸🌸"
    echo -e "${NC}"
    echo
    log_info "项目根目录: $PROJECT_ROOT"
    echo
}

# 检查环境
check_environment() {
    log_header "🔍 检查运行环境..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在PATH中"
        exit 1
    fi
    log_success "Go 环境正常: $(go version | cut -d' ' -f3)"
    
    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或不在PATH中"
        exit 1
    fi
    log_success "Node.js 环境正常: $(node --version)"
    
    # 检查npm环境
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装或不在PATH中"
        exit 1
    fi
    log_success "npm 环境正常: $(npm --version)"
    
    echo
}

# 检查端口占用
check_ports() {
    log_header "🔍 检查端口占用情况..."
    
    local ports=($BACKEND_PORT $AUCTIONEER_PORT $BUYER_PORT $DISPLAY_PORT)
    local services=("后端API" "拍卖师端" "购买商端" "投屏端")
    local occupied_ports=()
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local service=${services[$i]}
        
        if command -v lsof &> /dev/null; then
            local pid=$(lsof -ti:$port 2>/dev/null)
            if [ ! -z "$pid" ]; then
                log_warning "端口 $port ($service) 被进程 $pid 占用"
                occupied_ports+=("$port:$service:$pid")
            else
                log_success "端口 $port ($service) 可用"
            fi
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo
        log_warning "发现端口占用，是否终止占用进程并继续？"
        for port_info in "${occupied_ports[@]}"; do
            IFS=':' read -r port service pid <<< "$port_info"
            echo "  - 端口 $port ($service) 被进程 $pid 占用"
        done
        echo
        read -p "是否终止所有占用进程并继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for port_info in "${occupied_ports[@]}"; do
                IFS=':' read -r port service pid <<< "$port_info"
                log_info "终止进程 $pid (端口 $port)..."
                kill -9 $pid 2>/dev/null || true
            done
            sleep 2
            log_success "所有占用进程已终止"
        else
            log_error "启动取消"
            exit 1
        fi
    fi
    
    echo
}

# 启动后端服务
start_backend() {
    log_header "🚀 启动后端服务..."
    
    if [ ! -d "$BACKEND_DIR" ]; then
        log_error "后端目录不存在: $BACKEND_DIR"
        exit 1
    fi
    
    cd "$BACKEND_DIR"
    
    # 检查是否有启动脚本
    if [ -f "run.sh" ]; then
        log_info "使用启动脚本启动后端服务..."
        nohup ./run.sh > "$PID_DIR/backend.log" 2>&1 &
        echo $! > "$PID_DIR/backend.pid"
    else
        log_info "直接启动后端服务..."
        nohup go run main.go > "$PID_DIR/backend.log" 2>&1 &
        echo $! > "$PID_DIR/backend.pid"
    fi
    
    log_success "后端服务启动中... (PID: $(cat "$PID_DIR/backend.pid"))"
    log_info "访问地址: http://localhost:$BACKEND_PORT"
    log_info "日志文件: $PID_DIR/backend.log"
    
    # 等待后端服务启动
    log_info "等待后端服务启动..."
    sleep 5
    
    cd "$PROJECT_ROOT"
    echo
}

# 启动前端服务
start_frontend() {
    local service_name=$1
    local service_dir=$2
    local service_port=$3
    local display_name=$4
    
    log_header "🚀 启动 $display_name..."
    
    if [ ! -d "$service_dir" ]; then
        log_error "$display_name 目录不存在: $service_dir"
        return 1
    fi
    
    cd "$service_dir"
    
    # 检查是否有启动脚本
    if [ -f "run.sh" ]; then
        log_info "使用启动脚本启动 $display_name..."
        export PORT=$service_port
        nohup ./run.sh > "$PID_DIR/${service_name}.log" 2>&1 &
        echo $! > "$PID_DIR/${service_name}.pid"
    else
        log_info "使用npm启动 $display_name..."
        # 安装依赖
        if [ ! -d "node_modules" ]; then
            log_info "安装依赖..."
            npm install
        fi
        
        export PORT=$service_port
        nohup npm run dev > "$PID_DIR/${service_name}.log" 2>&1 &
        echo $! > "$PID_DIR/${service_name}.pid"
    fi
    
    log_success "$display_name 启动中... (PID: $(cat "$PID_DIR/${service_name}.pid"))"
    log_info "访问地址: http://localhost:$service_port"
    log_info "日志文件: $PID_DIR/${service_name}.log"
    
    cd "$PROJECT_ROOT"
    echo
}

# 显示服务状态
show_status() {
    log_header "📊 服务状态总览"
    
    echo "┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐"
    echo "│     服务        │      端口       │      状态       │      访问地址    │"
    echo "├─────────────────┼─────────────────┼─────────────────┼─────────────────┤"
    
    # 检查各服务状态
    local services=("backend:后端API:$BACKEND_PORT" "auctioneer:拍卖师端:$AUCTIONEER_PORT" "buyer:购买商端:$BUYER_PORT" "display:投屏端:$DISPLAY_PORT")
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name display_name port <<< "$service_info"
        local pid_file="$PID_DIR/${service_name}.pid"
        local status="❌ 未运行"
        local url="N/A"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                status="✅ 运行中"
                url="http://localhost:$port"
            else
                status="❌ 已停止"
            fi
        fi
        
        printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" "$display_name" "$port" "$status" "$url"
    done
    
    echo "└─────────────────┴─────────────────┴─────────────────┴─────────────────┘"
    echo
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统全局启动脚本"
    echo
    echo "用法:"
    echo "  $0 [命令]"
    echo
    echo "命令:"
    echo "  start    启动所有服务 (默认)"
    echo "  stop     停止所有服务"
    echo "  restart  重启所有服务"
    echo "  status   显示服务状态"
    echo "  logs     显示服务日志"
    echo "  help     显示帮助信息"
    echo
    echo "服务端口:"
    echo "  后端API:   $BACKEND_PORT"
    echo "  拍卖师端:  $AUCTIONEER_PORT"
    echo "  购买商端:  $BUYER_PORT"
    echo "  投屏端:    $DISPLAY_PORT"
}

# 停止所有服务
stop_services() {
    log_header "🛑 停止所有服务..."
    
    local services=("backend" "auctioneer" "buyer" "display")
    local service_names=("后端API" "拍卖师端" "购买商端" "投屏端")
    
    for i in "${!services[@]}"; do
        local service=${services[$i]}
        local service_name=${service_names[$i]}
        local pid_file="$PID_DIR/${service}.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                log_info "停止 $service_name (PID: $pid)..."
                kill $pid 2>/dev/null || true
                
                # 等待进程结束
                local count=0
                while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                    sleep 1
                    count=$((count + 1))
                done
                
                # 如果进程仍在运行，强制杀死
                if ps -p $pid > /dev/null 2>&1; then
                    log_warning "强制停止 $service_name..."
                    kill -9 $pid 2>/dev/null || true
                fi
                
                log_success "$service_name 已停止"
            else
                log_warning "$service_name 进程不存在"
            fi
            
            rm -f "$pid_file"
        else
            log_warning "$service_name PID文件不存在"
        fi
    done
    
    # 清理日志文件
    rm -f "$PID_DIR"/*.log
    
    log_success "所有服务已停止"
}

# 显示日志
show_logs() {
    log_header "📋 服务日志"
    
    local services=("backend:后端API" "auctioneer:拍卖师端" "buyer:购买商端" "display:投屏端")
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name display_name <<< "$service_info"
        local log_file="$PID_DIR/${service_name}.log"
        
        if [ -f "$log_file" ]; then
            echo -e "${CYAN}=== $display_name 日志 ===${NC}"
            tail -n 20 "$log_file"
            echo
        else
            log_warning "$display_name 日志文件不存在"
        fi
    done
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        "start")
            show_banner
            check_environment
            check_ports
            start_backend
            start_frontend "auctioneer" "$AUCTIONEER_DIR" "$AUCTIONEER_PORT" "拍卖师端"
            start_frontend "buyer" "$BUYER_DIR" "$BUYER_PORT" "购买商端"
            start_frontend "display" "$DISPLAY_DIR" "$DISPLAY_PORT" "投屏端"
            
            # 等待所有服务启动
            log_info "等待所有服务完全启动..."
            sleep 10
            
            show_status
            
            log_success "🎉 所有服务启动完成！"
            log_info "💡 使用 '$0 stop' 停止所有服务"
            log_info "💡 使用 '$0 status' 查看服务状态"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            main start
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
