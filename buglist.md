#  背景
- 首先请先理解工程结构,这是个昆明花卉拍卖系统,有前后端系统.原始需求文档: sell_flower.md, 请严格按照docs 目录下架构设计文档执行.前端目录: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/ ,后端目录: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/flower-auction/
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 前端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/run.sh
- 原始需求: sell_flower.md
- 技术架构文档: docs目录下

# buglist

## 拍卖管理
- [] 拍卖商品-拍卖商品列表页没显示数据.请分析原因,估计是前后端数据结构不一致
- [] 竞价记录页,搜索功能:请添加多条件搜索
- [] 实时竞价页,请完善前后端逻辑,页面使用真实数据

## 订单管理
- [] 订单列表页会循环调用refresh等接口.这bug 很严重,请解决并实现订单列表页前后端逻辑
- [] 物流管理:请实现物流管理页前后端逻辑

## 财务系统
- [] 账号管理.请实现账号管理页前后端逻辑
- [] 交易记录,请实现相关前后端逻辑
- [] 财务报表.请实现相关前后端逻辑

## 系统设置
- [] 系统配置,请实现相关前后端逻辑
- [] 安全设置,请实现相关前后端逻辑
- [] 通知设置,请实现相关前后端逻辑
- [] 系统维护,请实现相关前后端逻辑
- [] 性能监控,请实现相关前后端逻辑

## 报表中心
- [] 销售报表,请实现相关前后端逻辑
- [] 用户报表,请实现相关前后端逻辑
- [] 商品报表,请实现相关前后端逻辑
- [] 拍卖报表,请实现相关前后端逻辑

## 帮助中心
- [] 使用文档,请实现相关前后端逻辑
- [] 常见问题,请实现相关前后端逻辑
- [] 联系我们,请实现相关前后端逻辑

## 验证
- 修复后从前后端角度验证bug是否解决.
