import React from 'react';
import { Card, List, Typography, Badge } from 'antd';

const { Text } = Typography;

const RealTimeData: React.FC = () => {
  const mockData = [
    { id: 1, message: '用户张三出价 ¥150', time: '10:30:25', type: 'bid' },
    { id: 2, message: '批次001开始拍卖', time: '10:30:20', type: 'start' },
    { id: 3, message: '用户李四出价 ¥140', time: '10:30:15', type: 'bid' },
  ];

  return (
    <Card title="实时动态" size="small">
      <List
        size="small"
        dataSource={mockData}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '12px' }}>{item.message}</Text>
                <Badge status={item.type === 'bid' ? 'success' : 'processing'} />
              </div>
              <Text type="secondary" style={{ fontSize: '10px' }}>
                {item.time}
              </Text>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default RealTimeData;
