.clock-panel {
  height: 100%;
}

.panel-header {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.panel-header .ant-card-body {
  padding: 20px 24px;
}

.clock-icon {
  font-size: 2rem;
  color: white;
  margin-right: 12px;
}

.clock-title {
  color: white !important;
  margin: 0 !important;
  margin-bottom: 4px !important;
}

.status-tag {
  font-weight: 500;
  border: none;
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

.panel-header .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.panel-header .ant-statistic-content {
  color: white;
}

.batch-info {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.batch-info .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.batch-info .ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
}

.auction-progress {
  margin-top: 20px;
  padding: 16px;
  background: #f6ffed;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
}

.progress-container {
  padding-left: 16px;
}

.progress-container .ant-progress {
  margin-top: 8px;
}

.no-batch {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-batch-content {
  text-align: center;
}

.no-batch-icon {
  font-size: 3rem;
  color: #d9d9d9;
  margin-bottom: 16px;
  display: block;
}

.no-clock {
  text-align: center;
  padding: 40px;
}

.no-clock-icon {
  font-size: 4rem;
  color: #d9d9d9;
  margin-bottom: 16px;
  display: block;
}

.real-time-stats {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card {
  text-align: center;
  border: none;
  box-shadow: none;
  background: #fafafa;
  border-radius: 6px;
}

.stat-card .ant-card-body {
  padding: 16px 12px;
}

.stat-card .ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
}

.stat-card .ant-statistic-content {
  font-size: 18px;
  font-weight: 600;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel-header,
.batch-info,
.real-time-stats {
  animation: slideInUp 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-header .ant-row {
    flex-direction: column;
    text-align: center;
  }
  
  .panel-header .ant-col:first-child {
    margin-bottom: 16px;
  }
  
  .batch-info .ant-row {
    flex-direction: column;
  }
  
  .batch-info .ant-col {
    margin-bottom: 16px;
  }
  
  .auction-progress .ant-row {
    flex-direction: column;
    text-align: center;
  }
  
  .auction-progress .ant-col:first-child {
    margin-bottom: 16px;
  }
  
  .progress-container {
    padding-left: 0;
  }
  
  .real-time-stats .ant-row {
    flex-direction: column;
  }
  
  .real-time-stats .ant-col {
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  .clock-icon {
    font-size: 1.5rem;
  }
  
  .clock-title {
    font-size: 1.25rem !important;
  }
  
  .panel-header .ant-card-body {
    padding: 16px;
  }
  
  .batch-info .ant-descriptions {
    font-size: 12px;
  }
  
  .stat-card .ant-statistic-content {
    font-size: 16px;
  }
}

/* 实时数据闪烁效果 */
.stat-card:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 价格高亮效果 */
.batch-info .ant-descriptions-item-content:has(> [style*="color: rgb(245, 34, 45)"]) {
  animation: priceHighlight 2s ease-in-out infinite;
}

@keyframes priceHighlight {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(245, 34, 45, 0.1);
  }
}

/* 进度条动画 */
.ant-progress-bg {
  transition: all 0.3s ease;
}

/* 倒计时样式 */
.ant-statistic-content-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
