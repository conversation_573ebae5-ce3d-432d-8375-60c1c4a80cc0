.layout-container {
  min-height: 100vh;
}

.layout-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.logo-text {
  overflow: hidden;
}

.logo-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

.logo-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1;
}

.layout-menu {
  flex: 1;
  border-right: none;
}

.layout-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.layout-menu .ant-menu-item-selected {
  background: #1890ff !important;
}

.layout-menu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.layout-main {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.layout-main.collapsed {
  margin-left: 80px;
}

.layout-header {
  background: white;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-button {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s;
}

.collapse-button:hover {
  background: #f0f0f0;
}

.header-right {
  display: flex;
  align-items: center;
}

.welcome-text {
  color: #595959;
  margin-right: 16px;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-avatar:hover {
  background: #f0f0f0;
}

.avatar {
  margin-right: 8px;
  background: #1890ff;
}

.username {
  color: #262626;
  font-weight: 500;
}

.layout-content {
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sider.open {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-header {
    padding: 0 16px;
  }
  
  .welcome-text {
    display: none;
  }
  
  .username {
    display: none;
  }
}

@media (max-width: 576px) {
  .layout-header {
    padding: 0 12px;
  }
  
  .user-avatar {
    padding: 8px;
  }
  
  .logo {
    padding: 0 12px;
  }
  
  .layout-menu .ant-menu-item {
    margin: 4px;
  }
}

/* 动画效果 */
.layout-sider {
  transition: all 0.2s;
}

.layout-main {
  transition: all 0.2s;
}

/* 当侧边栏收起时的样式调整 */
.ant-layout-sider-collapsed .logo-text {
  display: none;
}

.ant-layout-sider-collapsed .logo {
  justify-content: center;
}

.ant-layout-sider-collapsed .layout-menu .ant-menu-item {
  margin: 4px;
  text-align: center;
}

/* 菜单项图标样式 */
.layout-menu .ant-menu-item .anticon {
  font-size: 16px;
}

/* 用户下拉菜单样式 */
.ant-dropdown-menu-item {
  display: flex;
  align-items: center;
}

.ant-dropdown-menu-item .anticon {
  margin-right: 8px;
  font-size: 14px;
}

/* 滚动条样式 */
.layout-sider .ant-layout-sider-children::-webkit-scrollbar {
  width: 4px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
