import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Spin } from 'antd';
import { UserOutlined, LockOutlined, SafetyCertificateOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState, AppDispatch } from '../../store';
import { loginAsync, clearError } from '../../store/slices/authSlice';
import { LoginRequest } from '../../types';
import './index.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // 如果已经登录，直接跳转到主页
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    // 清除之前的错误信息
    dispatch(clearError());
  }, [dispatch]);

  useEffect(() => {
    // 显示错误信息
    if (error) {
      message.error(error);
    }
  }, [error]);

  const handleSubmit = async (values: LoginRequest) => {
    try {
      const result = await dispatch(loginAsync(values));
      if (loginAsync.fulfilled.match(result)) {
        message.success('登录成功');
        navigate('/dashboard');
      }
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const handleFingerprint = () => {
    message.info('指纹识别功能开发中...');
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <SafetyCertificateOutlined className="logo-icon" />
            </div>
            <Title level={2} className="login-title">
              昆明花卉拍卖系统
            </Title>
            <Text className="login-subtitle">拍卖师工作台</Text>
          </div>

          <Form
            form={form}
            name="login"
            className="login-form"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入工号' },
                { min: 3, message: '工号至少3位' },
                { max: 20, message: '工号最多20位' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入工号"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="login-button"
                loading={loading}
                block
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>

            <Form.Item>
              <Button
                type="default"
                className="fingerprint-button"
                onClick={handleFingerprint}
                block
                icon={<SafetyCertificateOutlined />}
              >
                指纹识别登录
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Text type="secondary" className="footer-text">
              请使用您的拍卖师工号和密码登录系统
            </Text>
            <div className="footer-links">
              <Text type="secondary">
                遇到问题？请联系系统管理员
              </Text>
            </div>
          </div>
        </Card>

        <div className="login-info">
          <Card className="info-card" bordered={false}>
            <Title level={4}>系统特性</Title>
            <ul className="feature-list">
              <li>🔒 安全的身份验证</li>
              <li>📱 多端同步支持</li>
              <li>⚡ 实时拍卖控制</li>
              <li>📊 数据统计分析</li>
              <li>🔄 自动状态同步</li>
            </ul>
          </Card>
        </div>
      </div>

      {loading && (
        <div className="loading-overlay">
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default Login;
