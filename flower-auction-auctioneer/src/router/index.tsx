import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import AuthGuard from '../components/AuthGuard';
import Layout from '../components/Layout';
import Login from '../pages/Login';
import Dashboard from '../pages/Dashboard';

// 404页面组件
const NotFound: React.FC = () => (
  <Result
    status="404"
    title="404"
    subTitle="抱歉，您访问的页面不存在。"
    extra={
      <Button type="primary" onClick={() => window.location.href = '/dashboard'}>
        返回首页
      </Button>
    }
  />
);

// 403页面组件
const Forbidden: React.FC = () => (
  <Result
    status="403"
    title="403"
    subTitle="抱歉，您没有权限访问此页面。"
    extra={
      <Button type="primary" onClick={() => window.location.href = '/dashboard'}>
        返回首页
      </Button>
    }
  />
);

// 创建路由配置
export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },
  {
    path: '/dashboard',
    element: (
      <AuthGuard>
        <Layout>
          <Dashboard />
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/batches',
    element: (
      <AuthGuard>
        <Layout>
          <div>批次管理页面 - 开发中</div>
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/auction-control',
    element: (
      <AuthGuard>
        <Layout>
          <div>拍卖控制页面 - 开发中</div>
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/statistics',
    element: (
      <AuthGuard>
        <Layout>
          <div>统计分析页面 - 开发中</div>
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/settings',
    element: (
      <AuthGuard>
        <Layout>
          <div>系统设置页面 - 开发中</div>
        </Layout>
      </AuthGuard>
    ),
  },
  {
    path: '/403',
    element: <Forbidden />,
  },
  {
    path: '*',
    element: <NotFound />,
  },
]);

export default router;
