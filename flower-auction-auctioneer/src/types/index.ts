// 拍卖师端类型定义

// 用户相关类型
export interface User {
  id: number;
  username: string;
  realName: string;
  userType: number;
  status: number;
  roles: Role[];
  clockNumbers?: number[]; // 负责的钟号范围
}

export interface Role {
  id: number;
  name: string;
  description: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: number;
}

// 批次相关类型
export interface Batch {
  id: number;
  batchNumber: string;
  productName: string;
  categoryId: number;
  categoryName: string;
  quantity: number;
  unit: string;
  qualityLevel: number;
  startPrice: number;
  currentPrice?: number;
  status: BatchStatus;
  clockNumber: number;
  supplierId: number;
  supplierName: string;
  warehouseLocation: string;
  qualityReport?: QualityReport;
  createdAt: string;
  updatedAt: string;
  auctionStartTime?: string;
  auctionEndTime?: string;
}

export enum BatchStatus {
  PENDING = 0,      // 待起拍
  BIDDING = 1,      // 竞拍中
  SOLD = 2,         // 已成交
  UNSOLD = 3,       // 流拍
  CANCELLED = 4     // 已取消
}

export interface QualityReport {
  id: number;
  batchId: number;
  inspector: string;
  qualityLevel: number;
  defects: string[];
  notes: string;
  images: string[];
  createdAt: string;
}

// 拍卖控制相关类型
export interface AuctionParams {
  batchId: number;
  clockNumber: number;
  startPrice: number;
  priceStep: number;
  speed: number; // 转速 (秒/圈)
  minQuantity: number;
  maxDuration: number; // 最大拍卖时长(分钟)
}

export interface AuctionControl {
  action: 'start' | 'pause' | 'resume' | 'stop' | 'unsold' | 'adjust_price';
  batchId: number;
  clockNumber: number;
  params?: Partial<AuctionParams>;
  reason?: string;
}

// 竞价相关类型
export interface Bid {
  id: number;
  batchId: number;
  userId: number;
  username: string;
  price: number;
  quantity: number;
  timestamp: string;
  status: number;
}

export interface BidStatistics {
  totalBids: number;
  uniqueBidders: number;
  highestBid: number;
  averageBid: number;
  bidHistory: Bid[];
}

// 钟号相关类型
export interface ClockInfo {
  clockNumber: number;
  status: ClockStatus;
  currentBatch?: Batch;
  onlineBuyers: number;
  totalBids: number;
  currentPrice?: number;
  timeRemaining?: number;
}

export enum ClockStatus {
  IDLE = 0,         // 空闲
  PREPARING = 1,    // 准备中
  BIDDING = 2,      // 竞拍中
  PAUSED = 3,       // 暂停
  COMPLETED = 4     // 已完成
}

// 统计相关类型
export interface AuctionStatistics {
  totalBatches: number;
  completedBatches: number;
  unsoldBatches: number;
  totalRevenue: number;
  averagePrice: number;
  successRate: number;
  todayStats: {
    batches: number;
    revenue: number;
    buyers: number;
  };
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'bid' | 'auction_start' | 'auction_end' | 'price_update' | 'status_update';
  data: any;
  timestamp: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  size: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

// 查询参数类型
export interface BatchQueryParams extends PaginationParams {
  status?: BatchStatus;
  clockNumber?: number;
  categoryId?: number;
  supplierId?: number;
  startDate?: string;
  endDate?: string;
  keyword?: string;
}

// 表单类型
export interface BatchCreateForm {
  productName: string;
  categoryId: number;
  quantity: number;
  unit: string;
  qualityLevel: number;
  startPrice: number;
  clockNumber: number;
  supplierId: number;
  warehouseLocation: string;
  notes?: string;
}

export interface AuctionParamsForm {
  startPrice: number;
  priceStep: number;
  speed: number;
  minQuantity: number;
  maxDuration: number;
}
