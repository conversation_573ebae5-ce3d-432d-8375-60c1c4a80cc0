"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CloudTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CloudTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CloudTwoTone = function CloudTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CloudTwoTone.default
  }));
};

/**![cloud](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc5MS45IDQ5MmwtMzcuOC0xMC0xMy44LTM2LjVjLTguNi0yMi43LTIwLjYtNDQuMS0zNS43LTYzLjRhMjQ1LjczIDI0NS43MyAwIDAwLTUyLjQtNDkuOWMtNDEuMS0yOC45LTg5LjUtNDQuMi0xNDAtNDQuMnMtOTguOSAxNS4zLTE0MCA0NC4yYTI0NS42IDI0NS42IDAgMDAtNTIuNCA0OS45IDI0MC40NyAyNDAuNDcgMCAwMC0zNS43IDYzLjRsLTEzLjkgMzYuNi0zNy45IDkuOWExMjUuNyAxMjUuNyAwIDAwLTY2LjEgNDMuN0ExMjMuMSAxMjMuMSAwIDAwMTQwIDYxMmMwIDMzLjEgMTIuOSA2NC4zIDM2LjMgODcuNyAyMy40IDIzLjQgNTQuNSAzNi4zIDg3LjYgMzYuM2g0OTYuMmMzMy4xIDAgNjQuMi0xMi45IDg3LjYtMzYuM0ExMjMuMyAxMjMuMyAwIDAwODg0IDYxMmMwLTU2LjItMzcuOC0xMDUuNS05Mi4xLTEyMHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTgxMS40IDQxOC43Qzc2NS42IDI5Ny45IDY0OC45IDIxMiA1MTIuMiAyMTJTMjU4LjggMjk3LjggMjEzIDQxOC42QzEyNy4zIDQ0MS4xIDY0IDUxOS4xIDY0IDYxMmMwIDExMC41IDg5LjUgMjAwIDE5OS45IDIwMGg0OTYuMkM4NzAuNSA4MTIgOTYwIDcyMi41IDk2MCA2MTJjMC05Mi43LTYzLjEtMTcwLjctMTQ4LjYtMTkzLjN6bTM2LjMgMjgxYTEyMy4wNyAxMjMuMDcgMCAwMS04Ny42IDM2LjNIMjYzLjljLTMzLjEgMC02NC4yLTEyLjktODcuNi0zNi4zQTEyMy4zIDEyMy4zIDAgMDExNDAgNjEyYzAtMjggOS4xLTU0LjMgMjYuMi03Ni4zYTEyNS43IDEyNS43IDAgMDE2Ni4xLTQzLjdsMzcuOS05LjkgMTMuOS0zNi42YzguNi0yMi44IDIwLjYtNDQuMSAzNS43LTYzLjRhMjQ1LjYgMjQ1LjYgMCAwMTUyLjQtNDkuOWM0MS4xLTI4LjkgODkuNS00NC4yIDE0MC00NC4yczk4LjkgMTUuMyAxNDAgNDQuMmMxOS45IDE0IDM3LjUgMzAuOCA1Mi40IDQ5LjkgMTUuMSAxOS4zIDI3LjEgNDAuNyAzNS43IDYzLjRsMTMuOCAzNi41IDM3LjggMTBjNTQuMyAxNC41IDkyLjEgNjMuOCA5Mi4xIDEyMCAwIDMzLjEtMTIuOSA2NC4zLTM2LjMgODcuN3oiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloudTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloudTwoTone';
}
var _default = exports.default = RefIcon;