import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CoffeeOutlinedSvg from "@ant-design/icons-svg/es/asn/CoffeeOutlined";
import AntdIcon from "../components/AntdIcon";
var CoffeeOutlined = function CoffeeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CoffeeOutlinedSvg
  }));
};

/**![coffee](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI3NSAyODFjMTkuOSAwIDM2LTE2LjEgMzYtMzZWMzZjMC0xOS45LTE2LjEtMzYtMzYtMzZzLTM2IDE2LjEtMzYgMzZ2MjA5YzAgMTkuOSAxNi4xIDM2IDM2IDM2em02MTMgMTQ0SDc2OGMwLTM5LjgtMzIuMi03Mi03Mi03MkgyMDBjLTM5LjggMC03MiAzMi4yLTcyIDcydjI0OGMwIDMuNC4yIDYuNy43IDkuOS0uNSA3LS43IDE0LS43IDIxLjEgMCAxNzYuNyAxNDMuMyAzMjAgMzIwIDMyMCAxNjAuMSAwIDI5Mi43LTExNy41IDMxNi4zLTI3MUg4ODhjMzkuOCAwIDcyLTMyLjIgNzItNzJWNDk3YzAtMzkuOC0zMi4yLTcyLTcyLTcyek02OTYgNjgxaC0xLjFjLjcgNy42IDEuMSAxNS4yIDEuMSAyMyAwIDEzNy0xMTEgMjQ4LTI0OCAyNDhTMjAwIDg0MSAyMDAgNzA0YzAtNy44LjQtMTUuNCAxLjEtMjNIMjAwVjQyNWg0OTZ2MjU2em0xOTItOEg3NzZWNDk3aDExMnYxNzZ6TTYxMyAyODFjMTkuOSAwIDM2LTE2LjEgMzYtMzZWMzZjMC0xOS45LTE2LjEtMzYtMzYtMzZzLTM2IDE2LjEtMzYgMzZ2MjA5YzAgMTkuOSAxNi4xIDM2IDM2IDM2em0tMTcwIDBjMTkuOSAwIDM2LTE2LjEgMzYtMzZWMzZjMC0xOS45LTE2LjEtMzYtMzYtMzZzLTM2IDE2LjEtMzYgMzZ2MjA5YzAgMTkuOSAxNi4xIDM2IDM2IDM2eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CoffeeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CoffeeOutlined';
}
export default RefIcon;