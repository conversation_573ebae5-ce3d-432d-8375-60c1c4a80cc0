function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CodepenOutlinedSvg from "@ant-design/icons-svg/es/asn/CodepenOutlined";
import AntdIcon from "../components/AntdIcon";
const CodepenOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: CodepenOutlinedSvg
}));

/**![codepen](data:image/svg+xml;base64,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) */
const RefIcon = /*#__PURE__*/React.forwardRef(CodepenOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodepenOutlined';
}
export default RefIcon;