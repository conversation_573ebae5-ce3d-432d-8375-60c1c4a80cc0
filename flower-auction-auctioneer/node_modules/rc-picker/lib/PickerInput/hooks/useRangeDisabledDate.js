"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useRangeDisabledDate;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _dateUtil = require("../../utils/dateUtil");
var _miscUtil = require("../../utils/miscUtil");
/**
 * RangePicker need additional logic to handle the `disabled` case. e.g.
 * [disabled, enabled] should end date not before start date
 */
function useRangeDisabledDate(values, disabled, activeIndexList, generateConfig, locale, disabledDate) {
  var activeIndex = activeIndexList[activeIndexList.length - 1];
  var rangeDisabledDate = function rangeDisabledDate(date, info) {
    var _values = (0, _slicedToArray2.default)(values, 2),
      start = _values[0],
      end = _values[1];
    var mergedInfo = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, info), {}, {
      from: (0, _miscUtil.getFromDate)(values, activeIndexList)
    });

    // ============================ Disabled ============================
    // Should not select days before the start date
    if (activeIndex === 1 && disabled[0] && start &&
    // Same date isOK
    !(0, _dateUtil.isSame)(generateConfig, locale, start, date, mergedInfo.type) &&
    // Before start date
    generateConfig.isAfter(start, date)) {
      return true;
    }

    // Should not select days after the end date
    if (activeIndex === 0 && disabled[1] && end &&
    // Same date isOK
    !(0, _dateUtil.isSame)(generateConfig, locale, end, date, mergedInfo.type) &&
    // After end date
    generateConfig.isAfter(date, end)) {
      return true;
    }

    // ============================= Origin =============================
    return disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate(date, mergedInfo);
  };
  return rangeDisabledDate;
}